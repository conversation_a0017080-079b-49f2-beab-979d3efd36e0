# 前端运行爬虫修复需求规格

## 项目背景

拼多多爬虫系统目前存在前端运行和后端运行的差异问题：
- 后端运行（通过 `run_main.py`）正常稳定
- 前端运行（通过Web界面调用API）存在问题
- 需要统一前后端的运行方式和数据格式

## 功能需求

### FR-001: API数据格式统一
**作为** 前端开发者  
**我希望** API调用的数据格式与后端期望格式完全一致  
**以便** 前端能够成功启动爬虫任务  

**验收标准：**
- 前端发送的请求数据格式必须匹配后端的 `CrawlRequest` 模型
- `keywords` 字段必须为字符串数组格式，即使只有一个关键词
- 所有参数名称必须与后端模型定义完全一致
- API调用成功率达到100%（在有效参数下）

### FR-002: 参数名称标准化
**作为** 系统维护者  
**我希望** 前后端使用统一的参数命名规范  
**以便** 减少因参数名不一致导致的错误  

**验收标准：**
- 前端发送参数名与后端接收参数名完全一致
- 驼峰命名法在前后端保持一致
- 参数类型转换正确（字符串、数字、布尔值）
- 可选参数具有合理的默认值

### FR-003: 错误处理增强
**作为** 用户  
**我希望** 在前端操作出现错误时能看到明确的错误信息  
**以便** 了解问题原因并采取相应措施  

**验收标准：**
- API错误响应包含详细的错误描述
- 前端能够正确显示后端返回的错误信息
- 网络错误和业务逻辑错误分别处理
- 错误信息对用户友好且具有指导性

### FR-004: WebSocket连接稳定性
**作为** 用户  
**我希望** 前端能够稳定接收爬虫的实时进度和数据  
**以便** 监控爬虫执行状态  

**验收标准：**
- WebSocket连接建立成功率达到95%以上
- 连接断开后能够自动重连
- 进度更新实时性延迟不超过2秒
- 数据传输完整性得到保证

## 非功能需求

### NFR-001: 兼容性要求
- 修复后的系统必须保持向后兼容
- 直接运行方式（`run_main.py`）功能不受影响
- 现有配置文件格式保持不变

### NFR-002: 性能要求
- API响应时间不超过3秒
- 前端界面响应延迟不超过100ms
- 内存使用不超过原系统的120%

### NFR-003: 可维护性要求
- 代码结构清晰，易于理解和修改
- 添加充分的日志记录和错误跟踪
- 提供详细的API文档和使用说明

## 约束条件

### 技术约束
- 必须使用现有的技术栈（Python FastAPI + React）
- 不能破坏现有的核心爬虫逻辑
- 必须保持数据库和文件系统的兼容性

### 业务约束
- 修复过程中系统可用性不能低于80%
- 不能影响正在进行的爬虫任务
- 必须在2个工作日内完成修复

## 验收测试场景

### 测试场景1：单关键词爬取
- 前端输入单个关键词
- 点击开始爬取
- 验证任务成功启动且数据正常收集

### 测试场景2：多关键词爬取
- 前端输入多个关键词（逗号分隔）
- 点击开始爬取
- 验证多个关键词依次处理

### 测试场景3：参数配置
- 修改目标数量、排序方式等参数
- 启动爬取任务
- 验证参数正确传递到后端

### 测试场景4：错误处理
- 输入无效参数或在网络异常情况下启动爬取
- 验证错误信息正确显示
- 验证系统能够恢复正常状态

### 测试场景5：实时监控
- 启动爬取任务
- 验证进度条实时更新
- 验证数据预览功能正常

## 成功标准

1. **功能完整性**: 所有验收标准100%通过
2. **稳定性**: 连续运行24小时无严重错误
3. **用户体验**: 前端操作流畅，错误提示友好
4. **文档完整**: 提供完整的修复说明和使用文档