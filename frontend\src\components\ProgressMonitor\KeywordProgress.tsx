import React, { useState, useEffect } from 'react';
import { Card, Tag, Progress, Space, Typography } from 'antd';
import { 
  CheckCircleOutlined, 
  CloseCircleOutlined, 
  SyncOutlined 
} from '@ant-design/icons';
import type { KeywordProgressProps } from './types';

const { Text } = Typography;

const KeywordProgress: React.FC<KeywordProgressProps> = ({
  currentKeyword,
  completedKeywords,
  failedKeywords,
  totalKeywords,
  animationDuration = 500,
}) => {
  const [animatingKeyword, setAnimatingKeyword] = useState<string | null>(null);

  // 处理关键词切换动画
  useEffect(() => {
    if (currentKeyword && currentKeyword !== animatingKeyword) {
      setAnimatingKeyword(currentKeyword);
      const timer = setTimeout(() => {
        setAnimatingKeyword(null);
      }, animationDuration);
      
      return () => clearTimeout(timer);
    }
  }, [currentKeyword, animatingKeyword, animationDuration]);

  // 计算进度
  const processedCount = completedKeywords.length + failedKeywords.length;
  const progressPercent = totalKeywords > 0 
    ? Math.round((processedCount / totalKeywords) * 100) 
    : 0;

  // 获取关键词状态
  const getKeywordStatus = (keyword: string) => {
    if (completedKeywords.includes(keyword)) {
      return 'completed';
    } else if (failedKeywords.includes(keyword)) {
      return 'failed';
    } else if (keyword === currentKeyword) {
      return 'processing';
    } else {
      return 'pending';
    }
  };

  // 渲染关键词标签
  const renderKeywordTag = (keyword: string, status: string) => {
    let color: string;
    let icon: React.ReactNode;

    switch (status) {
      case 'completed':
        color = 'success';
        icon = <CheckCircleOutlined />;
        break;
      case 'failed':
        color = 'error';
        icon = <CloseCircleOutlined />;
        break;
      case 'processing':
        color = 'processing';
        icon = <SyncOutlined spin />;
        break;
      default:
        color = 'default';
        icon = null;
    }

    return (
      <Tag
        key={keyword}
        color={color}
        icon={icon}
        style={{
          margin: '4px',
          padding: '4px 8px',
          borderRadius: '6px',
          transition: `all ${animationDuration}ms ease-in-out`,
          transform: animatingKeyword === keyword ? 'scale(1.1)' : 'scale(1)',
        }}
      >
        {keyword}
      </Tag>
    );
  };

  // 所有关键词列表 (假设有完整的关键词列表)
  const allKeywords = Array.from(new Set([
    ...completedKeywords,
    ...failedKeywords,
    ...(currentKeyword ? [currentKeyword] : [])
  ]));

  return (
    <Card 
      title="关键词处理进度" 
      size="small"
      style={{ height: '100%' }}
    >
      <Space direction="vertical" style={{ width: '100%' }}>
        {/* 整体进度 */}
        <div>
          <div style={{ marginBottom: '8px' }}>
            <Text strong>整体进度 ({processedCount}/{totalKeywords})</Text>
          </div>
          <Progress
            percent={progressPercent}
            status={progressPercent === 100 ? 'success' : 'active'}
            strokeColor={{
              '0%': '#108ee9',
              '100%': '#87d068',
            }}
          />
        </div>

        {/* 当前处理的关键词 */}
        {currentKeyword && (
          <div>
            <Text strong style={{ color: '#1890ff' }}>
              正在处理: 
            </Text>
            <Tag
              color="processing"
              icon={<SyncOutlined spin />}
              style={{
                marginLeft: '8px',
                padding: '4px 12px',
                borderRadius: '6px',
                fontWeight: 'bold',
                animation: animatingKeyword === currentKeyword 
                  ? `pulse ${animationDuration}ms ease-in-out` 
                  : 'none',
              }}
            >
              {currentKeyword}
            </Tag>
          </div>
        )}

        {/* 统计信息 */}
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <Space>
            <Text type="success">
              <CheckCircleOutlined /> 完成: {completedKeywords.length}
            </Text>
            <Text type="danger">
              <CloseCircleOutlined /> 失败: {failedKeywords.length}
            </Text>
            <Text type="secondary">
              待处理: {totalKeywords - processedCount}
            </Text>
          </Space>
        </div>

        {/* 关键词标签列表 */}
        {allKeywords.length > 0 && (
          <div>
            <Text strong style={{ marginBottom: '8px', display: 'block' }}>
              关键词状态:
            </Text>
            <div 
              style={{ 
                maxHeight: '120px', 
                overflowY: 'auto',
                border: '1px solid #f0f0f0',
                borderRadius: '6px',
                padding: '8px',
                backgroundColor: '#fafafa'
              }}
            >
              {allKeywords.map(keyword => 
                renderKeywordTag(keyword, getKeywordStatus(keyword))
              )}
            </div>
          </div>
        )}
      </Space>

      <style>{`
        @keyframes pulse {
          0% { transform: scale(1); }
          50% { transform: scale(1.05); }
          100% { transform: scale(1); }
        }
      `}</style>
    </Card>
  );
};

export default KeywordProgress;