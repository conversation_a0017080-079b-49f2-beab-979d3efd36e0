import React, { useState, useEffect, useRef } from 'react';
import { Card, Statistic, Row, Col, Progress } from 'antd';
import { 
  ThunderboltOutlined, 
  CheckCircleOutlined, 
  ExclamationCircleOutlined,
  ClockCircleOutlined 
} from '@ant-design/icons';
import { formatTime, updateChartData } from './utils';
import type { RealtimeStatsProps, ChartDataPoint } from './types';

const RealtimeStats: React.FC<RealtimeStatsProps> = ({
  speed,
  successRate,
  errorCount,
  totalProcessed,
  estimatedTimeRemaining,
  showChart = false,
}) => {
  const [speedHistory, setSpeedHistory] = useState<ChartDataPoint[]>([]);
  const [animatingStats, setAnimatingStats] = useState({
    speed: false,
    successRate: false,
    errorCount: false,
    totalProcessed: false,
  });
  
  const prevValues = useRef({
    speed,
    successRate,
    errorCount,
    totalProcessed,
  });

  // 更新速度历史数据
  useEffect(() => {
    setSpeedHistory(prev => updateChartData(prev, speed, 30));
  }, [speed]);

  // 检测数值变化并触发动画
  useEffect(() => {
    const changes = {
      speed: speed !== prevValues.current.speed,
      successRate: successRate !== prevValues.current.successRate,
      errorCount: errorCount !== prevValues.current.errorCount,
      totalProcessed: totalProcessed !== prevValues.current.totalProcessed,
    };

    setAnimatingStats(changes);

    // 更新上一次的值
    prevValues.current = { speed, successRate, errorCount, totalProcessed };

    // 重置动画状态
    const timer = setTimeout(() => {
      setAnimatingStats({
        speed: false,
        successRate: false,
        errorCount: false,
        totalProcessed: false,
      });
    }, 300);

    return () => clearTimeout(timer);
  }, [speed, successRate, errorCount, totalProcessed]);

  // 获取成功率颜色
  const getSuccessRateColor = (rate: number) => {
    if (rate >= 90) return '#52c41a';
    if (rate >= 70) return '#faad14';
    return '#ff4d4f';
  };

  // 获取速度状态
  const getSpeedStatus = (currentSpeed: number) => {
    if (currentSpeed >= 60) return 'success';  // > 1条/秒
    if (currentSpeed >= 30) return 'active';   // > 0.5条/秒
    if (currentSpeed >= 10) return 'normal';   // > 1条/6秒
    return 'exception';
  };

  return (
    <Card 
      title="实时统计" 
      size="small"
      style={{ height: '100%' }}
    >
      <Row gutter={[16, 16]}>
        {/* 爬取速度 */}
        <Col span={12}>
          <div
            style={{
              transition: 'all 0.3s ease',
              transform: animatingStats.speed ? 'scale(1.05)' : 'scale(1)',
            }}
          >
            <Statistic
              title="爬取速度"
              value={speed}
              precision={1}
              suffix="条/分钟"
              prefix={<ThunderboltOutlined style={{ color: '#1890ff' }} />}
              valueStyle={{ 
                color: '#1890ff',
                fontSize: '20px',
                fontWeight: 'bold'
              }}
            />
            {/* 速度状态指示器 */}
            <Progress
              percent={Math.min((speed / 60) * 100, 100)}
              status={getSpeedStatus(speed)}
              showInfo={false}
              strokeWidth={6}
              size="small"
              style={{ marginTop: '4px' }}
            />
          </div>
        </Col>

        {/* 成功率 */}
        <Col span={12}>
          <div
            style={{
              transition: 'all 0.3s ease',
              transform: animatingStats.successRate ? 'scale(1.05)' : 'scale(1)',
            }}
          >
            <Statistic
              title="成功率"
              value={successRate}
              precision={1}
              suffix="%"
              prefix={<CheckCircleOutlined style={{ color: getSuccessRateColor(successRate) }} />}
              valueStyle={{ 
                color: getSuccessRateColor(successRate),
                fontSize: '20px',
                fontWeight: 'bold'
              }}
            />
            <Progress
              percent={successRate}
              status={successRate >= 90 ? 'success' : successRate >= 70 ? 'active' : 'exception'}
              showInfo={false}
              strokeWidth={6}
              size="small"
              style={{ marginTop: '4px' }}
            />
          </div>
        </Col>

        {/* 已处理总数 */}
        <Col span={12}>
          <div
            style={{
              transition: 'all 0.3s ease',
              transform: animatingStats.totalProcessed ? 'scale(1.05)' : 'scale(1)',
            }}
          >
            <Statistic
              title="已处理"
              value={totalProcessed}
              suffix="个"
              valueStyle={{ 
                color: '#52c41a',
                fontSize: '18px',
                fontWeight: 'bold'
              }}
            />
          </div>
        </Col>

        {/* 错误数量 */}
        <Col span={12}>
          <div
            style={{
              transition: 'all 0.3s ease',
              transform: animatingStats.errorCount ? 'scale(1.05)' : 'scale(1)',
            }}
          >
            <Statistic
              title="错误数量"
              value={errorCount}
              suffix="个"
              prefix={<ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />}
              valueStyle={{ 
                color: errorCount > 0 ? '#ff4d4f' : '#52c41a',
                fontSize: '18px',
                fontWeight: 'bold'
              }}
            />
          </div>
        </Col>

        {/* 预计剩余时间 */}
        {estimatedTimeRemaining > 0 && (
          <Col span={24}>
            <div style={{ textAlign: 'center', padding: '16px 0' }}>
              <Statistic
                title="预计剩余时间"
                value={formatTime(estimatedTimeRemaining)}
                prefix={<ClockCircleOutlined style={{ color: '#faad14' }} />}
                valueStyle={{ 
                  color: '#faad14',
                  fontSize: '16px',
                  fontWeight: 'bold'
                }}
              />
            </div>
          </Col>
        )}
      </Row>

      {/* 简单的速度趋势图表 */}
      {showChart && speedHistory.length > 1 && (
        <div style={{ marginTop: '16px', paddingTop: '16px', borderTop: '1px solid #f0f0f0' }}>
          <div style={{ marginBottom: '8px', fontSize: '12px', color: '#666' }}>
            速度趋势 (最近30个数据点)
          </div>
          <div 
            style={{
              height: '60px',
              position: 'relative',
              background: 'linear-gradient(to right, #f0f2f5, #fff)',
              borderRadius: '4px',
              padding: '8px',
            }}
          >
            <svg width="100%" height="100%" style={{ overflow: 'visible' }}>
              <polyline
                points={speedHistory.map((point, index) => 
                  `${(index / (speedHistory.length - 1)) * 100}%,${100 - (point.value / Math.max(...speedHistory.map(p => p.value), 1)) * 80}%`
                ).join(' ')}
                fill="none"
                stroke="#1890ff"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                style={{
                  filter: 'drop-shadow(0 2px 4px rgba(24, 144, 255, 0.2))',
                }}
              />
              {/* 最新数据点 */}
              {speedHistory.length > 0 && (
                <circle
                  cx="100%"
                  cy={`${100 - (speedHistory[speedHistory.length - 1].value / Math.max(...speedHistory.map(p => p.value), 1)) * 80}%`}
                  r="3"
                  fill="#1890ff"
                  stroke="#fff"
                  strokeWidth="2"
                />
              )}
            </svg>
          </div>
        </div>
      )}
    </Card>
  );
};

export default RealtimeStats;