# 前端运行爬虫修复技术设计

## 1. 架构概览

### 1.1 问题诊断
通过分析发现关键问题：

```mermaid
graph TB
    A[前端 API 调用] --> B[参数格式错误]
    B --> C[后端 Pydantic 验证失败]
    C --> D[任务启动失败]
    
    E[前端发送] --> F["keyword: '手机'<br/>target_count: 100<br/>sort_type: 'default'"]
    G[后端期望] --> H["keywords: ['手机']<br/>targetCount: 100<br/>sortMethod: 'default'"]
    
    I[直接运行] --> J[配置文件加载] --> K[正确参数格式] --> L[成功运行]
```

### 1.2 数据流对比

**直接运行（run_main.py）：**
```
配置文件 → PDDCrawler初始化 → 参数加载 → start_crawling()
```

**前端API调用：**
```
前端表单 → API请求 → FastAPI验证 → PDDCrawler实例化 → 参数设置 → start_crawling()
```

## 2. 核心问题分析

### 2.1 API数据格式不匹配

**问题现状：**
- **前端发送格式（api.js第14-18行）：**
```javascript
{
  keyword: config.keyword,           // 单个字符串
  max_pages: config.maxPages,        // 蛇形命名
  target_count: config.targetCount,  // 蛇形命名  
  sort_type: config.sortType         // 蛇形命名
}
```

- **后端期望格式（CrawlRequest模型）：**
```python
{
  keywords: List[str],        # 字符串数组，驼峰命名
  targetCount: int,           # 驼峰命名
  sortMethod: str,            # 不同的参数名
  maxPages: int,              # 驼峰命名
  headless: bool,             # 前端未传递
  enableFilter: bool          # 前端未传递
}
```

### 2.2 参数验证流程

```mermaid
sequenceDiagram
    participant F as 前端
    participant A as API服务器
    participant P as Pydantic验证
    participant C as PDDCrawler
    
    F->>A: POST /api/crawl/start
    Note over F,A: 错误的参数格式
    A->>P: 验证请求数据
    P-->>A: 验证失败
    A-->>F: HTTP 422 错误
    
    Note over A,C: 正确流程应该是：
    A->>P: 验证请求数据
    P->>A: 验证成功
    A->>C: 创建爬虫实例
    C->>C: 设置参数
    C->>C: 开始爬取
```

### 2.3 WebSocket路径不匹配

**问题：**
- 前端WebSocket URL: `ws://localhost:8000/ws`
- 后端WebSocket路由: `/ws/crawl/{task_id}`

## 3. 修复方案设计

### 3.1 前端API适配器设计

**创建API适配器类：**
```javascript
class CrawlApiAdapter {
  // 将前端参数转换为后端期望格式
  transformRequest(frontendConfig) {
    return {
      keywords: this.parseKeywords(frontendConfig.keyword),
      targetCount: frontendConfig.targetCount,
      sortMethod: frontendConfig.sortType || 'default',
      maxPages: frontendConfig.maxPages || 5,
      headless: frontendConfig.headless !== false,
      enableFilter: frontendConfig.enableFilter || false
    };
  }
  
  // 智能关键词解析
  parseKeywords(input) {
    if (Array.isArray(input)) return input;
    if (typeof input === 'string') {
      return input.split(/[,，\s]+/).filter(k => k.trim());
    }
    return [''];
  }
}
```

### 3.2 后端响应标准化

**统一错误处理格式：**
```python
class APIResponse(BaseModel):
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
    error: Optional[Dict[str, Any]] = None

class ErrorDetail(BaseModel):
    code: str
    message: str
    field: Optional[str] = None

# 错误处理中间件
@app.exception_handler(ValidationError)
async def validation_exception_handler(request: Request, exc: ValidationError):
    errors = []
    for error in exc.errors():
        errors.append(ErrorDetail(
            code="VALIDATION_ERROR",
            message=error["msg"],
            field=".".join(str(loc) for loc in error["loc"])
        ))
    
    return JSONResponse(
        status_code=422,
        content=APIResponse(
            success=False,
            message="请求参数验证失败",
            error={"details": [error.dict() for error in errors]}
        ).dict()
    )
```

### 3.3 WebSocket连接修复

**前端WebSocket管理器：**
```javascript
class WebSocketManager {
  connect(taskId) {
    const wsUrl = `ws://localhost:8000/ws/crawl/${taskId}`;
    this.ws = new WebSocket(wsUrl);
    
    this.ws.onopen = () => {
      console.log('WebSocket连接已建立');
      this.startHeartbeat();
    };
    
    this.ws.onmessage = (event) => {
      const data = JSON.parse(event.data);
      this.handleMessage(data);
    };
    
    this.ws.onerror = (error) => {
      console.error('WebSocket错误:', error);
      this.reconnect();
    };
  }
  
  startHeartbeat() {
    this.heartbeatInterval = setInterval(() => {
      if (this.ws.readyState === WebSocket.OPEN) {
        this.ws.send(JSON.stringify({ type: 'ping' }));
      }
    }, 30000);
  }
}
```

## 4. 数据模型设计

### 4.1 统一的请求响应模型

```python
# 请求模型增强
class CrawlRequest(BaseModel):
    keywords: List[str] = Field(..., description="搜索关键词列表")
    targetCount: int = Field(default=100, ge=1, le=10000, description="目标商品数量")
    sortMethod: Optional[str] = Field(default="default", description="排序方式")
    maxPages: Optional[int] = Field(default=5, ge=1, le=50, description="最大页数")
    headless: Optional[bool] = Field(default=True, description="无头模式")
    enableFilter: Optional[bool] = Field(default=False, description="启用商品筛选")
    
    @validator('keywords', pre=True)
    def parse_keywords(cls, v):
        if isinstance(v, str):
            # 支持逗号、中文逗号、空格分隔
            return [k.strip() for k in re.split(r'[,，\s]+', v) if k.strip()]
        return v

# 响应模型
class CrawlResponse(BaseModel):
    success: bool
    taskId: str
    message: str
    config: Optional[Dict[str, Any]] = None

class ProgressData(BaseModel):
    current: int
    total: int
    percentage: float
    status: str
    keyword: Optional[str] = None
```

### 4.2 前端状态管理

```javascript
// 爬虫状态管理
class CrawlerState {
  constructor() {
    this.state = {
      isRunning: false,
      taskId: null,
      progress: { current: 0, total: 0, percentage: 0 },
      data: [],
      error: null,
      config: null
    };
    this.listeners = new Set();
  }
  
  setState(newState) {
    this.state = { ...this.state, ...newState };
    this.notifyListeners();
  }
  
  subscribe(listener) {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }
  
  notifyListeners() {
    this.listeners.forEach(listener => listener(this.state));
  }
}
```

## 5. 错误处理机制

### 5.1 分层错误处理

```mermaid
graph TD
    A[前端表单验证] --> B[API请求发送]
    B --> C[网络层错误处理]
    C --> D[后端参数验证]
    D --> E[业务逻辑错误]
    E --> F[爬虫执行错误]
    
    G[错误统一处理中心] --> H[用户友好提示]
    G --> I[开发者调试信息]
    G --> J[错误日志记录]
```

### 5.2 错误分类处理

```javascript
class ErrorHandler {
  static handle(error, context) {
    if (error.name === 'ValidationError') {
      return this.handleValidationError(error, context);
    } else if (error.name === 'NetworkError') {
      return this.handleNetworkError(error, context);
    } else if (error.name === 'BusinessError') {
      return this.handleBusinessError(error, context);
    }
    return this.handleUnknownError(error, context);
  }
  
  static handleValidationError(error, context) {
    const messages = error.details?.map(d => `${d.field}: ${d.message}`) || [error.message];
    return {
      type: 'validation',
      title: '参数验证失败',
      messages,
      actions: ['检查输入参数', '查看帮助文档']
    };
  }
}
```

## 6. 性能优化设计

### 6.1 连接池管理

```python
# WebSocket连接池优化
class WebSocketConnectionPool:
    def __init__(self):
        self.connections: Dict[str, List[WebSocket]] = {}
        self.connection_stats = defaultdict(lambda: {"connected": 0, "messages": 0})
    
    async def add_connection(self, task_id: str, websocket: WebSocket):
        if task_id not in self.connections:
            self.connections[task_id] = []
        self.connections[task_id].append(websocket)
        self.connection_stats[task_id]["connected"] += 1
    
    async def broadcast(self, task_id: str, message: dict):
        if task_id not in self.connections:
            return
        
        dead_connections = []
        for ws in self.connections[task_id]:
            try:
                await ws.send_json(message)
                self.connection_stats[task_id]["messages"] += 1
            except Exception:
                dead_connections.append(ws)
        
        # 清理失效连接
        for ws in dead_connections:
            self.connections[task_id].remove(ws)
```

### 6.2 前端性能优化

```javascript
// 数据缓存和防抖
class PerformanceOptimizer {
  constructor() {
    this.cache = new Map();
    this.debounceTimers = new Map();
  }
  
  // 防抖处理进度更新
  debounceProgressUpdate(taskId, progressData) {
    if (this.debounceTimers.has(taskId)) {
      clearTimeout(this.debounceTimers.get(taskId));
    }
    
    this.debounceTimers.set(taskId, setTimeout(() => {
      this.updateProgressUI(progressData);
      this.debounceTimers.delete(taskId);
    }, 200));
  }
  
  // 数据分页加载
  paginateData(data, page = 1, pageSize = 50) {
    const start = (page - 1) * pageSize;
    const end = start + pageSize;
    return {
      data: data.slice(start, end),
      pagination: {
        current: page,
        total: Math.ceil(data.length / pageSize),
        pageSize,
        totalItems: data.length
      }
    };
  }
}
```

## 7. 监控和日志设计

### 7.1 前端监控

```javascript
class MonitoringService {
  constructor() {
    this.metrics = {
      apiCalls: 0,
      errors: 0,
      wsConnections: 0,
      dataReceived: 0
    };
  }
  
  trackApiCall(endpoint, duration, success) {
    this.metrics.apiCalls++;
    if (!success) this.metrics.errors++;
    
    console.log(`API调用: ${endpoint}, 耗时: ${duration}ms, 成功: ${success}`);
  }
  
  trackWebSocketEvent(event, data) {
    if (event === 'open') this.metrics.wsConnections++;
    if (event === 'message') this.metrics.dataReceived++;
    
    console.log(`WebSocket事件: ${event}`, data);
  }
}
```

### 7.2 后端监控增强

```python
# API调用监控中间件
@app.middleware("http")
async def monitor_requests(request: Request, call_next):
    start_time = time.time()
    
    response = await call_next(request)
    
    duration = time.time() - start_time
    logger.info(f"API调用监控", extra={
        "method": request.method,
        "url": str(request.url),
        "status_code": response.status_code,
        "duration": duration,
        "user_agent": request.headers.get("user-agent")
    })
    
    return response
```

## 8. 部署和配置

### 8.1 环境配置管理

```python
# 环境配置类
class Settings(BaseSettings):
    # API服务器配置
    host: str = "localhost"
    port: int = 8000
    debug: bool = False
    
    # CORS配置
    cors_origins: List[str] = ["http://localhost:3000", "http://localhost:5173"]
    
    # WebSocket配置
    ws_heartbeat_interval: int = 30
    ws_max_connections: int = 100
    
    # 爬虫配置
    default_target_count: int = 100
    max_target_count: int = 10000
    default_headless: bool = True
    
    class Config:
        env_file = ".env"

settings = Settings()
```

### 8.2 部署架构

```mermaid
graph TB
    subgraph "Development"
        A[Frontend Dev Server<br/>Vite/React] --> B[Backend API<br/>FastAPI uvicorn]
        B --> C[Browser Automation<br/>Playwright + Chrome]
    end
    
    subgraph "Production"
        D[Nginx<br/>Static Files + Proxy] --> E[Backend API<br/>FastAPI + Gunicorn]
        E --> F[Browser Pool<br/>Multiple Chrome Instances]
        E --> G[Redis<br/>Task Queue + Cache]
    end
```

## 9. 测试策略

### 9.1 单元测试

```python
# 后端API测试
@pytest.mark.asyncio
async def test_start_crawl_success():
    request_data = {
        "keywords": ["手机"],
        "targetCount": 50,
        "sortMethod": "default"
    }
    
    async with AsyncClient(app=app, base_url="http://test") as ac:
        response = await ac.post("/api/crawl/start", json=request_data)
    
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert "taskId" in data

# 前端组件测试
describe('CrawlControl Component', () => {
  test('should send correct API request format', async () => {
    const mockConfig = {
      keyword: '手机,电脑',
      targetCount: 100,
      sortType: 'default'
    };
    
    const apiSpy = jest.spyOn(apiService, 'startCrawl');
    
    render(<CrawlControl />);
    // ... 触发爬取操作
    
    expect(apiSpy).toHaveBeenCalledWith({
      keywords: ['手机', '电脑'],
      targetCount: 100,
      sortMethod: 'default',
      maxPages: 5,
      headless: true,
      enableFilter: false
    });
  });
});
```

### 9.2 集成测试

```python
# E2E测试场景
@pytest.mark.asyncio
async def test_complete_crawl_workflow():
    """测试完整的爬取工作流程"""
    # 1. 启动爬取任务
    request_data = {
        "keywords": ["测试商品"],
        "targetCount": 10
    }
    
    async with AsyncClient(app=app, base_url="http://test") as ac:
        response = await ac.post("/api/crawl/start", json=request_data)
        task_id = response.json()["taskId"]
        
        # 2. 检查任务状态
        await asyncio.sleep(1)
        status_response = await ac.get(f"/api/crawl/{task_id}/status")
        assert status_response.json()["status"] in ["running", "completed"]
        
        # 3. 获取预览数据
        preview_response = await ac.get(f"/api/crawl/{task_id}/preview")
        assert "data" in preview_response.json()
```

这个技术设计文档提供了完整的修复方案架构，包括问题诊断、解决方案设计、实施细节和测试策略。接下来我将继续完成任务清单中的其他项目。