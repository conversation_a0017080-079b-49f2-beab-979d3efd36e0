import type { Cookie, FormatDetectionResult, CookieParseOptions } from './types';

/**
 * 智能检测Cookie格式并解析
 */
export function detectAndParseCookies(input: string, options?: CookieParseOptions): FormatDetectionResult {
  const trimmedInput = input.trim();
  
  // 尝试 JSON 格式
  if (trimmedInput.startsWith('[') || trimmedInput.startsWith('{')) {
    try {
      const parsed = JSON.parse(trimmedInput);
      const cookies = Array.isArray(parsed) ? parsed : parsed.cookies || [];
      return {
        format: 'json',
        confidence: 0.9,
        data: normalizeCookies(cookies, options)
      };
    } catch {
      // JSON 解析失败，继续尝试其他格式
    }
  }
  
  // 尝试 Netscape HTTP Cookie 格式
  if (trimmedInput.includes('\t') && trimmedInput.includes('.')) {
    const cookies = parseNetscapeFormat(trimmedInput, options);
    if (cookies.length > 0) {
      return {
        format: 'netscape',
        confidence: 0.8,
        data: cookies
      };
    }
  }
  
  // 尝试浏览器导出格式（通常是分号分隔的字符串）
  if (trimmedInput.includes('=') && (trimmedInput.includes(';') || trimmedInput.includes('\n'))) {
    const cookies = parseBrowserExportFormat(trimmedInput, options);
    if (cookies.length > 0) {
      return {
        format: 'browser-export',
        confidence: 0.7,
        data: cookies
      };
    }
  }
  
  // 尝试简单的 Cookie 字符串格式
  if (trimmedInput.includes('=')) {
    const cookies = parseCookieString(trimmedInput, options);
    if (cookies.length > 0) {
      return {
        format: 'cookie-string',
        confidence: 0.6,
        data: cookies
      };
    }
  }
  
  return {
    format: 'cookie-string',
    confidence: 0,
    data: []
  };
}

/**
 * 解析 Netscape HTTP Cookie 格式
 */
function parseNetscapeFormat(input: string, _options?: CookieParseOptions): Cookie[] {
  const lines = input.split('\n').filter(line => line.trim() && !line.startsWith('#'));
  const cookies: Cookie[] = [];
  
  for (const line of lines) {
    const parts = line.split('\t');
    if (parts.length >= 7) {
      const [domain, , path, secure, expires, name, value] = parts;
      cookies.push({
        name: name.trim(),
        value: value.trim(),
        domain: domain.trim(),
        path: path.trim() || '/',
        expires: parseInt(expires) || undefined,
        secure: secure.toLowerCase() === 'true',
        httpOnly: false // Netscape 格式通常不包含 httpOnly
      });
    }
  }
  
  return cookies;
}

/**
 * 解析浏览器导出格式
 */
function parseBrowserExportFormat(input: string, options?: CookieParseOptions): Cookie[] {
  const cookies: Cookie[] = [];
  const lines = input.split('\n').filter(line => line.trim());
  
  for (const line of lines) {
    const cookiePairs = line.split(';').map(pair => pair.trim());
    const cookie: Partial<Cookie> = {
      domain: options?.domain || '.yangkeduo.com',
      path: options?.path || '/',
    };
    
    for (let i = 0; i < cookiePairs.length; i++) {
      const pair = cookiePairs[i];
      if (pair.includes('=')) {
        const [key, ...valueParts] = pair.split('=');
        const value = valueParts.join('=').trim();
        const keyLower = key.trim().toLowerCase();
        
        if (i === 0) {
          // 第一个键值对是 cookie 名称和值
          cookie.name = key.trim();
          cookie.value = value;
        } else {
          // 处理其他属性
          switch (keyLower) {
            case 'domain':
              cookie.domain = value;
              break;
            case 'path':
              cookie.path = value;
              break;
            case 'expires':
              cookie.expires = new Date(value).getTime() / 1000;
              break;
            case 'max-age':
              cookie.expires = Math.floor(Date.now() / 1000) + parseInt(value);
              break;
            case 'samesite':
              cookie.sameSite = value as 'Strict' | 'Lax' | 'None';
              break;
          }
        }
      } else {
        // 处理布尔属性
        const attrLower = pair.toLowerCase();
        if (attrLower === 'secure') {
          cookie.secure = true;
        } else if (attrLower === 'httponly') {
          cookie.httpOnly = true;
        }
      }
    }
    
    if (cookie.name && cookie.value !== undefined) {
      cookies.push(cookie as Cookie);
    }
  }
  
  return cookies;
}

/**
 * 解析简单的 Cookie 字符串
 */
function parseCookieString(input: string, options?: CookieParseOptions): Cookie[] {
  const cookies: Cookie[] = [];
  const pairs = input.split(';').map(pair => pair.trim());
  
  for (const pair of pairs) {
    if (pair.includes('=')) {
      const [name, ...valueParts] = pair.split('=');
      const value = valueParts.join('=').trim();
      
      cookies.push({
        name: name.trim(),
        value: value,
        domain: options?.domain || '.yangkeduo.com',
        path: options?.path || '/',
        expires: options?.defaultExpires
      });
    }
  }
  
  return cookies;
}

/**
 * 标准化 Cookie 对象
 */
function normalizeCookies(cookies: any[], options?: CookieParseOptions): Cookie[] {
  return cookies.map(cookie => ({
    name: cookie.name || '',
    value: cookie.value || '',
    domain: cookie.domain || options?.domain || '.yangkeduo.com',
    path: cookie.path || options?.path || '/',
    expires: cookie.expires || cookie.expirationDate || options?.defaultExpires,
    httpOnly: cookie.httpOnly || false,
    secure: cookie.secure || false,
    sameSite: cookie.sameSite || 'Lax'
  }));
}

/**
 * 验证单个 Cookie
 */
export function validateCookie(cookie: Cookie): { valid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  if (!cookie.name) {
    errors.push('Cookie 名称不能为空');
  }
  
  if (cookie.value === undefined || cookie.value === null) {
    errors.push('Cookie 值不能为空');
  }
  
  if (!cookie.domain) {
    errors.push('Cookie 域名不能为空');
  }
  
  // 检查是否过期
  if (cookie.expires && cookie.expires < Math.floor(Date.now() / 1000)) {
    errors.push('Cookie 已过期');
  }
  
  return {
    valid: errors.length === 0,
    errors
  };
}

/**
 * 验证必要的拼多多 Cookie
 */
export function validatePDDCookies(cookies: Cookie[]): { valid: boolean; missing: string[]; expired: string[] } {
  const requiredCookies = ['PDDAccessToken', 'pdd_user_id'];
  const cookieNames = cookies.map(c => c.name);
  const missing = requiredCookies.filter(name => !cookieNames.includes(name));
  
  const expired: string[] = [];
  const currentTime = Math.floor(Date.now() / 1000);
  
  for (const cookie of cookies) {
    if (requiredCookies.includes(cookie.name) && cookie.expires && cookie.expires < currentTime) {
      expired.push(cookie.name);
    }
  }
  
  return {
    valid: missing.length === 0 && expired.length === 0,
    missing,
    expired
  };
}

/**
 * 格式化过期时间显示
 */
export function formatExpiryTime(expires?: number): string {
  if (!expires) return '会话结束';
  
  const expiryDate = new Date(expires * 1000);
  const now = new Date();
  
  if (expiryDate < now) {
    return '已过期';
  }
  
  const diffMs = expiryDate.getTime() - now.getTime();
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
  const diffHours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
  
  if (diffDays > 0) {
    return `${diffDays}天后过期`;
  } else if (diffHours > 0) {
    return `${diffHours}小时后过期`;
  } else {
    return '即将过期';
  }
}

/**
 * 获取 Cookie 状态颜色
 */
export function getCookieStatusColor(cookie: Cookie): 'success' | 'warning' | 'error' {
  if (!cookie.expires) return 'success';
  
  const currentTime = Math.floor(Date.now() / 1000);
  const timeLeft = cookie.expires - currentTime;
  
  if (timeLeft < 0) return 'error'; // 已过期
  if (timeLeft < 24 * 60 * 60) return 'warning'; // 24小时内过期
  return 'success';
}

/**
 * 导出 Cookie 为 JSON 格式
 */
export function exportCookiesAsJSON(cookies: Cookie[]): string {
  return JSON.stringify(cookies, null, 2);
}

/**
 * 导出 Cookie 为 Netscape 格式
 */
export function exportCookiesAsNetscape(cookies: Cookie[]): string {
  const header = '# Netscape HTTP Cookie File\n# This is a generated file! Do not edit.\n\n';
  const lines = cookies.map(cookie => {
    const expires = cookie.expires || Math.floor(Date.now() / 1000) + 365 * 24 * 60 * 60;
    return [
      cookie.domain,
      'TRUE', // domain flag
      cookie.path || '/',
      cookie.secure ? 'TRUE' : 'FALSE',
      expires,
      cookie.name,
      cookie.value
    ].join('\t');
  });
  
  return header + lines.join('\n');
}