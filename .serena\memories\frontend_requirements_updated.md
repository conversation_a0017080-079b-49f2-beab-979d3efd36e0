# 前端需求分析更新要点

## 核心要求
- 前端运行结果必须与后端直接运行完全一致
- 前端必须支持后端的所有功能（多关键词、商品筛选）
- 添加Cookie管理功能（输入、验证、保存）

## 关键功能
1. **Cookie管理**：支持多种格式输入，完整API调用
2. **多关键词爬取**：逗号分隔输入，数组格式发送
3. **商品筛选**：enableFilter参数控制，筛选结果显示
4. **实时数据**：WebSocket消息格式完全一致
5. **数据导出**：Excel/CSV格式与后端一致

## API格式要求
- 严格按照CrawlRequest模型发送数据
- keywords必须是数组格式
- enableFilter控制商品筛选
- Cookie管理API完整调用

## 开发优先级
Phase 1: Cookie管理 + 基础爬取 + 多关键词
Phase 2: 实时预览 + 筛选 + 导出
Phase 3: 高级功能 + 体验优化