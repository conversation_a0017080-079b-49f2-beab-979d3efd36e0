{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "build:analyze": "npm run build && npx vite-bundle-analyzer dist/assets/*.js", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,scss,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,scss,md}\"", "type-check": "tsc --noEmit", "preview": "vite preview", "clean": "rimraf dist node_modules/.vite", "reinstall": "npm run clean && npm install", "test": "echo \"No tests specified\" && exit 0", "prepare": "npm run type-check && npm run lint", "serve": "npm run build && npm run preview"}, "dependencies": {"antd": "^5.26.7", "axios": "^1.11.0", "dayjs": "^1.11.13", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.30.1", "react-use-websocket": "^4.13.0", "zustand": "^4.5.7"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/node": "^22.17.0", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.32.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "prettier": "^3.6.2", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4"}}