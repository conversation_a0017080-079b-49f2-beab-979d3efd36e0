import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ConfigProvider, Layout, Menu, theme, App, Spin } from 'antd';
import {
  RobotOutlined,
  DatabaseOutlined,
} from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';
import ErrorBoundary from './components/ErrorBoundary';
import Crawler from './pages/Crawler';
import Products from './pages/Products';
import './App.css';

// 加载中组件
const LoadingSpinner: React.FC = () => (
  <div style={{
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: '200px'
  }}>
    <Spin size="large" />
  </div>
);

const { Header, Sider, Content } = Layout;

const AppContent: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { token } = theme.useToken();

  const menuItems = [
    {
      key: '/crawler',
      icon: <RobotOutlined />,
      label: '爬虫控制',
    },
    {
      key: '/products',
      icon: <DatabaseOutlined />,
      label: '商品数据',
    },
  ];

  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key);
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider
        width={200}
        style={{
          background: token.colorBgContainer,
        }}
      >
        <div
          style={{
            height: 64,
            margin: 16,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '18px',
            fontWeight: 'bold',
            color: token.colorPrimary,
          }}
        >
          拼多多爬虫
        </div>
        <Menu
          mode="inline"
          selectedKeys={[location.pathname]}
          items={menuItems}
          onClick={handleMenuClick}
          style={{ borderRight: 0 }}
        />
      </Sider>
      
      <Layout>
        <Header
          style={{
            padding: '0 24px',
            background: token.colorBgContainer,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            borderBottom: `1px solid ${token.colorBorder}`,
          }}
        >
          <h2 style={{ margin: 0, color: token.colorText }}>
            拼多多商品数据爬虫系统
          </h2>
        </Header>
        
        <Content
          style={{
            margin: 0,
            background: token.colorBgLayout,
            minHeight: 'calc(100vh - 64px)',
          }}
        >
          <ErrorBoundary>
            <Routes>
              <Route path="/" element={<Navigate to="/crawler" replace />} />
              <Route path="/crawler" element={<Crawler />} />
              <Route path="/products" element={<Products />} />
              <Route path="*" element={<Navigate to="/crawler" replace />} />
            </Routes>
          </ErrorBoundary>
        </Content>
      </Layout>
    </Layout>
  );
};

const AppWrapper: React.FC = () => {
  return (
    <ConfigProvider
      theme={{
        algorithm: theme.defaultAlgorithm,
        token: {
          colorPrimary: '#1890ff',
        },
      }}
    >
      <App>
        <Router>
          <AppContent />
        </Router>
      </App>
    </ConfigProvider>
  );
};

export default AppWrapper;
