import { useState, useCallback } from 'react';
import { message } from 'antd';
import { apiService } from '@/services/api';
import { useAppStore } from '@/stores/appStore';
import type { SearchConfig } from '@/types';

export const useApi = () => {
  const {
    setLoading,
    setError,
    setCrawlStatus,
    setProducts,
    setStatistics,
    setPagination,
  } = useAppStore();

  const [localLoading, setLocalLoading] = useState<Record<string, boolean>>({});

  // 通用请求包装器
  const withLoading = useCallback(async <T>(
    key: string,
    asyncFn: () => Promise<T>,
    options: {
      showMessage?: boolean;
      successMessage?: string;
      errorMessage?: string;
      useGlobalLoading?: boolean;
    } = {}
  ): Promise<T | null> => {
    const {
      showMessage = true,
      successMessage,
      errorMessage,
      useGlobalLoading = false,
    } = options;

    try {
      // 设置加载状态
      if (useGlobalLoading) {
        setLoading(key as any, true);
      } else {
        setLocalLoading(prev => ({ ...prev, [key]: true }));
      }

      const result = await asyncFn();

      if (showMessage && successMessage) {
        message.success(successMessage);
      }

      setError(null);
      return result;
    } catch (error: any) {
      const errorMsg = error.message || errorMessage || '操作失败';
      setError(errorMsg);
      
      if (showMessage) {
        message.error(errorMsg);
      }
      
      return null;
    } finally {
      // 清除加载状态
      if (useGlobalLoading) {
        setLoading(key as any, false);
      } else {
        setLocalLoading(prev => ({ ...prev, [key]: false }));
      }
    }
  }, [setLoading, setError]);

  // 启动爬虫
  const startCrawl = useCallback(async (config: SearchConfig) => {
    return withLoading('crawling', async () => {
      const response = await apiService.startCrawl(config);
      if (response.success) {
        message.success('爬虫启动成功');
        return response.data;
      } else {
        throw new Error(response.message || '启动失败');
      }
    }, {
      useGlobalLoading: true,
      errorMessage: '启动爬虫失败',
    });
  }, [withLoading]);

  // 停止爬虫
  const stopCrawl = useCallback(async () => {
    return withLoading('crawling', async () => {
      const response = await apiService.stopCrawl();
      if (response.success) {
        message.success('爬虫已停止');
        return response.data;
      } else {
        throw new Error(response.message || '停止失败');
      }
    }, {
      useGlobalLoading: true,
      errorMessage: '停止爬虫失败',
    });
  }, [withLoading]);

  // 获取爬虫状态
  const fetchCrawlStatus = useCallback(async () => {
    return withLoading('status', async () => {
      const response = await apiService.getCrawlStatus();
      if (response.success && response.data) {
        setCrawlStatus(response.data);
        return response.data;
      } else {
        throw new Error(response.message || '获取状态失败');
      }
    }, {
      showMessage: false,
    });
  }, [withLoading, setCrawlStatus]);

  // 获取产品数据
  const fetchProducts = useCallback(async (params?: {
    page?: number;
    pageSize?: number;
    keyword?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }) => {
    return withLoading('fetching', async () => {
      const response = await apiService.getProducts(params);
      if (response.success && response.data) {
        setProducts(response.data.products);
        setPagination({
          current: params?.page || 1,
          pageSize: params?.pageSize || 20,
          total: response.data.total,
        });
        return response.data;
      } else {
        throw new Error(response.message || '获取数据失败');
      }
    }, {
      useGlobalLoading: true,
      showMessage: false,
    });
  }, [withLoading, setProducts, setPagination]);

  // 导出数据
  const exportData = useCallback(async (format: 'excel' | 'csv' | 'json') => {
    return withLoading('exporting', async () => {
      const blob = await apiService.exportProducts(format);
      
      // 创建下载链接
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `products_${new Date().toISOString().split('T')[0]}.${format}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      
      return true;
    }, {
      useGlobalLoading: true,
      successMessage: '数据导出成功',
      errorMessage: '数据导出失败',
    });
  }, [withLoading]);

  // 获取统计数据
  const fetchStatistics = useCallback(async () => {
    return withLoading('statistics', async () => {
      const response = await apiService.getStatistics();
      if (response.success && response.data) {
        setStatistics(response.data);
        return response.data;
      } else {
        throw new Error(response.message || '获取统计数据失败');
      }
    }, {
      showMessage: false,
    });
  }, [withLoading, setStatistics]);

  // 清除数据
  const clearData = useCallback(async () => {
    return withLoading('clearing', async () => {
      const response = await apiService.clearData();
      if (response.success) {
        setProducts([]);
        setPagination({ current: 1, pageSize: 20, total: 0 });
        message.success('数据已清除');
        return true;
      } else {
        throw new Error(response.message || '清除数据失败');
      }
    }, {
      errorMessage: '清除数据失败',
    });
  }, [withLoading, setProducts, setPagination]);

  // 健康检查
  const healthCheck = useCallback(async () => {
    return withLoading('health', async () => {
      const response = await apiService.healthCheck();
      return response.success;
    }, {
      showMessage: false,
    });
  }, [withLoading]);

  return {
    // 状态
    loading: localLoading,
    
    // API方法
    startCrawl,
    stopCrawl,
    fetchCrawlStatus,
    fetchProducts,
    exportData,
    fetchStatistics,
    clearData,
    healthCheck,
  };
};