import React, { useState, useEffect, useCallback } from 'react';
import { 
  Steps, 
  Card, 
  message, 
  Spin,
  Alert,
  Space,
  Button,
  Typography
} from 'antd';
import {
  ImportOutlined,
  CheckCircleOutlined,
  SaveOutlined,
  EyeOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import CookieImportSimple from './CookieImportSimple';
import CookieDisplay from './CookieDisplay';
import CookieValidator from './CookieValidator';
import type { Cookie, CookieStatus, CookieImportData, CookieValidationResult } from './types';
import { apiClient } from '@/services/api';

const { Title, Paragraph } = Typography;

interface CookieManagerProps {
  onCookieStatusChange?: (status: CookieStatus) => void;
}

const CookieManager: React.FC<CookieManagerProps> = ({ onCookieStatusChange }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [cookies, setCookies] = useState<Cookie[]>([]);
  const [, setCookieStatus] = useState<CookieStatus | null>(null);
  const [validationResult, setValidationResult] = useState<CookieValidationResult | null>(null);

  // 获取Cookie状态
  const fetchCookieStatus = useCallback(async () => {
    setLoading(true);
    try {
      const response = await apiClient.getCookieStatus();
      if (response.success && response.data) {
        setCookieStatus(response.data);
        setCookies(response.data.cookies || []);
        
        // 更新步骤
        if (response.data.cookies && response.data.cookies.length > 0) {
          if (response.data.valid) {
            setCurrentStep(2); // 直接到验证完成步骤
          } else {
            setCurrentStep(1); // 到验证步骤
          }
        } else {
          setCurrentStep(0); // 需要导入
        }
        
        // 回调状态变化
        if (onCookieStatusChange) {
          onCookieStatusChange(response.data);
        }
      }
    } catch (error) {
      console.error('获取Cookie状态失败:', error);
      message.error('获取Cookie状态失败');
    } finally {
      setLoading(false);
    }
  }, [onCookieStatusChange]);

  // 导入Cookie
  const handleImport = useCallback(async (data: CookieImportData) => {
    setLoading(true);
    try {
      const response = await apiClient.importCookie(data);
      if (response.success) {
        message.success('Cookie导入成功');
        
        // 刷新状态
        await fetchCookieStatus();
        
        // 进入下一步
        setCurrentStep(1);
      } else {
        message.error(response.message || 'Cookie导入失败');
      }
    } catch (error) {
      console.error('导入Cookie失败:', error);
      message.error('导入Cookie失败');
    } finally {
      setLoading(false);
    }
  }, [fetchCookieStatus]);

  // 验证Cookie
  const handleValidation = useCallback(async (result: CookieValidationResult) => {
    setValidationResult(result);
    
    if (result.valid) {
      // 验证通过，进入保存步骤
      setCurrentStep(2);
      message.success('Cookie验证通过');
    } else {
      message.warning('Cookie验证未通过，请检查并更新');
    }
  }, []);

  // 保存Cookie
  const handleSave = useCallback(async () => {
    if (cookies.length === 0) {
      message.error('没有Cookie可保存');
      return;
    }

    setLoading(true);
    try {
      const response = await apiClient.saveCookie(cookies);
      
      if (response.success) {
        message.success('Cookie保存成功');
        
        // 刷新状态
        await fetchCookieStatus();
        
        // 完成流程
        setCurrentStep(2);
      } else {
        message.error(response.message || 'Cookie保存失败');
      }
    } catch (error) {
      console.error('保存Cookie失败:', error);
      message.error('保存Cookie失败');
    } finally {
      setLoading(false);
    }
  }, [cookies, fetchCookieStatus]);

  // 清除Cookie
  const handleClear = useCallback(async () => {
    setLoading(true);
    try {
      const response = await apiClient.clearCookie();
      if (response.success) {
        message.success('Cookie已清除');
        setCookies([]);
        setCookieStatus(null);
        setValidationResult(null);
        setCurrentStep(0);
        
        if (onCookieStatusChange) {
          onCookieStatusChange({
            exists: false,
            valid: false,
            cookies: []
          });
        }
      } else {
        message.error(response.message || '清除Cookie失败');
      }
    } catch (error) {
      console.error('清除Cookie失败:', error);
      message.error('清除Cookie失败');
    } finally {
      setLoading(false);
    }
  }, [onCookieStatusChange]);

  // 组件挂载时获取状态
  useEffect(() => {
    fetchCookieStatus();
  }, []); // 只在组件挂载时执行一次

  // 步骤配置
  const steps = [
    {
      title: '导入Cookie',
      description: '上传或粘贴Cookie数据',
      icon: <ImportOutlined />,
      status: currentStep > 0 ? 'finish' : currentStep === 0 ? 'process' : 'wait'
    },
    {
      title: '验证Cookie',
      description: '检查Cookie有效性',
      icon: <CheckCircleOutlined />,
      status: currentStep > 1 ? 'finish' : currentStep === 1 ? 'process' : 'wait'
    },
    {
      title: '保存并使用',
      description: 'Cookie已准备就绪',
      icon: <SaveOutlined />,
      status: currentStep === 2 ? 'finish' : 'wait'
    }
  ];

  // 渲染步骤内容
  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <CookieImportSimple 
            onImport={handleImport} 
            loading={loading} 
          />
        );
      
      case 1:
        return (
          <Space direction="vertical" size="large" style={{ width: '100%' }}>
            <CookieValidator 
              cookies={cookies}
              onValidate={handleValidation}
              loading={loading}
            />
            <CookieDisplay 
              cookies={cookies}
              loading={loading}
              onRefresh={fetchCookieStatus}
              onClear={handleClear}
            />
            {validationResult && validationResult.valid && (
              <div style={{ textAlign: 'center' }}>
                <Button 
                  type="primary" 
                  size="large"
                  icon={<SaveOutlined />}
                  onClick={handleSave}
                  loading={loading}
                >
                  保存Cookie并继续
                </Button>
              </div>
            )}
          </Space>
        );
      
      case 2:
        return (
          <Space direction="vertical" size="large" style={{ width: '100%' }}>
            <Alert
              message="Cookie设置完成"
              description="Cookie已成功配置，爬虫系统现在可以正常使用。您可以前往爬虫控制页面开始数据采集。"
              type="success"
              showIcon
              icon={<CheckCircleOutlined />}
            />
            <CookieDisplay 
              cookies={cookies}
              loading={loading}
              onRefresh={fetchCookieStatus}
              onClear={handleClear}
            />
          </Space>
        );
      
      default:
        return null;
    }
  };

  // 渲染操作按钮
  const renderActions = () => {
    return (
      <Space style={{ marginTop: 24 }}>
        <Button 
          icon={<ReloadOutlined />}
          onClick={fetchCookieStatus}
          loading={loading}
        >
          刷新状态
        </Button>
        
        {currentStep > 0 && (
          <Button 
            onClick={() => setCurrentStep(Math.max(0, currentStep - 1))}
            disabled={loading}
          >
            上一步
          </Button>
        )}
        
        {currentStep === 2 && (
          <Button 
            type="primary"
            icon={<EyeOutlined />}
            onClick={() => window.location.href = '/crawler'}
          >
            前往爬虫控制
          </Button>
        )}
      </Space>
    );
  };

  return (
    <Spin spinning={loading} tip="处理中...">
      <div style={{ padding: '24px', minHeight: '100vh', backgroundColor: '#f5f5f5' }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
          {/* 页面标题和说明 */}
          <Card style={{ marginBottom: 24 }}>
            <Title level={2} style={{ marginBottom: 16 }}>
              Cookie 管理
            </Title>
            <Paragraph type="secondary">
              Cookie是爬虫系统访问拼多多的身份凭证。请按照以下步骤完成Cookie配置：
              导入有效的Cookie数据，系统会自动验证其有效性，确认无误后保存即可开始使用爬虫功能。
            </Paragraph>
          </Card>

          {/* 步骤指示器 */}
          <Card style={{ marginBottom: 24 }}>
            <Steps 
              current={currentStep} 
              items={steps.map(step => ({
                title: step.title,
                description: step.description,
                icon: step.icon,
                status: step.status as any
              }))}
            />
          </Card>

          {/* 步骤内容 */}
          <div style={{ marginBottom: 24 }}>
            {renderStepContent()}
          </div>

          {/* 操作按钮 */}
          <Card>
            {renderActions()}
          </Card>
        </div>
      </div>
    </Spin>
  );
};

export default CookieManager;