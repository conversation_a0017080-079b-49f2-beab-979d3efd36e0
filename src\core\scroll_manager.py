"""
智能滚动管理器模块
负责智能滚动页面并监控数据加载
"""

import asyncio
import time
import random
from typing import Callable, Optional
from playwright.async_api import Page
from loguru import logger

from src.utils.helpers import load_config


class SmartScrollManager:
    """智能滚动管理器类"""
    
    def __init__(self, config_path: str = "config/settings.yaml"):
        """初始化智能滚动管理器"""
        self.config = load_config(config_path)
        self.scroll_config = self.config.get("scroll", {})
        
        # 滚动配置
        self.max_scrolls = self.scroll_config.get("max_scrolls", 10)
        self.scroll_distance = self.scroll_config.get("scroll_distance", 1000)
        self.wait_for_response = self.scroll_config.get("wait_for_response", 3)
        self.no_data_threshold = self.scroll_config.get("no_data_threshold", 2)

        # 动态滚动配置
        self.dynamic_scrolling = self.scroll_config.get("dynamic_scrolling", False)
        scroll_range = self.scroll_config.get("scroll_distance_range", {})
        self.scroll_min = scroll_range.get("min", 1400)
        self.scroll_max = scroll_range.get("max", 1900)

        # API响应检测配置
        self.smart_api_detection = self.scroll_config.get("smart_api_detection", True)
        self.api_response_timeout = self.scroll_config.get("api_response_timeout", 3.0)
        self.api_detected_wait = self.scroll_config.get("api_detected_wait", 0.5)
        
        # 自适应延迟配置
        adaptive_delay = self.scroll_config.get("adaptive_delay", {})
        self.min_delay = adaptive_delay.get("min", 1)
        self.max_delay = adaptive_delay.get("max", 5)
        self.delay_increment = adaptive_delay.get("increment", 0.5)
        
        # 懒加载配置
        lazy_loading_config = self.scroll_config.get("lazy_loading", {})
        self.lazy_loading_enabled = lazy_loading_config.get("enabled", True)
        self.lazy_loading_timeout = lazy_loading_config.get("wait_timeout", 5.0)
        self.near_bottom_threshold = lazy_loading_config.get("near_bottom_threshold", 200)
        self.content_check_interval = lazy_loading_config.get("content_check_interval", 0.2)
        
        # 状态变量
        self.current_delay = self.min_delay
        self.no_data_count = 0

        # 滚动状态跟踪
        self.scroll_count = 0
        self.no_new_items_count = 0
        self.last_item_count = 0
        self.stable_count = 0
        self._last_positions = []  # 修复缺失的属性
        self._same_position_count = 0
        self._last_check_time = None

        # 性能统计
        self.start_time = None
        self.scroll_efficiency = []  # 每次滚动获取的商品数
        
        logger.info("智能滚动管理器初始化完成")
    
    async def smart_scroll(self, page: Page, data_callback: Callable, target_count: Optional[int] = None, target_check_callback: Optional[Callable] = None) -> int:
        """
        智能滚动并收集数据
        
        Args:
            page: Playwright页面对象
            data_callback: 数据回调函数，用于获取当前数据数量
            target_count: 目标数量，如果提供则达到目标后停止
            target_check_callback: 检查目标是否达成的回调函数
            
        Returns:
            int: 收集到的数据数量
        """
        logger.info(f"开始智能滚动，最大滚动次数: {self.max_scrolls}，目标数量: {target_count or '无限制'}")
        
        initial_data_count = await self._get_current_data_count(data_callback)
        total_scrolls = 0
        
        # 动态调整最大滚动次数
        if target_count and target_count > 0:
            # 根据目标数量动态计算需要的滚动次数
            estimated_items_per_scroll = 20  # 假设每次滚动获取20个商品
            calculated_scrolls = int((target_count / estimated_items_per_scroll) * 1.5)  # 留50%余量
            self.max_scrolls = max(self.max_scrolls, calculated_scrolls)
            logger.info(f"根据目标数量{target_count}，动态调整最大滚动次数为: {self.max_scrolls}")
        
        for scroll_num in range(1, self.max_scrolls + 1):
            # 获取当前数据量
            current_count = await self._get_current_data_count(data_callback)
            collected_so_far = current_count - initial_data_count
            
            # 显示进度
            if target_count:
                progress = (collected_so_far / target_count) * 100
                logger.info(f"进度: {collected_so_far}/{target_count} ({progress:.1f}%) - 第 {scroll_num} 次滚动")
            else:
                logger.debug(f"执行第 {scroll_num}/{self.max_scrolls} 次滚动，已收集 {collected_so_far} 条")
            
            # 记录滚动前的数据数量
            before_scroll_count = current_count
            
            # 检查是否接近页面底部（可能触发懒加载）
            near_bottom_result = await page.evaluate(f"""
                () => {{
                    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                    const windowHeight = window.innerHeight;
                    const documentHeight = document.documentElement.scrollHeight;
                    return scrollTop + windowHeight >= documentHeight - {self.near_bottom_threshold};
                }}
            """)
            
            if near_bottom_result:
                logger.debug("接近页面底部，等待懒加载...")
                # 等待懒加载完成
                loading_success = await self.wait_for_lazy_loading(page)
                if loading_success:
                    logger.info("懒加载完成，继续滚动")
                else:
                    logger.debug("懒加载等待超时，检查是否真正到达底部")
                    
                    # 再次检查是否真正到达底部
                    if await self.is_page_bottom(page):
                        logger.info("确认已到达页面底部，停止滚动")
                        if target_count and collected_so_far < target_count:
                            logger.warning(f"页面底部但未达到目标，已收集 {collected_so_far}/{target_count}")
                        break
            
            # 滚动前最后检查（防止异步数据更新导致的竞态条件）
            # 优先检查目标达成状态回调（更可靠）
            if target_check_callback and callable(target_check_callback):
                try:
                    if target_check_callback():
                        logger.success(f"滚动前检查：目标已达成，停止滚动")
                        break
                except Exception as e:
                    logger.debug(f"目标检查回调执行失败: {e}")
            
            # 备用检查：基于数据计数
            pre_scroll_count = await self._get_current_data_count(data_callback)
            pre_scroll_collected = pre_scroll_count - initial_data_count
            if target_count and pre_scroll_collected >= target_count:
                logger.success(f"滚动前检查：已达到目标数量 {target_count}，停止滚动")
                break
            
            # 执行滚动
            await self._perform_scroll(page)
            total_scrolls += 1

            # 智能数据检查（根据配置选择检测模式）
            if self.smart_api_detection:
                # 智能API检测模式
                api_detected = await self._wait_for_api_response_smart(page)
                if api_detected:
                    logger.debug(f"✅ 检测到API响应，快速等待 {self.api_detected_wait}s")
                    await asyncio.sleep(self.api_detected_wait)
                    has_new_data = await self._wait_for_new_data_fast(page, data_callback, before_scroll_count)
                else:
                    # 未检测到API响应，使用标准等待
                    has_new_data = await self._wait_for_new_data_fast(page, data_callback, before_scroll_count)
            else:
                # 快速数据检查（连续滚动模式）
                has_new_data = await self._wait_for_new_data_fast(page, data_callback, before_scroll_count)

            
            # 统计每次滚动的效率
            after_scroll_count = await self._get_current_data_count(data_callback)
            items_gained = after_scroll_count - before_scroll_count
            
            # 检查是否达到目标数量（在数据更新后检查）
            # 优先检查目标达成状态回调（更可靠）
            if target_check_callback and callable(target_check_callback):
                try:
                    if target_check_callback():
                        logger.success(f"滚动后检查：目标已达成，停止滚动")
                        break
                except Exception as e:
                    logger.debug(f"目标检查回调执行失败: {e}")
            
            # 备用检查：基于数据计数
            if target_count:
                current_collected = after_scroll_count - initial_data_count
                logger.debug(f"滚动后数据检查: 当前收集={current_collected}, 目标={target_count}, 本次新增={items_gained}")
                if current_collected >= target_count:
                    logger.success(f"已达到目标数量 {target_count}，停止滚动（当前收集: {current_collected}）")
                    break
            
            if items_gained > 0:
                self.scroll_efficiency.append(items_gained)
                # 限制列表大小，只保留最近20次记录
                if len(self.scroll_efficiency) > 20:
                    self.scroll_efficiency = self.scroll_efficiency[-20:]
                avg_efficiency = sum(self.scroll_efficiency[-5:]) / len(self.scroll_efficiency[-5:])  # 最近5次的平均值
                
                # 显示更详细的进度信息
                if target_count:
                    remaining = target_count - (after_scroll_count - initial_data_count)
                    if remaining > 0 and avg_efficiency > 0:
                        eta_scrolls = int(remaining / avg_efficiency)
                        logger.info(f"本次获取: {items_gained} 个，平均效率: {avg_efficiency:.1f} 个/次，预计还需: {eta_scrolls} 次滚动")
            
            # 调整滚动策略
            self._adjust_scroll_strategy(has_new_data)
            
            # 检查是否触发了429限流
            if hasattr(data_callback, '__self__') and hasattr(data_callback.__self__, 'anti_detection'):
                if data_callback.__self__.anti_detection.rate_limited:
                    logger.error("🚨 检测到429限流，立即停止滚动并保存数据")
                    break
            
            # 只在达到目标或超过最大滚动次数时停止，不再因为无数据而停止
            # 如果长时间无数据，只是增加滚动距离
            if self.no_data_count >= 10:  # 连续10次无数据时调整策略
                if self.dynamic_scrolling:
                    self.scroll_max = min(3500, self.scroll_max + 500)
                    logger.info(f"长时间无新数据，增加最大滚动距离至: {self.scroll_max} 像素")
                self.no_data_count = 0  # 重置计数器继续尝试
            
            # 自适应延迟
            await asyncio.sleep(self.current_delay)
        
        final_data_count = await self._get_current_data_count(data_callback)
        collected_count = final_data_count - initial_data_count
        
        if target_count:
            success = collected_count >= target_count
            logger.info(f"滚动完成，共滚动 {total_scrolls} 次，收集到 {collected_count}/{target_count} 条数据 ({'成功' if success else '未完成'})")
        else:
            logger.info(f"滚动完成，共滚动 {total_scrolls} 次，收集到 {collected_count} 条新数据")
        
        return collected_count
    
    async def _perform_scroll(self, page: Page) -> None:
        """
        执行滚动操作（支持动态滚动距离）

        Args:
            page: 页面对象
        """
        try:
            # 计算滚动距离
            if self.dynamic_scrolling:
                # 动态滚动距离（随机范围）
                scroll_distance = random.randint(self.scroll_min, self.scroll_max)
                logger.debug(f"使用动态滚动距离: {scroll_distance} 像素")
            else:
                # 固定滚动距离
                scroll_distance = self.scroll_distance
                logger.debug(f"使用固定滚动距离: {scroll_distance} 像素")

            # 使用平滑滚动
            await page.evaluate(f"""
                window.scrollBy({{
                    top: {scroll_distance},
                    behavior: 'smooth'
                }});
            """)

            # 等待滚动完成（快速滚动）
            scroll_wait = random.uniform(0.1, 0.5)
            await asyncio.sleep(scroll_wait)
            logger.debug(f"滚动等待时间: {scroll_wait:.2f}秒")

            # 额外的小幅度滚动，模拟真实用户行为
            if random.random() < 0.2:  # 20%概率进行微调滚动
                micro_scroll = random.randint(-50, 50)
                await page.evaluate(f"window.scrollBy(0, {micro_scroll});")
                await asyncio.sleep(random.uniform(0.05, 0.15))

        except Exception as e:
            logger.error(f"执行滚动时出错: {e}")
    
    async def _wait_for_new_data(self, page: Page, data_callback: Callable, before_count: int) -> bool:
        """
        等待新数据加载（增强版）

        Args:
            page: 页面对象
            data_callback: 数据回调函数
            before_count: 滚动前的数据数量

        Returns:
            bool: 是否有新数据
        """
        start_time = time.time()
        timeout = self.wait_for_response
        check_interval = 0.3  # 更频繁的检查

        logger.debug(f"等待新数据加载，超时时间: {timeout} 秒")

        while time.time() - start_time < timeout:
            current_count = await self._get_current_data_count(data_callback)

            if current_count > before_count:
                new_items = current_count - before_count
                elapsed = time.time() - start_time
                logger.info(f"✅ 检测到新数据: {new_items} 条 (耗时: {elapsed:.1f}s)")

                # 如果有新数据，再等待一小段时间看是否还有更多数据
                await asyncio.sleep(0.5)
                final_count = await self._get_current_data_count(data_callback)
                if final_count > current_count:
                    additional_items = final_count - current_count
                    logger.debug(f"额外获取到 {additional_items} 条数据")

                return True

            # 等待一小段时间再检查
            await asyncio.sleep(check_interval)

        elapsed = time.time() - start_time
        logger.debug(f"⏰ 等待新数据超时 ({elapsed:.1f}s)，未检测到新数据")
        return False

    async def _wait_for_new_data_fast(self, page: Page, data_callback: Callable, before_count: int) -> bool:
        """
        优化的数据等待（智能检测模式）- 修复竞态条件问题

        Args:
            page: 页面对象
            data_callback: 数据回调函数
            before_count: 滚动前的数据数量

        Returns:
            bool: 是否有新数据
        """
        start_time = time.time()
        # 增加等待时间以避免竞态条件
        fast_timeout = max(1.5, self.wait_for_response * 1.5)  # 至少1.5秒，避免API响应延迟
        check_interval = 0.1  # 适中的检查频率

        logger.debug(f"⚡ 智能数据检查，超时时间: {fast_timeout} 秒")

        # 分阶段检查策略
        phase1_timeout = fast_timeout * 0.4  # 前40%时间快速检查
        phase2_timeout = fast_timeout * 0.6  # 后60%时间慢速检查

        # 第一阶段：快速检查
        while time.time() - start_time < phase1_timeout:
            current_count = await self._get_current_data_count(data_callback)

            if current_count > before_count:
                new_items = current_count - before_count
                elapsed = time.time() - start_time
                logger.info(f"⚡ [快速阶段] 检测到新数据: {new_items} 条 (耗时: {elapsed:.3f}s)")
                return True

            await asyncio.sleep(0.05)  # 快速检查间隔

        # 第二阶段：慢速检查（给API更多响应时间）
        while time.time() - start_time < fast_timeout:
            current_count = await self._get_current_data_count(data_callback)

            if current_count > before_count:
                new_items = current_count - before_count
                elapsed = time.time() - start_time
                logger.info(f"⚡ [慢速阶段] 检测到新数据: {new_items} 条 (耗时: {elapsed:.3f}s)")
                return True

            await asyncio.sleep(check_interval)  # 慢速检查间隔

        # 最后再检查一次，确保没有遗漏延迟到达的数据
        await asyncio.sleep(0.3)  # 额外等待300ms
        final_count = await self._get_current_data_count(data_callback)
        if final_count > before_count:
            new_items = final_count - before_count
            elapsed = time.time() - start_time
            logger.info(f"⚡ [延迟检测] 检测到新数据: {new_items} 条 (耗时: {elapsed:.3f}s)")
            return True

        elapsed = time.time() - start_time
        logger.debug(f"⚡ 智能检查完成 ({elapsed:.3f}s)，确认无新数据")
        return False
    
    async def _get_current_data_count(self, data_callback: Callable) -> int:
        """
        获取当前数据数量
        
        Args:
            data_callback: 数据回调函数
            
        Returns:
            int: 当前数据数量
        """
        try:
            # 优先尝试直接调用回调函数
            if callable(data_callback):
                count = data_callback()
                # 如果是协程，等待结果
                if asyncio.iscoroutine(count):
                    count = await count
                return count
            
            # 备用方案：如果是方法，尝试获取collected_data长度
            if hasattr(data_callback, '__self__'):
                crawler = data_callback.__self__
                if hasattr(crawler, 'collected_data'):
                    # 如果有当前关键词，只计算该关键词的数据
                    if hasattr(crawler, 'current_keyword') and crawler.current_keyword:
                        return sum(1 for item in crawler.collected_data 
                                 if item.get('keyword') == crawler.current_keyword)
                    return len(crawler.collected_data)
            
            return 0
        except Exception as e:
            logger.debug(f"获取数据数量时出错: {e}")
            return 0
    
    async def is_page_bottom(self, page: Page) -> bool:
        """
        检测是否到达页面底部（针对懒加载页面优化）
        
        Args:
            page: 页面对象
            
        Returns:
            bool: 是否到达真正的底部（没有更多内容可加载）
        """
        try:
            # 对于拼多多这种懒加载页面，我们需要更智能的检测
            result = await page.evaluate("""
                async () => {
                    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                    const windowHeight = window.innerHeight;
                    const documentHeight = document.documentElement.scrollHeight;
                    
                    // 检查是否接近底部（触发懒加载的区域）
                    const nearBottom = scrollTop + windowHeight >= documentHeight - 200;
                    
                    if (!nearBottom) {
                        return { isBottom: false, reason: 'not_near_bottom' };
                    }
                    
                    // 检查是否有"正在加载"的指示器
                    const loadingIndicators = [
                        '.loading', '.spinner', '.load-more',
                        '[class*="loading"]', '[class*="spinner"]',
                        'text=正在加载', 'text=加载中', 'text=Loading',
                        '.lazy-loading', '.infinite-loading'
                    ];
                    
                    let hasLoadingIndicator = false;
                    for (const selector of loadingIndicators) {
                        if (selector.startsWith('text=')) {
                            const text = selector.substring(5);
                            if (document.body.textContent.includes(text)) {
                                hasLoadingIndicator = true;
                                break;
                            }
                        } else {
                            if (document.querySelector(selector)) {
                                hasLoadingIndicator = true;
                                break;
                            }
                        }
                    }
                    
                    // 检查是否有"没有更多"的指示器
                    const noMoreIndicators = [
                        'text=没有更多', 'text=已经到底了', 'text=暂无更多',
                        'text=No more', 'text=End of results',
                        '.no-more', '.end-of-list', '[class*="no-more"]'
                    ];
                    
                    let hasNoMoreIndicator = false;
                    for (const selector of noMoreIndicators) {
                        if (selector.startsWith('text=')) {
                            const text = selector.substring(5);
                            if (document.body.textContent.includes(text)) {
                                hasNoMoreIndicator = true;
                                break;
                            }
                        } else {
                            if (document.querySelector(selector)) {
                                hasNoMoreIndicator = true;
                                break;
                            }
                        }
                    }
                    
                    return {
                        isBottom: hasNoMoreIndicator && !hasLoadingIndicator,
                        nearBottom: nearBottom,
                        hasLoadingIndicator: hasLoadingIndicator,
                        hasNoMoreIndicator: hasNoMoreIndicator,
                        scrollTop: scrollTop,
                        windowHeight: windowHeight,
                        documentHeight: documentHeight
                    };
                }
            """)
            
            if result['nearBottom']:
                logger.debug(f"接近页面底部 - 加载指示器: {result['hasLoadingIndicator']}, 无更多指示器: {result['hasNoMoreIndicator']}")
            
            if result['isBottom']:
                logger.debug("检测到页面真正底部（有'没有更多'指示器且无加载指示器）")
            
            return result['isBottom']
            
        except Exception as e:
            logger.error(f"检测页面底部时出错: {e}")
            return False

    async def wait_for_lazy_loading(self, page: Page, timeout: float = None) -> bool:
        """
        等待懒加载完成
        
        Args:
            page: 页面对象
            timeout: 超时时间（秒）
            
        Returns:
            bool: 是否成功等待到新内容加载
        """
        try:
            if timeout is None:
                timeout = self.lazy_loading_timeout
            logger.debug(f"等待懒加载完成，超时时间: {timeout}秒")
            start_time = time.time()
            initial_height = await page.evaluate("document.documentElement.scrollHeight")
            
            while time.time() - start_time < timeout:
                # 检查页面高度是否增加（表示有新内容加载）
                current_height = await page.evaluate("document.documentElement.scrollHeight")
                if current_height > initial_height:
                    logger.debug(f"检测到新内容加载，页面高度从 {initial_height} 增加到 {current_height}")
                    return True
                
                # 检查是否有加载指示器消失
                has_loading = await page.evaluate("""
                    () => {
                        const loadingIndicators = [
                            '.loading', '.spinner', '.load-more',
                            '[class*="loading"]', '[class*="spinner"]',
                            'text=正在加载', 'text=加载中', 'text=Loading'
                        ];
                        
                        for (const selector of loadingIndicators) {
                            if (selector.startsWith('text=')) {
                                const text = selector.substring(5);
                                if (document.body.textContent.includes(text)) {
                                    return true;
                                }
                            } else {
                                if (document.querySelector(selector)) {
                                    return true;
                                }
                            }
                        }
                        return false;
                    }
                """)
                
                if not has_loading:
                    logger.debug("加载指示器已消失")
                    # 再等待一小段时间确保内容完全加载
                    await asyncio.sleep(0.5)
                    final_height = await page.evaluate("document.documentElement.scrollHeight")
                    if final_height > initial_height:
                        logger.debug(f"懒加载完成，页面高度增加了 {final_height - initial_height} 像素")
                        return True
                
                await asyncio.sleep(self.content_check_interval)  # 短暂等待后再次检查
            
            logger.debug("懒加载等待超时")
            return False
            
        except Exception as e:
            logger.error(f"等待懒加载时出错: {e}")
            return False
    
    def _adjust_scroll_strategy(self, has_new_data: bool) -> None:
        """
        根据数据获取情况调整滚动策略（增强版）

        Args:
            has_new_data: 是否有新数据
        """
        if has_new_data:
            # 有新数据，重置无数据计数，适当减少延迟
            self.no_data_count = 0
            self.current_delay = max(self.min_delay, self.current_delay - self.delay_increment)
            logger.debug(f"✅ 检测到新数据，调整延迟为: {self.current_delay}秒")
        else:
            # 无新数据，增加计数和延迟
            self.no_data_count += 1
            self.current_delay = min(self.max_delay, self.current_delay + self.delay_increment)

            # 根据连续无数据次数调整策略
            if self.no_data_count == 1:
                logger.debug(f"⚠️ 首次无新数据，调整延迟为: {self.current_delay}秒")
            elif self.no_data_count == 2:
                logger.info(f"⚠️ 连续2次无新数据，调整延迟为: {self.current_delay}秒")
            elif self.no_data_count >= 3:
                logger.warning(f"🚨 连续{self.no_data_count}次无新数据，延迟: {self.current_delay}秒")

                # 如果连续多次无数据，可能需要更大的滚动距离
                if self.dynamic_scrolling and self.no_data_count >= 4:
                    # 更激进的滚动距离增加策略
                    increment = 400 if self.no_data_count >= 6 else 300
                    self.scroll_max = min(2800, self.scroll_max + increment)
                    logger.debug(f"增加最大滚动距离至: {self.scroll_max} 像素 (无数据次数: {self.no_data_count})")
    
    async def _wait_for_api_response_smart(self, page: Page, api_pattern: str = "/proxy/api/search") -> bool:
        """
        智能API响应检测（优化版）

        Args:
            page: 页面对象
            api_pattern: API路径模式

        Returns:
            bool: 是否检测到API响应
        """
        try:
            # 使用配置的超时时间等待API响应
            response = await page.wait_for_response(
                lambda response: api_pattern in response.url and response.status == 200,
                timeout=self.api_response_timeout * 1000  # 转换为毫秒
            )

            logger.debug(f"🚀 快速检测到API响应: {response.url} (状态: {response.status})")
            return True

        except Exception:
            # 超时或其他错误，不记录详细日志
            logger.debug(f"⏰ API响应检测超时 ({self.api_response_timeout}s)")
            return False

    async def _wait_for_api_response(self, page: Page, api_pattern: str = "/proxy/api/search") -> bool:
        """
        等待API响应（内部方法）

        Args:
            page: 页面对象
            api_pattern: API路径模式

        Returns:
            bool: 是否检测到API响应
        """
        try:
            # 等待API响应，使用较短的超时时间
            response = await page.wait_for_response(
                lambda response: api_pattern in response.url,
                timeout=2000  # 2秒超时
            )

            if response.status == 200:
                logger.debug(f"✅ 检测到API响应: {response.url}")
                return True
            else:
                logger.debug(f"⚠️ API响应状态异常: {response.status}")
                return False

        except Exception:
            # 超时或其他错误，不记录日志（避免日志过多）
            return False

    async def wait_for_api_response(self, page: Page, api_pattern: str = "/proxy/api/search") -> bool:
        """
        等待特定API响应
        
        Args:
            page: 页面对象
            api_pattern: API路径模式
            
        Returns:
            bool: 是否检测到API响应
        """
        try:
            # 等待API响应
            response = await page.wait_for_response(
                lambda response: api_pattern in response.url,
                timeout=self.wait_for_response * 1000
            )
            
            if response.status == 200:
                logger.debug(f"检测到API响应: {response.url}")
                return True
            else:
                logger.warning(f"API响应状态异常: {response.status}")
                return False
                
        except Exception as e:
            logger.debug(f"等待API响应超时或出错: {e}")
            return False
    
    def reset_strategy(self) -> None:
        """重置滚动策略"""
        self.current_delay = self.min_delay
        self.no_data_count = 0
        logger.debug("滚动策略已重置")
    
    def get_scroll_stats(self) -> dict:
        """
        获取滚动统计信息（增强版）

        Returns:
            dict: 滚动统计
        """
        avg_efficiency = 0
        if self.scroll_efficiency:
            avg_efficiency = sum(self.scroll_efficiency) / len(self.scroll_efficiency)

        return {
            "current_delay": self.current_delay,
            "no_data_count": self.no_data_count,
            "max_scrolls": self.max_scrolls,
            "scroll_distance": self.scroll_distance,
            "dynamic_scrolling": self.dynamic_scrolling,
            "scroll_range": f"{self.scroll_min}-{self.scroll_max}",
            "avg_efficiency": round(avg_efficiency, 2),
            "total_scrolls_performed": len(self.scroll_efficiency),
            "wait_for_response": self.wait_for_response,
            "smart_api_detection": self.smart_api_detection,
            "api_response_timeout": self.api_response_timeout,
            "api_detected_wait": self.api_detected_wait,
            "delay_range": f"{self.min_delay}-{self.max_delay}s"
        }
    
    def reset_for_new_keyword(self):
        """为新关键词重置状态"""
        self.scroll_count = 0
        self.no_new_items_count = 0
        self.last_item_count = 0
        self.stable_count = 0
        self._last_positions.clear()
        self._same_position_count = 0
        self._last_check_time = None
        # 重置滚动效率记录
        self.scroll_efficiency.clear()
        # 重置延迟
        self.current_delay = self.min_delay
        self.no_data_count = 0
        logger.info("滚动管理器状态已重置")