anti_detection:
  check_interval: 3
  human_behavior:
    enabled: false
  max_sleep_seconds: 2
  risk_indicators:
  - text=网络繁忙
  - text=请稍后再试
  - text=系统繁忙
  - text=访问过于频繁
  - text=请输入验证码
  - text=请求过于频繁
  - text=429
  - \.risk-control
  - \.captcha
  - \.verify-code
  - '#captcha'
  risk_status_codes:
  - 429
  - 403
  - 503
  - 509
  - 520
app:
  author: AI Assistant
  name: 拼多多商品数据爬虫
  version: 2.0.0
browser:
  args:
  - --disable-blink-features=AutomationControlled
  - --no-sandbox
  - --disable-dev-shm-usage
  device_scale_factor: 1.0
  headless: false
  slow_mo: 100
  timeout: 30000
  type: chromium
  viewport:
    height: 1080
    width: 1920
cdp:
  auto_close_browser: false
  custom_browser_path: ''
  debug_port: 9222
  enabled: true
  headless: false
  launch_timeout: 30
  save_login_state: true
  user_data_dir: pdd_user_data_dir
  # 健康检查配置
  health_check:
    enabled: true          # 启用健康检查
    interval: 30          # 检查间隔（秒）
    timeout: 10           # 检查超时（秒）
  # 自动恢复配置  
  recovery:
    max_attempts: 3       # 最大恢复尝试次数
    delay: 5             # 恢复延迟（秒）
  # 会话备份配置
  session_backup:
    enabled: true         # 启用会话备份
    interval: 60         # 备份间隔（秒）
  # 故障转移策略
  failover:
    strategy: restart     # 策略: restart, fallback_to_standard
    fallback_to_standard: true      # 是否允许降级到标准模式
    graceful_degradation: true      # 是否启用优雅降级
export:
  format: csv
  csv_encoding: utf-8-sig
  excel:
    column_mapping:
      activity_type_name: 活动类型
      brand_name: 品牌名称
      category: 商品分类
      comment_count: 评论数
      coupon_price: 券后价(元)
      created_time: 采集时间
      goods_id: 商品ID
      goods_name: 商品名称
      goods_url: 商品链接
      hd_thumb_url: 高清图片
      image_url: 商品图片
      keyword: 搜索关键词
      market_price: 市场价(元)
      marketing_tags: 营销标签
      merchant_type_name: 商家类型
      original_price: 原价(元)
      price: 拼团价(元)
      rating: 评分
      sales: 销量
      sales_tip: 销量描述
      special_text: 特殊信息
      subsidy_info: 补贴详情
      tags: 商品标签
    sheet_name_template: '{keyword}_商品数据'
  filename_template: 拼多多商品数据_{timestamp}.xlsx
  output_dir: ./output
fingerprint:
  audio_fingerprint: true
  canvas_noise_level: 0.1
  enabled: true
  font_fingerprint: true
  rotation_interval: 3600
  webgl_randomization: true
lightweight_fingerprint:
  enabled: true
  rotation_interval: 3600
logging:
  console_output: true
  level: DEBUG
  log_dir: ./logs
  log_file_template: pdd_crawler_{date}.log
  retention_days: 7
memory_monitoring:
  enabled: true                    # 启用内存监控
  monitoring_interval: 30          # 监控间隔（秒）
  auto_gc_enabled: true           # 启用自动垃圾回收
  auto_gc_threshold_mb: 800       # 自动垃圾回收阈值（MB）
  report_interval: 300            # 报告生成间隔（秒，5分钟）
  # 各组件监控阈值配置
  components:
    crawler:
      warning_threshold_mb: 500   # 警告阈值
      critical_threshold_mb: 1000 # 严重阈值
    browser_manager:
      warning_threshold_mb: 400
      critical_threshold_mb: 800
    data_processor:
      warning_threshold_mb: 300
      critical_threshold_mb: 600
    api_monitor:
      warning_threshold_mb: 150
      critical_threshold_mb: 300
    scroll_manager:
      warning_threshold_mb: 100
      critical_threshold_mb: 200
    anti_detection:
      warning_threshold_mb: 100
      critical_threshold_mb: 200
    system:
      warning_threshold_mb: 1200
      critical_threshold_mb: 2000
  # 内存泄漏检测配置
  leak_detection:
    window_size: 10              # 检测窗口大小
    growth_threshold_mb: 50      # 增长阈值（MB）
    leak_probability_threshold: 0.8  # 泄漏概率阈值
login_detection:
  check_interval: 5
  login_indicators:
  - .login-avatar
  - '[data-testid=''user-avatar'']'
  - .user-info
  logout_indicators:
  - .login-btn
  - '[data-testid=''login-button'']'
  - text=登录
performance:
  concurrent_limit: 3
  enable_cache: true
  memory_limit: 512
  request_delay: 5
product_filter:
  algorithm:
    case_sensitive: false
    fuzzy_threshold: 0.8
    number_tolerance: 0.05
    unit_normalization: true
  debug:
    enabled: true
    log_scores: true
    save_filtered: false
  enabled: false
  match_threshold: 0.5    # 进一步降低阈值，让统帅品牌能通过
  strict_mode: false
  weights:
    brand: 0.4
    keyword: 0.1
    product_type: 0.25
    specification: 0.25
  statistical:
    enabled: true
    min_sample_size: 8      # 降低最小样本量
    major_type_threshold: 0.5    # 降低主要类型阈值到50%
    minor_type_threshold: 0.15   # 提高少数类型阈值到15%
    confidence_threshold: 0.8
    log_statistics: true
proxy:
  enabled: false
  password: ''
  server: ''
  username: ''
retry:
  backoff_factor: 1.5
  max_attempts: 2
  max_wait_time: 10
  operation_timeout: 10
  quick_fail_exceptions:
  - LoginRequiredError
  - CookieInvalidError
  - PageStructureChangedError
  total_timeout: 60
  wait_time: 2
scroll:
  adaptive_delay:
    increment: 0.1
    max: 0.5
    min: 0.1
  api_detected_wait: 0.3
  api_response_timeout: 2.0
  dynamic_scrolling: true
  max_scrolls: 50
  no_data_threshold: 8
  scroll_distance: 1800
  scroll_distance_range:
    max: 2200
    min: 1600
  smart_api_detection: true
  wait_for_response: 0.8
  # 懒加载页面配置
  lazy_loading:
    enabled: true                    # 启用懒加载支持
    wait_timeout: 5.0               # 等待懒加载完成的超时时间（秒）
    near_bottom_threshold: 200      # 接近底部的像素阈值
    content_check_interval: 0.2     # 内容检查间隔（秒）
search:
  keywords:
  - 冰箱
  - 手机
  max_pages: 20
  target_count: 60
sorting:
  enabled: false
  retry_on_fail: false
  timeout: 10
  types:
  - default: true
    name: 综合排序
    selector: .sort-item[data-sort='综合']
    value: comprehensive
  - name: 价格从低到高
    selector: .sort-item[data-sort='价格']
    sub_selector: .price-asc
    value: price_asc
  - name: 销量从高到低
    selector: .sort-item[data-sort='销量']
    value: sales_desc
stealth:
  enabled: true
  init_scripts_only: true
  languages:
  - zh-CN
  - zh
  - en
target:
  base_url: https://mobile.yangkeduo.com
  search_api: /proxy/api/search
  search_page: /search_result.html
user_agent:
  browser_weights:
    chrome: 0.7
    firefox: 0.2
    safari: 0.1
  consistency_check: true
  device_weights:
    desktop: 0.4
    mobile: 0.5
    tablet: 0.1
  rotation_interval: 3600

# 资源清理配置
resource_cleanup:
  # 基础设置
  enabled: true                    # 启用资源清理功能
  auto_cleanup_interval: 3600      # 自动清理间隔（秒，1小时）
  cleanup_on_shutdown: true        # 程序关闭时执行清理
  
  # 清理路径配置
  cleanup_paths:
    logs_dir: "./logs"             # 日志目录
    browser_data_dir: "./browser_data"  # 浏览器数据目录
    temp_dir: "./temp"             # 临时文件目录
    output_dir: "./output"         # 导出文件目录
    cache_dir: "./cache"           # 缓存目录
  
  # 保留策略配置
  retention_policies:
    # 日志文件保留策略
    logs:
      days: 7                      # 保留天数
      max_size_mb: 100             # 单个文件最大大小(MB)
      compress_old: true           # 压缩旧文件
    
    # 浏览器缓存保留策略
    browser_cache:
      days: 3                      # 保留天数
      max_size_mb: 500             # 缓存目录最大大小(MB)
      aggressive_cleanup: false    # 激进清理模式
    
    # 临时文件保留策略
    temp_files:
      days: 1                      # 保留天数
      max_size_mb: 50              # 临时文件最大大小(MB)
      extensions: [".tmp", ".temp", ".lock", "~"]  # 清理的文件扩展名
    
    # 导出文件保留策略
    export_files:
      days: 30                     # 保留天数
      max_count: 100               # 最大文件数量
      keep_recent: 10              # 始终保留最近的文件数量
    
    # 截图文件保留策略
    screenshots:
      days: 7                      # 保留天数
      max_count: 50                # 最大文件数量
      file_patterns: ["*.png", "*.jpg", "*.jpeg", "*.gif"]  # 截图文件模式
  
  # 清理策略配置
  cleanup_strategies:
    # 定时清理策略
    scheduled:
      enabled: true
      times: ["02:00", "14:00"]    # 定时清理时间点
      
    # 磁盘空间监控
    disk_monitoring:
      enabled: true
      threshold_mb: 1000           # 磁盘空间告警阈值(MB)
      emergency_threshold_mb: 500  # 紧急清理阈值(MB)
      
    # 内存监控清理
    memory_monitoring:
      enabled: true
      threshold_mb: 800            # 内存使用告警阈值(MB)
      auto_cleanup: true           # 自动清理内存
  
  # 清理日志配置
  logging:
    enabled: true                  # 启用清理日志
    detailed: true                 # 详细日志
    save_stats: true               # 保存清理统计
