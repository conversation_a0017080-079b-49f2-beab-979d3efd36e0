import React, { useState, useCallback } from 'react';
import {
  Card,
  Upload,
  Input,
  Button,
  Tabs,
  Alert,
  Space,
  Typography,
  Divider,
  Badge,
  Tag,
  Row,
  Col
} from 'antd';
import {
  UploadOutlined,
  FileTextOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import type { UploadProps } from 'antd';
import type { CookieImportProps, FormatDetectionResult } from './types';
import { detectAndParseCookies, validatePDDCookies } from './utils';

const { TextArea } = Input;
const { Text, Paragraph } = Typography;
// const { TabPane } = Tabs; // 新版本Antd已移除TabPane

const CookieImport: React.FC<CookieImportProps> = ({ onImport, loading = false }) => {
  const [activeTab, setActiveTab] = useState<string>('file');
  const [textInput, setTextInput] = useState<string>('');
  const [previewData, setPreviewData] = useState<FormatDetectionResult | null>(null);
  const [validationResult, setValidationResult] = useState<{
    valid: boolean;
    missing: string[];
    expired: string[];
  } | null>(null);

  // 文件上传处理
  const handleFileUpload: UploadProps['customRequest'] = useCallback(async (options: any) => {
    const { file, onSuccess, onError } = options;
    
    try {
      const text = await readFileAsText(file as File);
      const result = detectAndParseCookies(text);
      
      if (result.data.length === 0) {
        onError?.(new Error('无法从文件中解析出有效的Cookie数据'));
        return;
      }
      
      setPreviewData(result);
      const validation = validatePDDCookies(result.data);
      setValidationResult(validation);
      
      onSuccess?.(result);
    } catch (error) {
      onError?.(error as Error);
    }
  }, []);

  // 读取文件为文本
  const readFileAsText = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = () => reject(new Error('文件读取失败'));
      reader.readAsText(file, 'utf-8');
    });
  };

  // 文本输入处理
  const handleTextInput = useCallback((value: string) => {
    setTextInput(value);
    
    if (value.trim()) {
      const result = detectAndParseCookies(value.trim());
      setPreviewData(result);
      
      if (result.data.length > 0) {
        const validation = validatePDDCookies(result.data);
        setValidationResult(validation);
      } else {
        setValidationResult(null);
      }
    } else {
      setPreviewData(null);
      setValidationResult(null);
    }
  }, []);

  // 执行导入
  const handleImport = useCallback(async () => {
    if (!previewData || previewData.data.length === 0) {
      return;
    }

    try {
      await onImport({
        cookies: previewData.data
      });
      
      // 清空数据
      setTextInput('');
      setPreviewData(null);
      setValidationResult(null);
    } catch (error) {
      console.error('导入失败:', error);
    }
  }, [previewData, onImport]);

  // 渲染格式检测结果
  const renderFormatDetection = () => {
    if (!previewData) return null;

    const formatNames = {
      'json': 'JSON 格式',
      'netscape': 'Netscape 格式',
      'browser-export': '浏览器导出格式',
      'cookie-string': 'Cookie 字符串'
    };

    const confidenceColor = previewData.confidence > 0.8 ? 'success' : 
                           previewData.confidence > 0.5 ? 'warning' : 'error';

    return (
      <Alert
        message="格式检测结果"
        description={
          <Space direction="vertical" size="small" style={{ width: '100%' }}>
            <Row gutter={16}>
              <Col span={12}>
                <Text strong>检测到的格式：</Text>
                <Tag color="blue" style={{ marginLeft: 8 }}>
                  {formatNames[previewData.format]}
                </Tag>
              </Col>
              <Col span={12}>
                <Text strong>置信度：</Text>
                <Badge 
                  status={confidenceColor} 
                  text={`${(previewData.confidence * 100).toFixed(0)}%`}
                  style={{ marginLeft: 8 }}
                />
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Text strong>解析出的Cookie数：</Text>
                <Text style={{ marginLeft: 8 }}>{previewData.data.length}</Text>
              </Col>
              <Col span={12}>
                <Text strong>有效性检查：</Text>
                {validationResult && (
                  <Tag 
                    color={validationResult.valid ? 'success' : 'error'}
                    style={{ marginLeft: 8 }}
                  >
                    {validationResult.valid ? '通过' : '未通过'}
                  </Tag>
                )}
              </Col>
            </Row>
          </Space>
        }
        type="info"
        showIcon
        style={{ marginBottom: 16 }}
      />
    );
  };

  // 渲染验证结果
  const renderValidationResult = () => {
    if (!validationResult) return null;

    if (validationResult.valid) {
      return (
        <Alert
          message="Cookie 验证通过"
          description="检测到所有必需的拼多多Cookie，可以正常使用。"
          type="success"
          showIcon
          icon={<CheckCircleOutlined />}
          style={{ marginBottom: 16 }}
        />
      );
    }

    return (
      <Alert
        message="Cookie 验证警告"
        description={
          <Space direction="vertical" size="small">
            {validationResult.missing.length > 0 && (
              <Text>
                缺少必需的Cookie: <Text code>{validationResult.missing.join(', ')}</Text>
              </Text>
            )}
            {validationResult.expired.length > 0 && (
              <Text>
                已过期的Cookie: <Text code>{validationResult.expired.join(', ')}</Text>
              </Text>
            )}
            <Text type="secondary">
              建议重新获取完整的Cookie数据
            </Text>
          </Space>
        }
        type="warning"
        showIcon
        icon={<ExclamationCircleOutlined />}
        style={{ marginBottom: 16 }}
      />
    );
  };

  // 渲染Cookie预览
  const renderCookiePreview = () => {
    if (!previewData || previewData.data.length === 0) return null;

    const importantCookies = previewData.data.filter(
      cookie => ['PDDAccessToken', 'pdd_user_id'].includes(cookie.name)
    );

    return (
      <Card 
        title="Cookie 预览" 
        size="small" 
        style={{ marginBottom: 16 }}
        extra={
          <Badge count={previewData.data.length} showZero>
            <Text>总数</Text>
          </Badge>
        }
      >
        {importantCookies.length > 0 && (
          <>
            <Paragraph>
              <Text strong>重要Cookie：</Text>
            </Paragraph>
            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              {importantCookies.map((cookie, index) => (
                <Card key={index} size="small" bodyStyle={{ padding: '8px 12px' }}>
                  <Row gutter={16}>
                    <Col span={6}>
                      <Text strong>{cookie.name}</Text>
                    </Col>
                    <Col span={6}>
                      <Text ellipsis style={{ maxWidth: 120 }} title={cookie.value}>
                        {cookie.value}
                      </Text>
                    </Col>
                    <Col span={6}>
                      <Text type="secondary">{cookie.domain}</Text>
                    </Col>
                    <Col span={6}>
                      <Tag color={cookie.expires ? 'blue' : 'default'}>
                        {cookie.expires ? new Date(cookie.expires * 1000).toLocaleDateString() : '会话'}
                      </Tag>
                    </Col>
                  </Row>
                </Card>
              ))}
            </Space>
            <Divider />
          </>
        )}
        <Paragraph>
          <Text type="secondary">
            共检测到 {previewData.data.length} 个Cookie，其中包含 {importantCookies.length} 个重要Cookie。
          </Text>
        </Paragraph>
      </Card>
    );
  };

  return (
    <Card title="Cookie 导入" style={{ marginBottom: 24 }}>
      <Tabs 
        activeKey={activeTab} 
        onChange={setActiveTab}
        items={[
          {
            key: 'file',
            label: (
              <span>
                <UploadOutlined />
                文件上传
              </span>
            ),
            children: (
              <Space direction="vertical" size="large" style={{ width: '100%' }}>
                <Alert
                  message="支持的文件格式"
                  description={
                    <ul style={{ marginBottom: 0, paddingLeft: 20 }}>
                      <li>JSON 格式的Cookie文件</li>
                      <li>浏览器导出的Cookie文件</li>
                      <li>Netscape HTTP Cookie格式</li>
                      <li>其他包含Cookie数据的文本文件</li>
                    </ul>
                  }
                  type="info"
                  showIcon
                  icon={<InfoCircleOutlined />}
                />

                <Upload
                  customRequest={handleFileUpload}
                  showUploadList={false}
                  accept=".json,.txt,.cookies"
                  disabled={loading}
                >
                  <Button 
                    icon={<UploadOutlined />} 
                    size="large" 
                    loading={loading}
                    block
                  >
                    选择Cookie文件
                  </Button>
                </Upload>
              </Space>
            )
          },
          {
            key: 'text',
            label: (
              <span>
                <FileTextOutlined />
                文本粘贴
              </span>
            ),
            children: (
              <Space direction="vertical" size="large" style={{ width: '100%' }}>
                <Alert
                  message="支持的文本格式"
                  description="支持粘贴JSON格式、浏览器开发者工具复制的Cookie字符串、或者其他包含Cookie信息的文本格式。系统会自动识别格式并解析。"
                  type="info"
                  showIcon
                  icon={<InfoCircleOutlined />}
                />

                <TextArea
                  placeholder="请粘贴Cookie数据..."
                  rows={8}
                  value={textInput}
                  onChange={(e) => handleTextInput(e.target.value)}
                  disabled={loading}
                />
              </Space>
            )
          }
        ]}
      />

      {/* 格式检测结果 */}
      {renderFormatDetection()}

      {/* 验证结果 */}
      {renderValidationResult()}

      {/* Cookie预览 */}
      {renderCookiePreview()}

      {/* 导入按钮 */}
      {previewData && previewData.data.length > 0 && (
        <div style={{ textAlign: 'center', marginTop: 16 }}>
          <Button
            type="primary"
            size="large"
            loading={loading}
            onClick={handleImport}
            icon={<CheckCircleOutlined />}
          >
            导入 {previewData.data.length} 个Cookie
          </Button>
        </div>
      )}
    </Card>
  );
};

export default CookieImport;