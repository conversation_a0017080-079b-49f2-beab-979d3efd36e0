import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  Steps,
  Row,
  Col,
  Alert,
  Button,
  Space,
  Typography,
  Tabs,
  Badge,
  notification
} from 'antd';
import {
  PlayCircleOutlined,
  MonitorOutlined,
  HistoryOutlined,
  ThunderboltOutlined,
  SettingOutlined,
  KeyOutlined,
  SearchOutlined
} from '@ant-design/icons';
import { useAppStore } from '@/stores/appStore';
import { useApi } from '@/hooks/useApi';
import { useWebSocket } from '@/hooks/useWebSocket';
import CookieManager from '@/components/CookieManager';
import SearchConfig from '@/components/SearchConfig';
import ProgressMonitor from '@/components/ProgressMonitor';
import DataPreview from '@/components/DataPreview';
import TaskControl from './TaskControl';
import TaskList from './TaskList';
import TaskHistory from './TaskHistory';
import QuickStart from './QuickStart';
import type { 
  ControlPanelState, 
  TaskInfo, 
  TaskControlActions,
  StepValidation 
} from './types';
import type { SearchConfig as SearchConfigType } from '@/types';
import {
  validateCookieStep,
  validateConfigStep,
  generateTaskName,
  storage
} from './utils';

const { Title } = Typography;
const { Step } = Steps;

interface CrawlControlPanelProps {
  defaultStep?: 'cookie' | 'config' | 'start' | 'monitor';
  className?: string;
}

const CrawlControlPanel: React.FC<CrawlControlPanelProps> = ({
  defaultStep = 'cookie',
  className
}) => {
  // 状态管理
  const [controlState, setControlState] = useState<ControlPanelState>({
    currentStep: defaultStep,
    isValidated: {
      cookie: false,
      config: false
    },
    quickConfigs: [],
    activeTasks: [],
    taskHistory: []
  });

  const [currentTask, setCurrentTask] = useState<TaskInfo | null>(null);
  const [activeTab, setActiveTab] = useState('control');
  const [stepValidations, setStepValidations] = useState<Record<string, StepValidation>>({});
  const [loading, setLoading] = useState({
    cookie: false,
    config: false,
    starting: false,
    stopping: false
  });

  // Store 和 hooks
  const {
    searchConfig,
    products,
    loading: storeLoading,
    error,
    setSearchConfig
  } = useAppStore();

  const { 
    startCrawl, 
    stopCrawl, 
    fetchCrawlStatus 
  } = useApi();

  const { isConnected, reconnect } = useWebSocket(currentTask?.id);

  // 验证 Cookie 步骤
  const validateCookie = useCallback(async () => {
    setLoading(prev => ({ ...prev, cookie: true }));
    
    try {
      const validation = await validateCookieStep();
      setStepValidations(prev => ({ ...prev, cookie: validation }));
      setControlState(prev => ({
        ...prev,
        isValidated: { ...prev.isValidated, cookie: validation.valid }
      }));
      
      if (validation.valid) {
        notification.success({
          message: 'Cookie 验证成功',
          description: validation.message
        });
      } else {
        notification.error({
          message: 'Cookie 验证失败',
          description: validation.message
        });
      }
    } catch (error) {
      const validation = {
        valid: false,
        message: '验证过程中发生错误',
        details: [(error as Error).message]
      };
      setStepValidations(prev => ({ ...prev, cookie: validation }));
    } finally {
      setLoading(prev => ({ ...prev, cookie: false }));
    }
  }, []);

  // 验证配置步骤
  const validateConfig = useCallback(() => {
    const validation = validateConfigStep(searchConfig);
    setStepValidations(prev => ({ ...prev, config: validation }));
    setControlState(prev => ({
      ...prev,
      isValidated: { ...prev.isValidated, config: validation.valid }
    }));

    if (!validation.valid) {
      notification.error({
        message: '配置验证失败',
        description: validation.message,
        duration: 5
      });
    }

    return validation.valid;
  }, [searchConfig]);

  // 自动验证 - 只在组件挂载时执行一次
  useEffect(() => {
    validateCookie();
  }, []); // 空依赖数组，只在挂载时执行

  useEffect(() => {
    if (searchConfig.keywords && searchConfig.keywords.length > 0) {
      validateConfig();
    }
  }, [searchConfig.keywords]); // 只依赖keywords变化

  // 任务控制动作
  const taskActions: TaskControlActions = {
    start: async (config: SearchConfigType) => {
      setLoading(prev => ({ ...prev, starting: true }));
      
      try {
        const result = await startCrawl(config);
        
        if (result?.task_id) {
          const newTask: TaskInfo = {
            id: result.task_id,
            name: generateTaskName(config),
            config,
            status: 'running',
            createdAt: new Date().toISOString(),
            startedAt: new Date().toISOString(),
            progress: {
              currentPage: 0,
              totalPages: 0,
              collectedCount: 0,
              failedCount: 0
            }
          };
          
          setCurrentTask(newTask);
          setControlState(prev => ({
            ...prev,
            currentStep: 'monitor',
            activeTasks: [newTask, ...prev.activeTasks]
          }));
          
          setActiveTab('monitor');
          
          notification.success({
            message: '爬虫任务已启动',
            description: `任务ID: ${result.task_id}`
          });
          
          return result.task_id;
        }
        
        throw new Error('未获取到任务ID');
      } catch (error) {
        notification.error({
          message: '启动任务失败',
          description: (error as Error).message
        });
        throw error;
      } finally {
        setLoading(prev => ({ ...prev, starting: false }));
      }
    },

    pause: async (_taskId: string) => {
      // 实现暂停逻辑
      throw new Error('暂停功能暂未实现');
    },

    resume: async (_taskId: string) => {
      // 实现恢复逻辑
      throw new Error('恢复功能暂未实现');
    },

    stop: async (taskId: string) => {
      setLoading(prev => ({ ...prev, stopping: true }));
      
      try {
        await stopCrawl();
        
        if (currentTask) {
          const stoppedTask = {
            ...currentTask,
            status: 'cancelled' as const,
            completedAt: new Date().toISOString()
          };
          
          setCurrentTask(null);
          storage.saveTaskHistory(stoppedTask);
          
          setControlState(prev => ({
            ...prev,
            activeTasks: prev.activeTasks.filter(t => t.id !== taskId),
            taskHistory: [stoppedTask, ...prev.taskHistory]
          }));
        }
        
        notification.success({
          message: '任务已停止',
          description: '爬虫任务已成功停止'
        });
      } catch (error) {
        notification.error({
          message: '停止任务失败',
          description: (error as Error).message
        });
        throw error;
      } finally {
        setLoading(prev => ({ ...prev, stopping: false }));
      }
    },

    cancel: async (taskId: string) => {
      return taskActions.stop(taskId);
    },

    retry: async (_taskId: string) => {
      if (currentTask) {
        return taskActions.start(currentTask.config);
      }
      throw new Error('无任务可重试');
    }
  };

  // 步骤点击处理
  const handleStepClick = (step: number) => {
    const stepMap = ['cookie', 'config', 'start', 'monitor'] as const;
    const targetStep = stepMap[step];
    
    if (step === 0 || controlState.isValidated.cookie) {
      setControlState(prev => ({ ...prev, currentStep: targetStep }));
    } else if (step === 1 && !controlState.isValidated.cookie) {
      notification.warning({
        message: '请先验证 Cookie',
        description: '在配置搜索参数之前，需要先验证 Cookie 状态'
      });
    }
  };

  // 快速启动处理
  const handleQuickStart = (config: SearchConfigType) => {
    // 更新搜索配置
    setSearchConfig(config);
    
    // 验证并启动
    const configValid = validateConfigStep(config);
    if (configValid.valid && controlState.isValidated.cookie) {
      taskActions.start(config);
    } else {
      notification.warning({
        message: '无法启动',
        description: '请确保 Cookie 验证通过且配置正确'
      });
    }
  };

  // 获取当前步骤
  const getCurrentStepIndex = () => {
    const stepMap = { cookie: 0, config: 1, start: 2, monitor: 3 };
    return stepMap[controlState.currentStep];
  };

  // 获取步骤状态
  const getStepStatus = (step: string) => {
    if (step === 'cookie') {
      return controlState.isValidated.cookie ? 'finish' : 
             stepValidations.cookie && !stepValidations.cookie.valid ? 'error' : 'wait';
    }
    if (step === 'config') {
      return controlState.isValidated.config ? 'finish' : 
             stepValidations.config && !stepValidations.config.valid ? 'error' : 'wait';
    }
    if (step === 'start') {
      return currentTask ? 'finish' : 'wait';
    }
    if (step === 'monitor') {
      return currentTask && currentTask.status === 'running' ? 'process' : 'wait';
    }
    return 'wait';
  };

  return (
    <div className={className}>
      <Title level={2}>爬取控制面板</Title>

      {/* 全局错误提示 */}
      {error && (
        <Alert
          message="系统错误"
          description={error}
          type="error"
          closable
          style={{ marginBottom: '16px' }}
        />
      )}

      {/* WebSocket 连接状态 */}
      <Alert
        message={
          <Space>
            <span>WebSocket 连接状态:</span>
            <Badge 
              status={isConnected ? 'success' : 'error'} 
              text={isConnected ? '已连接' : '未连接'} 
            />
            {!isConnected && (
              <Button size="small" onClick={reconnect}>
                重新连接
              </Button>
            )}
          </Space>
        }
        type={isConnected ? 'success' : 'warning'}
        style={{ marginBottom: '16px' }}
        showIcon
      />

      {/* 步骤指示器 */}
      <Card style={{ marginBottom: '16px' }}>
        <Steps 
          current={getCurrentStepIndex()}
          type="navigation"
          onChange={handleStepClick}
          style={{ marginBottom: '20px' }}
        >
          <Step
            title="Cookie 验证"
            icon={<KeyOutlined />}
            status={getStepStatus('cookie')}
            description={
              controlState.isValidated.cookie ? '验证通过' : 
              stepValidations.cookie ? stepValidations.cookie.message : '等待验证'
            }
          />
          <Step
            title="搜索配置"
            icon={<SearchOutlined />}
            status={getStepStatus('config')}
            description={
              controlState.isValidated.config ? '配置就绪' : '等待配置'
            }
          />
          <Step
            title="启动任务"
            icon={<PlayCircleOutlined />}
            status={getStepStatus('start')}
            description={currentTask ? '任务运行中' : '准备启动'}
          />
          <Step
            title="监控数据"
            icon={<MonitorOutlined />}
            status={getStepStatus('monitor')}
            description={
              currentTask?.status === 'running' ? '实时监控中' : '等待启动'
            }
          />
        </Steps>

        {/* 任务控制按钮 */}
        <div style={{ textAlign: 'center' }}>
          <TaskControl
            task={currentTask || undefined}
            loading={{
              starting: loading.starting,
              stopping: loading.stopping
            }}
            disabled={!controlState.isValidated.cookie || !controlState.isValidated.config || !isConnected}
            actions={taskActions}
            onStart={() => {
              if (validateConfig()) {
                taskActions.start(searchConfig);
              }
            }}
          />
        </div>
      </Card>

      {/* 标签页内容 */}
      <Tabs 
        activeKey={activeTab} 
        onChange={setActiveTab}
        type="card"
        size="large"
        items={[
          {
            label: (
              <Space>
                <SettingOutlined />
                控制面板
              </Space>
            ),
            key: 'control',
            children: (
              <Row gutter={[16, 16]}>
                {/* Cookie 管理 */}
                <Col span={24}>
                  <Card title="Cookie 管理" extra={
                    <Badge 
                      status={cookieValid ? 'success' : 'error'} 
                      text={cookieValid ? '已验证' : '未验证'} 
                    />
                  }>
                    <CookieManager
                      onCookieUpdated={handleCookieUpdated}
                      disabled={isSearching}
                    />
                  </Card>
                </Col>

                {/* 搜索配置 */}
                <Col span={24}>
                  <Card title="搜索配置">
                    <SearchConfig
                      onStartSearch={handleStartSearch}
                      disabled={!cookieValid || isSearching}
                    />
                  </Card>
                </Col>
              </Row>
            )
          },
          {
            label: (
              <Space>
                <MonitorOutlined />
                实时监控
                {currentTask && (
                  <Badge 
                    status={currentTask.status === 'running' ? 'processing' : 'default'} 
                  />
                )}
              </Space>
            ),
            key: 'monitor',
            children: currentTask ? (
              <ProgressMonitor 
                taskId={currentTask.id}
                showAdvanced={true}
                refreshInterval={1000}
              />
            ) : (
              <Card>
                <div style={{ textAlign: 'center', padding: '60px 0' }}>
                  <Typography.Text type="secondary">
                    暂无运行中的任务
                  </Typography.Text>
                  <br />
                  <Button 
                    type="link" 
                    onClick={() => setActiveTab('control')}
                    style={{ marginTop: 16 }}
                  >
                    前往控制面板启动任务
                  </Button>
                </div>
              </Card>
            )
          },
          {
            label: (
              <Space>
                <ThunderboltOutlined />
                数据预览
                <Badge count={totalProductCount} overflowCount={9999} />
              </Space>
            ),
            key: 'preview',
            children: (
              <DataPreview 
                height={600}
              />
            )
          },
          {
            label: (
              <Space>
                <SearchOutlined />
                任务管理
                {allTasks.length > 0 && (
                  <Badge count={allTasks.length} />
                )}
              </Space>
            ),
            key: 'tasks',
            children: (
              <TaskList 
                tasks={allTasks}
                onTaskSelect={handleTaskSelect}
                selectedTaskId={currentTask?.id}
              />
            )
          },
          {
            label: (
              <Space>
                <HistoryOutlined />
                历史记录
              </Space>
            ),
            key: 'history',
            children: (
              <TaskHistory 
                onTaskLoad={handleTaskLoad}
                actions={taskActions}
              />
            )
          }
        ]}
      />
            <Space>
              <SettingOutlined />
              控制面板
            </Space>
          }
          key="control"
        >
          <Row gutter={[16, 16]}>
            {/* Cookie 管理 */}
            <Col xs={24} lg={12}>
              <Card 
                title="Cookie 管理" 
                size="small"
                extra={
                  <Button 
                    size="small" 
                    loading={loading.cookie}
                    onClick={validateCookie}
                  >
                    重新验证
                  </Button>
                }
              >
                <CookieManager />
              </Card>
            </Col>

            {/* 搜索配置 */}
            <Col xs={24} lg={12}>
              <Card title="搜索配置" size="small">
                <SearchConfig />
              </Card>
            </Col>
          </Row>

          {/* 快速启动 */}
          <Card 
            title={
              <Space>
                <ThunderboltOutlined />
                快速启动
              </Space>
            }
            style={{ marginTop: '16px' }}
            size="small"
          >
            <QuickStart
              onStart={handleQuickStart}
              currentConfig={searchConfig}
              loading={loading.starting}
            />
          </Card>
        </TabPane>

        {/* 实时监控 */}
        <TabPane
          tab={
            <Space>
              <MonitorOutlined />
              实时监控
              {currentTask && (
                <Badge 
                  status={currentTask.status === 'running' ? 'processing' : 'default'} 
                />
              )}
            </Space>
          }
          key="monitor"
        >
          {currentTask ? (
            <ProgressMonitor 
              taskId={currentTask.id}
              showAdvanced={true}
              refreshInterval={1000}
            />
          ) : (
            <Card>
              <div style={{ textAlign: 'center', padding: '60px 0' }}>
                <MonitorOutlined style={{ fontSize: '64px', color: '#d9d9d9', marginBottom: '16px' }} />
                <div style={{ fontSize: '16px', color: '#666', marginBottom: '16px' }}>
                  暂无运行中的任务
                </div>
                <Button 
                  type="primary" 
                  onClick={() => setActiveTab('control')}
                >
                  配置并启动任务
                </Button>
              </div>
            </Card>
          )}
        </TabPane>

        {/* 数据预览 */}
        <TabPane
          tab={
            <Space>
              <SearchOutlined />
              数据预览
              {products.length > 0 && (
                <Badge count={products.length} style={{ backgroundColor: '#52c41a' }} />
              )}
            </Space>
          }
          key="preview"
        >
          <DataPreview
            data={products}
            loading={storeLoading.fetching}
            onRefresh={fetchCrawlStatus}
            height={600}
          />
        </TabPane>

        {/* 任务管理 */}
        <TabPane
          tab={
            <Space>
              <PlayCircleOutlined />
              任务管理
              {controlState.activeTasks.length > 0 && (
                <Badge count={controlState.activeTasks.length} />
              )}
            </Space>
          }
          key="tasks"
        >
          <TaskList
            tasks={controlState.activeTasks}
            actions={taskActions}
            onTaskSelect={setCurrentTask}
            selectedTaskId={currentTask?.id}
          />
        </TabPane>

        {/* 历史记录 */}
        <TabPane
          tab={
            <Space>
              <HistoryOutlined />
              历史记录
            </Space>
          }
          key="history"
        >
          <TaskHistory
            onRetryTask={(_taskId) => setActiveTab('monitor')}
            actions={taskActions}
          />
        </TabPane>
      </Tabs>
    </div>
  );
};

export default CrawlControlPanel;