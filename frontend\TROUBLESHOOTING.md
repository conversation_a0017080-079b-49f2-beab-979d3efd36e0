# 故障排除指南

## 常见问题快速解决

本文档提供前端系统常见问题的诊断和解决方案。

## 🚀 启动问题

### 1. Node.js环境问题

#### 问题症状
- 运行启动脚本时报错"未找到Node.js"
- npm命令无法执行

#### 解决方案
```bash
# 检查Node.js是否安装
node --version

# 如果未安装，请访问 https://nodejs.org/ 下载安装
# 推荐安装LTS版本（18.x或更高）

# 检查npm版本
npm --version

# 如果npm版本过低，升级npm
npm install -g npm@latest
```

#### 验证安装
```bash
# 确认版本满足要求
node --version    # 应该 >= 16.0.0
npm --version     # 应该 >= 8.0.0
```

### 2. 依赖安装失败

#### 问题症状
- `npm install` 执行失败
- 报告网络超时或权限错误
- 某些包安装失败

#### 解决方案

**网络问题**:
```bash
# 切换到国内镜像源
npm config set registry https://registry.npmmirror.com/

# 或使用cnpm
npm install -g cnpm --registry=https://registry.npmmirror.com/
cnpm install
```

**权限问题（Linux/macOS）**:
```bash
# 使用sudo（不推荐）
sudo npm install

# 推荐：配置npm全局目录
mkdir ~/.npm-global
npm config set prefix '~/.npm-global'
echo 'export PATH=~/.npm-global/bin:$PATH' >> ~/.profile
source ~/.profile
```

**缓存问题**:
```bash
# 清除npm缓存
npm cache clean --force

# 删除node_modules重新安装
rm -rf node_modules
rm package-lock.json
npm install
```

### 3. 端口占用问题

#### 问题症状
- 启动时提示端口5173已被占用
- 服务无法正常启动

#### 解决方案

**Windows**:
```bash
# 查看端口占用
netstat -ano | findstr :5173

# 杀死占用进程（替换PID）
taskkill /PID <PID> /F
```

**Linux/macOS**:
```bash
# 查看端口占用
lsof -i :5173

# 杀死占用进程
kill -9 <PID>
```

**使用其他端口**:
```bash
# 修改vite.config.ts中的端口配置
npm run dev -- --port 3000
```

## 🌐 网络连接问题

### 1. WebSocket连接失败

#### 问题症状
- 前端显示"连接已断开"
- WebSocket连接状态为红色
- 实时数据无法更新

#### 诊断步骤
```javascript
// 在浏览器控制台执行
const ws = new WebSocket('ws://localhost:8001/ws');
ws.onopen = () => console.log('WebSocket连接成功');
ws.onerror = (error) => console.error('WebSocket连接失败:', error);
```

#### 解决方案

**检查后端服务**:
```bash
# 确认后端服务运行在8001端口
curl http://localhost:8001/api/health

# 或在浏览器访问
# http://localhost:8001/api/health
```

**防火墙问题**:
```bash
# Windows防火墙
# 控制面板 -> 系统和安全 -> Windows Defender防火墙
# 允许应用通过防火墙 -> 添加Node.js

# Linux iptables
sudo iptables -A INPUT -p tcp --dport 8001 -j ACCEPT
```

**代理配置问题**:
检查`vite.config.ts`中的代理配置：
```typescript
server: {
  proxy: {
    '/api': {
      target: 'http://localhost:8001',
      changeOrigin: true
    },
    '/ws': {
      target: 'ws://localhost:8001',
      ws: true
    }
  }
}
```

### 2. API请求失败

#### 问题症状
- 接口返回404或500错误
- 请求超时
- CORS跨域错误

#### 诊断工具
1. **浏览器开发者工具**
   - F12 -> Network面板
   - 查看失败的请求详情

2. **使用curl测试**
```bash
# 测试健康检查接口
curl -v http://localhost:8001/api/health

# 测试POST接口
curl -X POST http://localhost:8001/api/crawl/start \
  -H "Content-Type: application/json" \
  -d '{"keywords":["测试"]}'
```

#### 解决方案

**CORS错误**:
确认后端CORS配置允许前端域名：
```python
# 后端应该配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173", "http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
```

**请求超时**:
增加超时时间：
```typescript
// 在api.ts中调整
this.client = axios.create({
  baseURL: '/api',
  timeout: 30000, // 增加到30秒
});
```

## 🎨 界面显示问题

### 1. 页面白屏

#### 问题症状
- 浏览器显示空白页面
- 控制台有JavaScript错误

#### 诊断步骤
1. **检查控制台错误**
   - F12 -> Console面板
   - 查看红色错误信息

2. **检查网络请求**
   - F12 -> Network面板
   - 确认资源加载情况

#### 解决方案

**JavaScript错误**:
```bash
# 重新构建项目
npm run build
npm run preview

# 或清理缓存重启
rm -rf node_modules
npm install
npm run dev
```

**资源加载失败**:
```bash
# 检查静态资源
ls -la public/
ls -la src/assets/

# 确认文件路径正确
```

### 2. 样式异常

#### 问题症状
- 界面布局错乱
- 颜色、字体不正确
- 响应式布局失效

#### 解决方案

**Ant Design样式问题**:
```typescript
// 确认正确导入Ant Design样式
// 在main.tsx中
import 'antd/dist/reset.css';
```

**CSS模块问题**:
```typescript
// 检查CSS导入
import styles from './Component.module.css';

// 使用className
<div className={styles.container}>
```

**浏览器兼容性**:
```bash
# 检查浏览器版本
# Chrome: chrome://version/
# Firefox: about:support

# 推荐使用最新版本浏览器
```

### 3. 数据显示异常

#### 问题症状
- 表格数据为空
- 图表不显示
- 分页功能异常

#### 诊断步骤
```javascript
// 在组件中添加调试日志
console.log('API响应数据:', data);
console.log('表格数据源:', dataSource);
```

#### 解决方案

**数据格式问题**:
```typescript
// 检查API响应格式
interface ApiResponse<T> {
  success: boolean;
  data: T;
  message: string;
}

// 确认数据结构匹配
```

**状态管理问题**:
```typescript
// 检查Zustand store状态
import { useAppStore } from '@/stores/appStore';

const MyComponent = () => {
  const { products, setProducts } = useAppStore();
  
  // 调试状态
  console.log('当前状态:', products);
};
```

## 🔧 性能问题

### 1. 页面加载缓慢

#### 问题症状
- 首页加载时间超过5秒
- 切换页面有明显延迟
- 大数据量时卡顿

#### 性能分析
```bash
# 生产构建分析
npm run build -- --mode analyze

# 使用Lighthouse分析
# Chrome DevTools -> Lighthouse面板
```

#### 优化方案

**代码分割**:
```typescript
// 使用React.lazy懒加载
const Dashboard = lazy(() => import('@/pages/Dashboard'));
const Crawler = lazy(() => import('@/pages/Crawler'));

// 路由配置
<Suspense fallback={<Loading />}>
  <Routes>
    <Route path="/" element={<Dashboard />} />
    <Route path="/crawler" element={<Crawler />} />
  </Routes>
</Suspense>
```

**图片优化**:
```typescript
// 使用懒加载图片
const LazyImage = ({ src, alt }: { src: string; alt: string }) => {
  return (
    <img 
      src={src} 
      alt={alt}
      loading="lazy"
      style={{ width: '100%', height: 'auto' }}
    />
  );
};
```

**虚拟滚动**:
```typescript
// 对于大数据量表格，使用虚拟滚动
import { VirtualTable } from 'antd';

<VirtualTable
  columns={columns}
  dataSource={dataSource}
  scroll={{ y: 400, x: '100vw' }}
/>
```

### 2. 内存泄漏

#### 问题症状
- 长时间使用后页面变慢
- 浏览器内存使用持续增长
- 页面最终崩溃

#### 诊断工具
```javascript
// 监控内存使用
console.log('内存使用:', performance.memory);

// 定期检查
setInterval(() => {
  console.log('堆内存:', performance.memory.usedJSHeapSize);
}, 5000);
```

#### 解决方案

**清理副作用**:
```typescript
useEffect(() => {
  const timer = setInterval(() => {
    // 定时器逻辑
  }, 1000);

  // 清理函数
  return () => {
    clearInterval(timer);
  };
}, []);
```

**WebSocket连接管理**:
```typescript
useEffect(() => {
  const ws = new WebSocket('ws://localhost:8001/ws');
  
  return () => {
    ws.close(); // 组件卸载时关闭连接
  };
}, []);
```

## 🔒 安全问题

### 1. XSS攻击防护

#### 防护措施
```typescript
// React自动转义，但注意dangerouslySetInnerHTML
const SafeHTML = ({ content }: { content: string }) => {
  // 使用DOMPurify清理HTML
  const cleanHTML = DOMPurify.sanitize(content);
  return <div dangerouslySetInnerHTML={{ __html: cleanHTML }} />;
};
```

### 2. CSRF攻击防护

#### 防护措施
```typescript
// 在axios请求中添加CSRF token
axios.defaults.headers.common['X-CSRF-Token'] = getCsrfToken();
```

## 📱 移动端问题

### 1. 响应式布局异常

#### 解决方案
```css
/* 确保视口设置正确 */
<meta name="viewport" content="width=device-width, initial-scale=1.0">

/* 使用弹性布局 */
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

/* 媒体查询 */
@media (max-width: 768px) {
  .table-container {
    overflow-x: auto;
  }
}
```

### 2. 触摸事件问题

#### 解决方案
```typescript
// 添加触摸事件支持
const handleTouch = (e: TouchEvent) => {
  e.preventDefault();
  // 触摸逻辑
};

useEffect(() => {
  document.addEventListener('touchstart', handleTouch, { passive: false });
  return () => {
    document.removeEventListener('touchstart', handleTouch);
  };
}, []);
```

## 🛠️ 开发工具配置

### 1. VSCode配置

**推荐插件**:
- ES7+ React/Redux/React-Native snippets
- TypeScript Importer
- Auto Rename Tag
- Prettier - Code formatter
- ESLint

**配置文件** (`.vscode/settings.json`):
```json
{
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "typescript.preferences.importModuleSpecifier": "relative"
}
```

### 2. 浏览器调试

**React Developer Tools**:
- 安装React DevTools扩展
- 查看组件树和状态
- 性能分析

**Redux DevTools**:
- 监控Zustand状态变化
- 时间旅行调试

## 📋 问题报告模板

当遇到无法解决的问题时，请按以下模板提供信息：

### 环境信息
- 操作系统: Windows 10 / macOS 12 / Ubuntu 20.04
- 浏览器: Chrome 120 / Firefox 115 / Safari 16
- Node.js版本: v18.17.0
- npm版本: 9.8.1

### 问题描述
1. **问题现象**: 详细描述出现的问题
2. **复现步骤**: 如何重现这个问题
3. **预期结果**: 期望的正确行为
4. **实际结果**: 实际发生的情况

### 错误信息
```
粘贴完整的错误堆栈信息
```

### 相关截图
提供问题截图或录屏

### 已尝试的解决方法
列出已经尝试过的解决方案

## 📞 获取帮助

### 官方资源
- **React文档**: https://react.dev/
- **TypeScript文档**: https://www.typescriptlang.org/docs/
- **Vite文档**: https://vitejs.dev/
- **Ant Design文档**: https://ant.design/

### 社区支持
- **Stack Overflow**: 搜索相关问题
- **GitHub Issues**: 查看项目issue
- **Reddit**: r/reactjs, r/typescript

### 本地调试技巧

**启用详细日志**:
```bash
# 设置环境变量启用详细日志
VITE_DEBUG=true npm run dev
```

**使用调试器**:
```typescript
// 在代码中设置断点
debugger;

// 或使用console.log
console.log('调试信息:', variable);
```

**检查构建产物**:
```bash
# 分析构建结果
npm run build
ls -la dist/

# 检查依赖
npm ls
```

记住，大多数问题都有标准解决方案。遇到问题时，先检查控制台错误信息，然后按照本指南逐步排查。如果问题依然存在，请提供详细的环境信息和错误日志以获得更好的帮助。