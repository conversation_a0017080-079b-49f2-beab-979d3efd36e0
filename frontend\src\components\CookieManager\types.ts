// Cookie 管理相关类型定义

export interface Cookie {
  name: string;
  value: string;
  domain: string;
  path?: string;
  expires?: number;
  httpOnly?: boolean;
  secure?: boolean;
  sameSite?: 'Strict' | 'Lax' | 'None';
}

export interface CookieStatus {
  exists: boolean;
  valid: boolean;
  expiresAt?: string;
  cookies: <PERSON>ie[];
}

export interface CookieValidationResult {
  valid: boolean;
  message: string;
  details?: {
    missingCookies?: string[];
    expiredCookies?: string[];
    invalidCookies?: string[];
  };
}

export interface CookieImportData {
  cookies?: Cookie[];
  cookieString?: string;
}

export interface CookieImportResult {
  success: boolean;
  message: string;
  data?: {
    importedCount: number;
    validCount: number;
    invalidCount: number;
  };
}

export interface CookieDisplayProps {
  cookies: Cookie[];
  loading?: boolean;
  onRefresh?: () => void;
  onClear?: () => void;
}

export interface CookieImportProps {
  onImport: (data: CookieImportData) => Promise<void>;
  loading?: boolean;
}

export interface CookieValidatorProps {
  cookies: <PERSON>ie[];
  onValidate?: (result: CookieValidationResult) => void;
  loading?: boolean;
}

// 支持的导入格式类型
export type ImportFormat = 'json' | 'netscape' | 'cookie-string' | 'browser-export';

// 导入格式检测结果
export interface FormatDetectionResult {
  format: ImportFormat;
  confidence: number;
  data: Cookie[];
}

// Cookie 解析选项
export interface CookieParseOptions {
  domain?: string;
  path?: string;
  defaultExpires?: number;
}