"""
增强的内存监控机制
提供各组件独立监控、内存泄漏检测、自动清理等功能

版本: 1.0.0
作者: AI Assistant
更新时间: 2025-01-31
"""

import asyncio
import gc
import json
import psutil
import threading
import time
from collections import deque, defaultdict
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Callable, Any, Tuple
from loguru import logger


@dataclass
class MemorySnapshot:
    """内存快照数据类"""
    timestamp: float
    component: str
    process_memory_mb: float
    process_memory_percent: float
    system_memory_total_gb: float
    system_memory_available_gb: float
    system_memory_usage_percent: float
    cpu_percent: float
    thread_count: int
    file_descriptors: int
    stage: str = ""
    additional_info: Dict[str, Any] = None

    def __post_init__(self):
        if self.additional_info is None:
            self.additional_info = {}


@dataclass
class MemoryAlert:
    """内存告警数据类"""
    timestamp: float
    component: str
    alert_type: str  # warning, critical, leak_detected
    memory_mb: float
    threshold_mb: float
    message: str
    stage: str = ""


@dataclass
class MemoryStatistics:
    """内存统计数据类"""
    component: str
    total_snapshots: int
    avg_memory_mb: float
    max_memory_mb: float
    min_memory_mb: float
    memory_growth_rate: float  # MB per minute
    leak_probability: float  # 0-1
    garbage_collections_triggered: int
    alerts_count: int


class ComponentMemoryTracker:
    """单个组件的内存跟踪器"""
    
    def __init__(self, component_name: str, warning_threshold_mb: float = 500.0, 
                 critical_threshold_mb: float = 1000.0, history_size: int = 100):
        self.component_name = component_name
        self.warning_threshold_mb = warning_threshold_mb
        self.critical_threshold_mb = critical_threshold_mb
        self.history_size = history_size
        
        self.snapshots = deque(maxlen=history_size)
        self.alerts = deque(maxlen=50)  # 保存最近50个告警
        self.last_gc_time = time.time()
        self.gc_count = 0
        
        # 内存泄漏检测参数
        self.leak_detection_window = 10  # 检测窗口大小
        self.leak_growth_threshold = 50.0  # MB，超过此增长认为可能泄漏
        self.stable_threshold = 0.95  # 内存稳定阈值
        
    def add_snapshot(self, snapshot: MemorySnapshot) -> List[MemoryAlert]:
        """添加内存快照并检查告警"""
        self.snapshots.append(snapshot)
        alerts = []
        
        # 检查阈值告警
        if snapshot.process_memory_mb > self.critical_threshold_mb:
            alert = MemoryAlert(
                timestamp=snapshot.timestamp,
                component=self.component_name,
                alert_type="critical",
                memory_mb=snapshot.process_memory_mb,
                threshold_mb=self.critical_threshold_mb,
                message=f"组件 {self.component_name} 内存使用过高: {snapshot.process_memory_mb:.1f}MB (阈值: {self.critical_threshold_mb}MB)",
                stage=snapshot.stage
            )
            alerts.append(alert)
            self.alerts.append(alert)
            
        elif snapshot.process_memory_mb > self.warning_threshold_mb:
            alert = MemoryAlert(
                timestamp=snapshot.timestamp,
                component=self.component_name,
                alert_type="warning",
                memory_mb=snapshot.process_memory_mb,
                threshold_mb=self.warning_threshold_mb,
                message=f"组件 {self.component_name} 内存使用较高: {snapshot.process_memory_mb:.1f}MB (阈值: {self.warning_threshold_mb}MB)",
                stage=snapshot.stage
            )
            alerts.append(alert)
            self.alerts.append(alert)
        
        # 检查内存泄漏
        leak_alert = self._detect_memory_leak()
        if leak_alert:
            alerts.append(leak_alert)
            self.alerts.append(leak_alert)
        
        return alerts
    
    def _detect_memory_leak(self) -> Optional[MemoryAlert]:
        """检测内存泄漏"""
        if len(self.snapshots) < self.leak_detection_window:
            return None
        
        recent_snapshots = list(self.snapshots)[-self.leak_detection_window:]
        
        # 计算内存增长趋势
        memory_values = [s.process_memory_mb for s in recent_snapshots]
        time_values = [s.timestamp for s in recent_snapshots]
        
        # 简单线性回归计算增长率
        growth_rate = self._calculate_growth_rate(time_values, memory_values)
        
        # 转换为每分钟增长率
        growth_rate_per_minute = growth_rate * 60
        
        # 检查是否持续增长且超过阈值
        if growth_rate_per_minute > self.leak_growth_threshold / 60:  # 每分钟增长超过阈值
            latest_snapshot = recent_snapshots[-1]
            return MemoryAlert(
                timestamp=latest_snapshot.timestamp,
                component=self.component_name,
                alert_type="leak_detected",
                memory_mb=latest_snapshot.process_memory_mb,
                threshold_mb=self.leak_growth_threshold,
                message=f"组件 {self.component_name} 检测到可能的内存泄漏，增长率: {growth_rate_per_minute:.2f}MB/分钟",
                stage=latest_snapshot.stage
            )
        
        return None
    
    def _calculate_growth_rate(self, time_values: List[float], memory_values: List[float]) -> float:
        """计算内存增长率（MB/秒）"""
        if len(time_values) < 2:
            return 0.0
        
        # 简单线性回归
        n = len(time_values)
        sum_x = sum(time_values)
        sum_y = sum(memory_values)
        sum_xy = sum(x * y for x, y in zip(time_values, memory_values))
        sum_x2 = sum(x * x for x in time_values)
        
        # 避免除零
        denominator = n * sum_x2 - sum_x * sum_x
        if abs(denominator) < 1e-10:
            return 0.0
        
        # 计算斜率（增长率）
        slope = (n * sum_xy - sum_x * sum_y) / denominator
        return slope
    
    def get_statistics(self) -> MemoryStatistics:
        """获取内存统计信息"""
        if not self.snapshots:
            return MemoryStatistics(
                component=self.component_name,
                total_snapshots=0,
                avg_memory_mb=0.0,
                max_memory_mb=0.0,
                min_memory_mb=0.0,
                memory_growth_rate=0.0,
                leak_probability=0.0,
                garbage_collections_triggered=self.gc_count,
                alerts_count=len(self.alerts)
            )
        
        memory_values = [s.process_memory_mb for s in self.snapshots]
        time_values = [s.timestamp for s in self.snapshots]
        
        return MemoryStatistics(
            component=self.component_name,
            total_snapshots=len(self.snapshots),
            avg_memory_mb=sum(memory_values) / len(memory_values),
            max_memory_mb=max(memory_values),
            min_memory_mb=min(memory_values),
            memory_growth_rate=self._calculate_growth_rate(time_values, memory_values) * 60,  # 每分钟
            leak_probability=self._calculate_leak_probability(),
            garbage_collections_triggered=self.gc_count,
            alerts_count=len(self.alerts)
        )
    
    def _calculate_leak_probability(self) -> float:
        """计算内存泄漏概率"""
        if len(self.snapshots) < self.leak_detection_window:
            return 0.0
        
        recent_snapshots = list(self.snapshots)[-self.leak_detection_window:]
        memory_values = [s.process_memory_mb for s in recent_snapshots]
        
        # 检查内存是否持续增长
        increases = 0
        for i in range(1, len(memory_values)):
            if memory_values[i] > memory_values[i-1]:
                increases += 1
        
        # 增长比例
        growth_ratio = increases / (len(memory_values) - 1) if len(memory_values) > 1 else 0.0
        
        # 计算变化幅度
        if len(memory_values) > 1:
            total_change = memory_values[-1] - memory_values[0]
            avg_memory = sum(memory_values) / len(memory_values)
            relative_change = abs(total_change) / avg_memory if avg_memory > 0 else 0.0
        else:
            relative_change = 0.0
        
        # 综合评估泄漏概率
        leak_probability = min(1.0, growth_ratio * 0.7 + relative_change * 0.3)
        
        return leak_probability
    
    def trigger_garbage_collection(self) -> int:
        """触发垃圾回收"""
        collected = gc.collect()
        self.gc_count += 1
        self.last_gc_time = time.time()
        return collected


class MemoryMonitor:
    """增强的内存监控器"""
    
    def __init__(self, config: Optional[Dict] = None):
        """初始化内存监控器"""
        self.config = config or self._get_default_config()
        
        # 组件跟踪器
        self.component_trackers: Dict[str, ComponentMemoryTracker] = {}
        
        # 全局设置
        self.monitoring_enabled = self.config.get('enabled', True)
        self.monitoring_interval = self.config.get('monitoring_interval', 30)  # 秒
        self.auto_gc_enabled = self.config.get('auto_gc_enabled', True)
        self.auto_gc_threshold_mb = self.config.get('auto_gc_threshold_mb', 800)
        self.report_interval = self.config.get('report_interval', 300)  # 5分钟
        
        # 监控线程
        self._monitoring_thread: Optional[threading.Thread] = None
        self._stop_monitoring = threading.Event()
        self._monitoring_lock = threading.Lock()
        
        # 回调函数
        self.alert_callbacks: List[Callable[[MemoryAlert], None]] = []
        self.report_callbacks: List[Callable[[Dict[str, MemoryStatistics]], None]] = []
        
        # 统计信息
        self.start_time = time.time()
        self.total_gc_collections = 0
        self.total_alerts = 0
        
        logger.info("内存监控器已初始化")
    
    def _get_default_config(self) -> Dict:
        """获取默认配置"""
        return {
            'enabled': True,
            'monitoring_interval': 30,
            'auto_gc_enabled': True,
            'auto_gc_threshold_mb': 800,
            'report_interval': 300,
            'components': {
                'crawler': {'warning_threshold_mb': 400, 'critical_threshold_mb': 800},
                'browser_manager': {'warning_threshold_mb': 300, 'critical_threshold_mb': 600},
                'data_processor': {'warning_threshold_mb': 200, 'critical_threshold_mb': 400},
                'api_monitor': {'warning_threshold_mb': 100, 'critical_threshold_mb': 200},
                'scroll_manager': {'warning_threshold_mb': 50, 'critical_threshold_mb': 100},
                'system': {'warning_threshold_mb': 1000, 'critical_threshold_mb': 2000}
            }
        }
    
    def register_component(self, component_name: str, warning_threshold_mb: float = 300.0, 
                          critical_threshold_mb: float = 600.0) -> None:
        """注册需要监控的组件"""
        if component_name not in self.component_trackers:
            self.component_trackers[component_name] = ComponentMemoryTracker(
                component_name, warning_threshold_mb, critical_threshold_mb
            )
            logger.debug(f"已注册组件监控: {component_name}")
    
    def add_alert_callback(self, callback: Callable[[MemoryAlert], None]) -> None:
        """添加告警回调函数"""
        self.alert_callbacks.append(callback)
    
    def add_report_callback(self, callback: Callable[[Dict[str, MemoryStatistics]], None]) -> None:
        """添加报告回调函数"""
        self.report_callbacks.append(callback)
    
    def start_monitoring(self) -> None:
        """启动后台监控"""
        if not self.monitoring_enabled:
            logger.info("内存监控已禁用")
            return
        
        if self._monitoring_thread and self._monitoring_thread.is_alive():
            logger.warning("内存监控已在运行")
            return
        
        self._stop_monitoring.clear()
        self._monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self._monitoring_thread.start()
        logger.info("内存监控已启动")
    
    def stop_monitoring(self) -> None:
        """停止后台监控"""
        self._stop_monitoring.set()
        if self._monitoring_thread:
            self._monitoring_thread.join(timeout=5)
        logger.info("内存监控已停止")
    
    def _monitoring_loop(self) -> None:
        """监控循环"""
        last_report_time = time.time()
        
        while not self._stop_monitoring.is_set():
            try:
                current_time = time.time()
                
                # 自动注册系统组件（如果还未注册）
                if 'system' not in self.component_trackers:
                    self.register_component('system', 1000, 2000)
                
                # 收集所有组件的内存快照
                for component_name, tracker in self.component_trackers.items():
                    snapshot = self._take_memory_snapshot(component_name)
                    alerts = tracker.add_snapshot(snapshot)
                    
                    # 处理告警
                    for alert in alerts:
                        self.total_alerts += 1
                        self._handle_alert(alert)
                    
                    # 自动垃圾回收
                    if (self.auto_gc_enabled and 
                        snapshot.process_memory_mb > self.auto_gc_threshold_mb):
                        collected = tracker.trigger_garbage_collection()
                        self.total_gc_collections += collected
                        logger.info(f"组件 {component_name} 触发垃圾回收，清理了 {collected} 个对象")
                
                # 定期生成报告
                if current_time - last_report_time >= self.report_interval:
                    self._generate_periodic_report()
                    last_report_time = current_time
                
                # 等待下次监控
                self._stop_monitoring.wait(self.monitoring_interval)
                
            except Exception as e:
                logger.error(f"内存监控循环出错: {e}")
                self._stop_monitoring.wait(self.monitoring_interval)
    
    def _take_memory_snapshot(self, component_name: str, stage: str = "") -> MemorySnapshot:
        """获取内存快照"""
        try:
            process = psutil.Process()
            
            # 内存信息
            memory_info = process.memory_info()
            memory_mb = memory_info.rss / 1024 / 1024
            memory_percent = process.memory_percent()
            
            # 系统内存信息
            system_memory = psutil.virtual_memory()
            system_memory_gb = system_memory.total / 1024 / 1024 / 1024
            system_available_gb = system_memory.available / 1024 / 1024 / 1024
            
            # CPU和其他信息
            cpu_percent = process.cpu_percent()
            thread_count = process.num_threads()
            
            # 文件描述符数量（仅在支持的系统上）
            try:
                file_descriptors = process.num_fds()
            except (AttributeError, psutil.AccessDenied):
                file_descriptors = 0
            
            return MemorySnapshot(
                timestamp=time.time(),
                component=component_name,
                process_memory_mb=round(memory_mb, 1),
                process_memory_percent=round(memory_percent, 1),
                system_memory_total_gb=round(system_memory_gb, 1),
                system_memory_available_gb=round(system_available_gb, 1),
                system_memory_usage_percent=system_memory.percent,
                cpu_percent=cpu_percent,
                thread_count=thread_count,
                file_descriptors=file_descriptors,
                stage=stage
            )
            
        except Exception as e:
            logger.error(f"获取内存快照失败: {e}")
            # 返回默认快照
            return MemorySnapshot(
                timestamp=time.time(),
                component=component_name,
                process_memory_mb=0.0,
                process_memory_percent=0.0,
                system_memory_total_gb=0.0,
                system_memory_available_gb=0.0,
                system_memory_usage_percent=0.0,
                cpu_percent=0.0,
                thread_count=0,
                file_descriptors=0,
                stage=stage
            )
    
    def _handle_alert(self, alert: MemoryAlert) -> None:
        """处理告警"""
        # 记录日志
        if alert.alert_type == "critical":
            logger.error(alert.message)
        elif alert.alert_type == "warning":
            logger.warning(alert.message)
        elif alert.alert_type == "leak_detected":
            logger.error(f"🚨 {alert.message}")
        
        # 调用回调函数
        for callback in self.alert_callbacks:
            try:
                callback(alert)
            except Exception as e:
                logger.error(f"告警回调函数执行失败: {e}")
    
    def _generate_periodic_report(self) -> None:
        """生成定期报告"""
        statistics = {}
        for component_name, tracker in self.component_trackers.items():
            statistics[component_name] = tracker.get_statistics()
        
        # 调用报告回调函数
        for callback in self.report_callbacks:
            try:
                callback(statistics)
            except Exception as e:
                logger.error(f"报告回调函数执行失败: {e}")
    
    def record_memory_usage(self, component_name: str, stage: str = "") -> MemorySnapshot:
        """手动记录内存使用情况"""
        if not self.monitoring_enabled:
            return None
        
        # 确保组件已注册
        if component_name not in self.component_trackers:
            self.register_component(component_name)
        
        snapshot = self._take_memory_snapshot(component_name, stage)
        tracker = self.component_trackers[component_name]
        alerts = tracker.add_snapshot(snapshot)
        
        # 处理告警
        for alert in alerts:
            self.total_alerts += 1
            self._handle_alert(alert)
        
        return snapshot
    
    def force_garbage_collection(self, component_name: str = "system") -> int:
        """强制执行垃圾回收"""
        if component_name in self.component_trackers:
            collected = self.component_trackers[component_name].trigger_garbage_collection()
            self.total_gc_collections += collected
            logger.info(f"强制垃圾回收完成，清理了 {collected} 个对象")
            return collected
        else:
            collected = gc.collect()
            self.total_gc_collections += collected
            logger.info(f"系统垃圾回收完成，清理了 {collected} 个对象")
            return collected
    
    def get_component_statistics(self, component_name: str) -> Optional[MemoryStatistics]:
        """获取指定组件的统计信息"""
        if component_name in self.component_trackers:
            return self.component_trackers[component_name].get_statistics()
        return None
    
    def get_all_statistics(self) -> Dict[str, MemoryStatistics]:
        """获取所有组件的统计信息"""
        statistics = {}
        for component_name, tracker in self.component_trackers.items():
            statistics[component_name] = tracker.get_statistics()
        return statistics
    
    def get_recent_alerts(self, component_name: Optional[str] = None, 
                         limit: int = 10) -> List[MemoryAlert]:
        """获取最近的告警"""
        all_alerts = []
        
        if component_name:
            if component_name in self.component_trackers:
                all_alerts.extend(self.component_trackers[component_name].alerts)
        else:
            for tracker in self.component_trackers.values():
                all_alerts.extend(tracker.alerts)
        
        # 按时间排序并限制数量
        all_alerts.sort(key=lambda x: x.timestamp, reverse=True)
        return all_alerts[:limit]
    
    def export_memory_report(self, file_path: Optional[str] = None) -> str:
        """导出内存使用报告"""
        if file_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            file_path = f"memory_report_{timestamp}.json"
        
        report_data = {
            "timestamp": datetime.now().isoformat(),
            "monitoring_duration_hours": (time.time() - self.start_time) / 3600,
            "total_alerts": self.total_alerts,
            "total_gc_collections": self.total_gc_collections,
            "statistics": {},
            "recent_alerts": []
        }
        
        # 添加统计信息
        for component_name, stats in self.get_all_statistics().items():
            report_data["statistics"][component_name] = asdict(stats)
        
        # 添加最近告警
        recent_alerts = self.get_recent_alerts(limit=50)
        for alert in recent_alerts:
            report_data["recent_alerts"].append(asdict(alert))
        
        # 保存到文件
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"内存报告已导出到: {file_path}")
            return file_path
            
        except Exception as e:
            logger.error(f"导出内存报告失败: {e}")
            return ""
    
    def cleanup_resources(self) -> None:
        """清理资源"""
        logger.info("开始清理内存监控器资源...")
        
        # 停止监控
        self.stop_monitoring()
        
        # 清理跟踪器
        self.component_trackers.clear()
        
        # 清理回调函数
        self.alert_callbacks.clear()
        self.report_callbacks.clear()
        
        logger.info("内存监控器资源清理完成")
    
    def __enter__(self):
        """上下文管理器入口"""
        self.start_monitoring()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.cleanup_resources()


def create_memory_monitor(config: Optional[Dict] = None) -> MemoryMonitor:
    """创建内存监控器的便捷函数"""
    return MemoryMonitor(config)


# 全局内存监控器实例（可选）
_global_monitor: Optional[MemoryMonitor] = None


def get_global_memory_monitor() -> Optional[MemoryMonitor]:
    """获取全局内存监控器实例"""
    return _global_monitor


def set_global_memory_monitor(monitor: MemoryMonitor) -> None:
    """设置全局内存监控器实例"""
    global _global_monitor
    _global_monitor = monitor


def init_global_memory_monitor(config: Optional[Dict] = None) -> MemoryMonitor:
    """初始化全局内存监控器"""
    global _global_monitor
    if _global_monitor is None:
        _global_monitor = MemoryMonitor(config)
    return _global_monitor