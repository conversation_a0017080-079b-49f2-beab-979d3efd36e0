import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Statistic,
  Modal,
  message,
  Dropdown,
  Input,
  DatePicker,
  Select,
  Row,
  Col,
  Typography
} from 'antd';
import {
  ReloadOutlined,
  DeleteOutlined,
  EyeOutlined,
  SearchOutlined,
  ClearOutlined,
  ExportOutlined,
  MoreOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import type { MenuProps } from 'antd';
import dayjs from 'dayjs';
import isBetween from 'dayjs/plugin/isBetween';

dayjs.extend(isBetween);
import type { TaskHistory, TaskControlActions } from './types';
import {
  formatTaskStatus,
  formatDuration,
  getConfigSummary,
  storage
} from './utils';

const { Text } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;

interface TaskHistoryProps {
  onRetryTask?: (taskId: string) => void;
  onExportHistory?: (tasks: TaskHistory[]) => void;
  actions?: TaskControlActions;
}

interface HistoryFilter {
  keyword?: string;
  status?: string;
  dateRange?: [dayjs.Dayjs, dayjs.Dayjs];
}

const TaskHistoryComponent: React.FC<TaskHistoryProps> = ({
  onRetryTask,
  onExportHistory,
  actions
}) => {
  const [history, setHistory] = useState<TaskHistory[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [filter, setFilter] = useState<HistoryFilter>({});
  const [detailVisible, setDetailVisible] = useState(false);
  const [selectedTask, setSelectedTask] = useState<TaskHistory | null>(null);

  // 加载历史记录
  const loadHistory = () => {
    setLoading(true);
    try {
      const historyData = storage.getTaskHistory();
      setHistory(historyData);
    } catch {
      message.error('加载历史记录失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadHistory();
  }, []);

  // 过滤历史记录
  const filteredHistory = history.filter(task => {
    if (filter.keyword && !task.name.toLowerCase().includes(filter.keyword.toLowerCase()) &&
        !task.config.keyword.toLowerCase().includes(filter.keyword.toLowerCase())) {
      return false;
    }
    
    if (filter.status && task.status !== filter.status) {
      return false;
    }
    
    if (filter.dateRange) {
      const taskDate = dayjs(task.createdAt);
      if (!taskDate.isBetween(filter.dateRange[0], filter.dateRange[1], 'day', '[]')) {
        return false;
      }
    }
    
    return true;
  });

  // 重新运行任务
  const handleRetryTask = async (task: TaskHistory) => {
    if (!actions) {
      message.error('任务操作不可用');
      return;
    }

    try {
      const newTaskId = await actions.start(task.config);
      message.success(`任务已重新启动，新任务ID: ${newTaskId}`);
      
      if (onRetryTask) {
        onRetryTask(newTaskId);
      }
    } catch (error) {
      message.error('重新运行失败: ' + (error as Error).message);
    }
  };

  // 删除历史记录
  const handleDeleteTask = (taskId: string) => {
    Modal.confirm({
      title: '确认删除？',
      content: '删除后历史记录将无法恢复。',
      okText: '确认删除',
      cancelText: '取消',
      okButtonProps: { danger: true },
      onOk: () => {
        const newHistory = history.filter(t => t.id !== taskId);
        setHistory(newHistory);
        localStorage.setItem('pdd-task-history', JSON.stringify(newHistory));
        message.success('删除成功');
      }
    });
  };

  // 批量删除
  const handleBatchDelete = () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请先选择要删除的记录');
      return;
    }

    Modal.confirm({
      title: `确认删除 ${selectedRowKeys.length} 条记录？`,
      content: '删除后历史记录将无法恢复。',
      okText: '确认删除',
      cancelText: '取消',
      okButtonProps: { danger: true },
      onOk: () => {
        const newHistory = history.filter(t => !selectedRowKeys.includes(t.id));
        setHistory(newHistory);
        localStorage.setItem('pdd-task-history', JSON.stringify(newHistory));
        setSelectedRowKeys([]);
        message.success('批量删除成功');
      }
    });
  };

  // 清空历史记录
  const handleClearAll = () => {
    Modal.confirm({
      title: '确认清空所有历史记录？',
      content: '此操作不可撤销，所有历史记录将被永久删除。',
      okText: '确认清空',
      cancelText: '取消',
      okButtonProps: { danger: true },
      onOk: () => {
        setHistory([]);
        localStorage.removeItem('pdd-task-history');
        setSelectedRowKeys([]);
        message.success('历史记录已清空');
      }
    });
  };

  // 导出历史记录
  const handleExport = () => {
    if (onExportHistory) {
      onExportHistory(filteredHistory);
    } else {
      // 默认导出为 JSON
      const dataStr = JSON.stringify(filteredHistory, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `task-history-${dayjs().format('YYYY-MM-DD')}.json`;
      link.click();
      URL.revokeObjectURL(url);
      message.success('历史记录已导出');
    }
  };

  // 查看任务详情
  const handleViewDetail = (task: TaskHistory) => {
    setSelectedTask(task);
    setDetailVisible(true);
  };

  // 操作菜单
  const getActionMenu = (task: TaskHistory): MenuProps['items'] => [
    {
      key: 'retry',
      label: '重新运行',
      icon: <ReloadOutlined />,
      onClick: () => handleRetryTask(task)
    },
    {
      key: 'detail',
      label: '查看详情',
      icon: <EyeOutlined />,
      onClick: () => handleViewDetail(task)
    },
    {
      type: 'divider'
    },
    {
      key: 'delete',
      label: '删除记录',
      icon: <DeleteOutlined />,
      danger: true,
      onClick: () => handleDeleteTask(task.id)
    }
  ];

  // 表格列定义
  const columns: ColumnsType<TaskHistory> = [
    {
      title: '任务名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      ellipsis: true,
      render: (text, record) => (
        <div>
          <div>{text}</div>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.config.keyword}
          </Text>
        </div>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      filters: [
        { text: '已完成', value: 'completed' },
        { text: '失败', value: 'failed' },
        { text: '已取消', value: 'cancelled' }
      ],
      onFilter: (value, record) => record.status === value,
      render: (status) => {
        const { text, color } = formatTaskStatus(status);
        return <Tag color={color}>{text}</Tag>;
      }
    },
    {
      title: '收集数量',
      key: 'collected',
      width: 100,
      render: (_, record) => (
        <Statistic
          value={record.progress.collectedCount}
          suffix="个"
          style={{ margin: 0 }}
          valueStyle={{ fontSize: '14px' }}
        />
      ),
      sorter: (a, b) => a.progress.collectedCount - b.progress.collectedCount
    },
    {
      title: '用时',
      key: 'duration',
      width: 120,
      render: (_, record) => {
        if (!record.startedAt) return '-';
        const endTime = record.completedAt || new Date().toISOString();
        return formatDuration(record.startedAt, endTime);
      }
    },
    {
      title: '成功率',
      key: 'successRate',
      width: 100,
      render: (_, record) => {
        if (!record.statistics?.successRate) return '-';
        return (
          <Text style={{
            color: record.statistics.successRate > 0.8 ? '#52c41a' : 
                   record.statistics.successRate > 0.5 ? '#faad14' : '#ff4d4f'
          }}>
            {(record.statistics.successRate * 100).toFixed(1)}%
          </Text>
        );
      },
      sorter: (a, b) => (a.statistics?.successRate || 0) - (b.statistics?.successRate || 0)
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
      render: (text) => dayjs(text).format('MM-DD HH:mm'),
      sorter: (a, b) => dayjs(a.createdAt).unix() - dayjs(b.createdAt).unix(),
      defaultSortOrder: 'descend'
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      fixed: 'right',
      render: (_, record) => (
        <Dropdown
          menu={{ items: getActionMenu(record) }}
          trigger={['click']}
          placement="bottomRight"
        >
          <Button size="small" icon={<MoreOutlined />} />
        </Dropdown>
      )
    }
  ];

  return (
    <>
      <Card
        title="任务历史"
        extra={
          <Space>
            <Button
              icon={<ReloadOutlined />}
              onClick={loadHistory}
              loading={loading}
            >
              刷新
            </Button>
            <Button
              icon={<ExportOutlined />}
              onClick={handleExport}
              disabled={filteredHistory.length === 0}
            >
              导出
            </Button>
            {selectedRowKeys.length > 0 && (
              <Button
                danger
                icon={<DeleteOutlined />}
                onClick={handleBatchDelete}
              >
                删除 ({selectedRowKeys.length})
              </Button>
            )}
            <Button
              danger
              icon={<ClearOutlined />}
              onClick={handleClearAll}
              disabled={history.length === 0}
            >
              清空
            </Button>
          </Space>
        }
      >
        {/* 筛选器 */}
        <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
          <Col xs={24} sm={8}>
            <Input
              placeholder="搜索任务名称或关键词"
              prefix={<SearchOutlined />}
              value={filter.keyword}
              onChange={(e) => setFilter(prev => ({ ...prev, keyword: e.target.value }))}
              allowClear
            />
          </Col>
          <Col xs={24} sm={6}>
            <Select
              placeholder="筛选状态"
              value={filter.status}
              onChange={(value) => setFilter(prev => ({ ...prev, status: value }))}
              allowClear
              style={{ width: '100%' }}
            >
              <Option value="completed">已完成</Option>
              <Option value="failed">失败</Option>
              <Option value="cancelled">已取消</Option>
            </Select>
          </Col>
          <Col xs={24} sm={10}>
            <RangePicker
              placeholder={['开始日期', '结束日期']}
              value={filter.dateRange}
              onChange={(dates) => setFilter(prev => ({ 
                ...prev, 
                dateRange: dates as [dayjs.Dayjs, dayjs.Dayjs] 
              }))}
              style={{ width: '100%' }}
            />
          </Col>
        </Row>

        {/* 数据表格 */}
        <Table
          columns={columns}
          dataSource={filteredHistory}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => 
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`
          }}
          rowSelection={{
            selectedRowKeys,
            onChange: (keys) => setSelectedRowKeys(keys as string[]),
            selections: [
              Table.SELECTION_ALL,
              Table.SELECTION_INVERT,
              Table.SELECTION_NONE
            ]
          }}
          scroll={{ x: 800 }}
          size="small"
        />
      </Card>

      {/* 任务详情弹窗 */}
      <Modal
        title="任务详情"
        open={detailVisible}
        onCancel={() => setDetailVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailVisible(false)}>
            关闭
          </Button>,
          selectedTask && (
            <Button
              key="retry"
              type="primary"
              icon={<ReloadOutlined />}
              onClick={() => {
                if (selectedTask) {
                  handleRetryTask(selectedTask);
                  setDetailVisible(false);
                }
              }}
            >
              重新运行
            </Button>
          )
        ]}
        width={800}
      >
        {selectedTask && (
          <div>
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <Text strong>任务名称：</Text>
                <div>{selectedTask.name}</div>
              </Col>
              <Col span={12}>
                <Text strong>状态：</Text>
                <div>
                  <Tag color={formatTaskStatus(selectedTask.status).color}>
                    {formatTaskStatus(selectedTask.status).text}
                  </Tag>
                </div>
              </Col>
              <Col span={12}>
                <Text strong>创建时间：</Text>
                <div>{dayjs(selectedTask.createdAt).format('YYYY-MM-DD HH:mm:ss')}</div>
              </Col>
              <Col span={12}>
                <Text strong>完成时间：</Text>
                <div>
                  {selectedTask.completedAt ? 
                    dayjs(selectedTask.completedAt).format('YYYY-MM-DD HH:mm:ss') : 
                    '-'
                  }
                </div>
              </Col>
            </Row>

            <div style={{ margin: '16px 0' }}>
              <Text strong>搜索配置：</Text>
              <ul style={{ marginTop: 8, paddingLeft: 20 }}>
                {getConfigSummary(selectedTask.config).map((item, index) => (
                  <li key={index}>{item}</li>
                ))}
              </ul>
            </div>

            <Row gutter={[16, 16]}>
              <Col span={6}>
                <Statistic title="收集数量" value={selectedTask.progress.collectedCount} suffix="个" />
              </Col>
              <Col span={6}>
                <Statistic title="失败数量" value={selectedTask.progress.failedCount} suffix="个" />
              </Col>
              <Col span={6}>
                <Statistic 
                  title="成功率" 
                  value={selectedTask.statistics?.successRate ? 
                    (selectedTask.statistics.successRate * 100).toFixed(1) : 0
                  } 
                  suffix="%" 
                />
              </Col>
              <Col span={6}>
                <Statistic 
                  title="平均速度" 
                  value={selectedTask.statistics?.speed?.toFixed(1) || 0} 
                  suffix="条/分钟" 
                />
              </Col>
            </Row>

            {selectedTask.error && (
              <div style={{ marginTop: 16 }}>
                <Text strong>错误信息：</Text>
                <div style={{ 
                  marginTop: 8, 
                  padding: 12, 
                  backgroundColor: '#fff2f0', 
                  border: '1px solid #ffccc7',
                  borderRadius: 4 
                }}>
                  <Text type="danger">{selectedTask.error}</Text>
                </div>
              </div>
            )}

            {selectedTask.result && (
              <div style={{ marginTop: 16 }}>
                <Text strong>任务结果：</Text>
                <div style={{ marginTop: 8 }}>
                  <Text>{selectedTask.result.summary}</Text>
                  {selectedTask.result.exportPath && (
                    <div style={{ marginTop: 4 }}>
                      <Text type="secondary">导出路径：{selectedTask.result.exportPath}</Text>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        )}
      </Modal>
    </>
  );
};

export default TaskHistoryComponent;