#!/usr/bin/env python3
"""
启动后端API服务器
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 启动API服务器
if __name__ == "__main__":
    from backend.api_server import app
    import uvicorn
    
    print("正在启动后端API服务器...")
    print("地址: http://localhost:8001")
    print("API文档: http://localhost:8001/docs")
    
    uvicorn.run(
        app, 
        host="0.0.0.0", 
        port=8001,
        reload=False
    )