#!/usr/bin/env python3
"""
拼多多爬虫后端启动脚本
自动启动后端API服务器
"""

import os
import sys
import subprocess
import platform
import time
import json
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python版本过低，需要3.8或更高版本")
        return False
    return True

def check_dependencies():
    """检查并安装后端依赖"""
    required_packages = [
        'fastapi',
        'uvicorn',
        'selenium',
        'pandas',
        'openpyxl',
        'pyyaml'
    ]
    
    missing_packages = []
    
    print("📦 检查依赖包...")
    for package in required_packages:
        try:
            __import__(package)
            print(f"  ✅ {package}")
        except ImportError:
            print(f"  ❌ {package} 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print("\n📦 正在安装缺失的依赖包...")
        try:
            # 使用清华镜像源加速
            cmd = [sys.executable, '-m', 'pip', 'install', 
                   '-i', 'https://pypi.tuna.tsinghua.edu.cn/simple'] + missing_packages
            subprocess.run(cmd, check=True)
            print("✅ 依赖安装成功")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ 依赖安装失败: {e}")
            return False
    else:
        print("✅ 所有依赖已安装")
        return True

def check_chromedriver():
    """检查ChromeDriver是否存在"""
    driver_dir = Path(__file__).parent / 'drivers'
    
    if platform.system() == 'Windows':
        driver_path = driver_dir / 'chromedriver.exe'
    else:
        driver_path = driver_dir / 'chromedriver'
    
    if driver_path.exists():
        print(f"✅ ChromeDriver已就绪: {driver_path}")
        return True
    else:
        print(f"⚠️  ChromeDriver未找到: {driver_path}")
        print("请下载对应Chrome版本的ChromeDriver")
        print("下载地址: https://chromedriver.chromium.org/")
        return False

def check_config():
    """检查配置文件"""
    config_path = Path(__file__).parent / 'config' / 'settings.yaml'
    
    if not config_path.exists():
        print(f"⚠️  配置文件不存在: {config_path}")
        print("正在创建默认配置...")
        
        # 创建默认配置
        default_config = {
            'browser': {
                'headless': True,
                'window_size': '1920,1080',
                'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            },
            'crawl': {
                'default_max_pages': 5,
                'page_load_timeout': 30,
                'scroll_pause_time': 2
            },
            'api': {
                'host': '0.0.0.0',
                'port': 8001,
                'debug': True
            }
        }
        
        # 确保目录存在
        config_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 写入配置文件
        try:
            import yaml
            with open(config_path, 'w', encoding='utf-8') as f:
                yaml.dump(default_config, f, default_flow_style=False, allow_unicode=True)
            print(f"✅ 配置文件已创建: {config_path}")
        except ImportError:
            print("❌ 无法创建配置文件，缺少yaml库")
            return False
    else:
        print(f"✅ 配置文件已就绪: {config_path}")
    
    return True

def ensure_directories():
    """确保必要的目录存在"""
    directories = [
        'data',
        'logs',
        'browser_data',
        'browser_data/cookies',
        'drivers',
        'config'
    ]
    
    base_dir = Path(__file__).parent
    
    for dir_name in directories:
        dir_path = base_dir / dir_name
        if not dir_path.exists():
            dir_path.mkdir(parents=True, exist_ok=True)
            print(f"📁 创建目录: {dir_path}")
    
    print("✅ 目录结构已就绪")
    return True

def start_backend_server():
    """启动后端API服务器"""
    backend_dir = Path(__file__).parent / 'backend'
    api_file = backend_dir / 'api_server.py'
    
    if not api_file.exists():
        print(f"❌ API服务器文件不存在: {api_file}")
        return False
    
    print("\n🚀 正在启动后端API服务器...")
    print("=" * 50)
    
    try:
        # 切换到项目根目录
        os.chdir(Path(__file__).parent)
        
        # 显示启动信息
        print("📍 工作目录:", os.getcwd())
        print("🌐 API地址: http://localhost:8001")
        print("📡 WebSocket: ws://localhost:8001/ws")
        print("📚 API文档: http://localhost:8001/docs")
        print("=" * 50)
        print("\n按 Ctrl+C 停止服务器\n")
        
        # 启动uvicorn服务器
        cmd = [
            sys.executable, '-m', 'uvicorn',
            'backend.api_server:app',
            '--host', '0.0.0.0',
            '--port', '8001',
            '--reload',  # 开发模式，自动重载
            '--log-level', 'info'
        ]
        
        # 启动服务器
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        # 实时输出日志
        for line in process.stdout:
            print(line, end='')
        
        # 等待进程结束
        process.wait()
        
    except KeyboardInterrupt:
        print("\n\n👋 正在停止后端服务器...")
        if 'process' in locals():
            process.terminate()
            process.wait()
        print("✅ 后端服务器已停止")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 启动失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 发生错误: {e}")
        return False

def check_port_available(port=8001):
    """检查端口是否可用"""
    import socket
    
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    sock.settimeout(1)
    result = sock.connect_ex(('localhost', port))
    sock.close()
    
    if result == 0:
        print(f"⚠️  端口 {port} 已被占用")
        print("可能原因：")
        print("  1. 后端服务已在运行")
        print("  2. 其他程序占用了该端口")
        print("\n解决方案：")
        print("  1. 如果后端已运行，请先停止它")
        print("  2. 或修改 config/settings.yaml 中的端口配置")
        return False
    else:
        print(f"✅ 端口 {port} 可用")
        return True

def main():
    """主函数"""
    print("=" * 50)
    print("🕷️  拼多多爬虫后端启动脚本")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 检查端口
    if not check_port_available():
        response = input("\n是否仍要继续启动？(y/n): ")
        if response.lower() != 'y':
            print("👋 已取消启动")
            sys.exit(0)
    
    # 确保目录结构
    ensure_directories()
    
    # 检查配置文件
    if not check_config():
        sys.exit(1)
    
    # 检查ChromeDriver
    check_chromedriver()  # 只是警告，不阻止启动
    
    # 检查并安装依赖
    if not check_dependencies():
        print("❌ 依赖检查失败，无法启动后端")
        sys.exit(1)
    
    # 启动后端服务器
    start_backend_server()
    
    print("\n👋 感谢使用！")

if __name__ == "__main__":
    main()