import React, { useState, useCallback, useEffect } from 'react';
import {
  Card,
  Space,
  Button,
  Modal,
  Form,
  Input,
  message,
  Dropdown,
  Row,
  Col,
  Typography,
  Alert
} from 'antd';
import {
  SearchOutlined,
  SaveOutlined,
  LoadingOutlined,
  BookOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined
} from '@ant-design/icons';

import KeywordInput from './KeywordInput';
import ParameterSettings from './ParameterSettings';
import AdvancedFilters from './AdvancedFilters';
import SearchStats from './SearchStats';
import type { SearchConfig as SearchConfigType } from './types';
import { keywordHistoryManager } from './utils';

const { Title } = Typography;

interface SearchConfigProps {
  initialConfig?: Partial<SearchConfigType>;
  onConfigChange?: (config: SearchConfigType) => void;
  onStartSearch?: (config: SearchConfigType) => void;
  onStopSearch?: () => void;
  isSearching?: boolean;
  disabled?: boolean;
}

// 默认配置
const defaultConfig: SearchConfigType = {
  keywords: [],
  targetCount: 50,
  sortMethod: 'default',
  headless: true,
  enableFilter: false,
  priceRange: undefined,
  minRating: undefined,
  minSoldCount: undefined,
  excludeKeywords: undefined
};

const SearchConfig: React.FC<SearchConfigProps> = ({
  initialConfig = {},
  onConfigChange,
  onStartSearch,
  onStopSearch,
  isSearching = false,
  disabled = false
}) => {
  // 合并初始配置
  const [config, setConfig] = useState<SearchConfigType>({
    ...defaultConfig,
    ...initialConfig
  });

  // 移除预设管理功能

  // 配置更新处理
  const handleConfigChange = useCallback((updates: Partial<SearchConfigType>) => {
    const newConfig = { ...config, ...updates };
    setConfig(newConfig);
    onConfigChange?.(newConfig);
  }, [config, onConfigChange]);

  // 关键词变更处理
  const handleKeywordsChange = useCallback((keywords: string[]) => {
    handleConfigChange({ keywords });
  }, [handleConfigChange]);

  // 参数设置变更处理
  const handleParametersChange = useCallback((params: Partial<SearchConfigType>) => {
    handleConfigChange(params);
  }, [handleConfigChange]);

  // 筛选条件变更处理
  const handleFiltersChange = useCallback((filters: Partial<SearchConfigType>) => {
    handleConfigChange(filters);
  }, [handleConfigChange]);

  // 开始搜索
  const handleStartSearch = useCallback(() => {
    if (config.keywords.length === 0) {
      message.error('请至少添加一个关键词');
      return;
    }

    // 验证配置
    if (config.targetCount <= 0) {
      message.error('目标数量必须大于0');
      return;
    }

    // 添加关键词到历史记录
    keywordHistoryManager.addToHistory(config.keywords);

    // 保存搜索历史
    try {
      const history = JSON.parse(localStorage.getItem('pdd_search_history') || '[]');
      history.unshift({
        keywords: config.keywords,
        resultCount: 0, // 实际结果数量需要搜索完成后更新
        timestamp: new Date().toISOString(),
        config: { ...config }
      });
      localStorage.setItem('pdd_search_history', JSON.stringify(history.slice(0, 10)));
    } catch (error) {
      console.error('Failed to save search history:', error);
    }

    onStartSearch?.(config);
  }, [config, onStartSearch]);

  // 停止搜索
  const handleStopSearch = useCallback(() => {
    onStopSearch?.();
  }, [onStopSearch]);

  // 移除所有预设相关功能

  // 检查配置是否有效
  const isConfigValid = config.keywords.length > 0 && config.targetCount > 0;

  return (
    <div style={{ padding: '0 0 24px 0' }}>
      {/* 头部标题和操作 */}
      <div style={{ marginBottom: 24 }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', flexWrap: 'wrap', gap: 16 }}>
          <Title level={3} style={{ margin: 0 }}>
            <SearchOutlined style={{ marginRight: 8 }} />
            搜索配置
          </Title>
          
          <Space wrap>
            {/* 搜索控制 */}
            {isSearching ? (
              <Button
                type="primary"
                danger
                icon={<PauseCircleOutlined />}
                onClick={handleStopSearch}
                loading={false}
              >
                停止搜索
              </Button>
            ) : (
              <Button
                type="primary"
                icon={<PlayCircleOutlined />}
                onClick={handleStartSearch}
                disabled={disabled || !isConfigValid}
                size="large"
              >
                开始搜索
              </Button>
            )}
          </Space>
        </div>

        {/* 状态提示 */}
        {isSearching && (
          <Alert
            message="搜索进行中..."
            description="爬虫正在根据配置参数进行数据采集，请耐心等待"
            type="info"
            showIcon
            icon={<LoadingOutlined />}
            style={{ marginTop: 16 }}
          />
        )}
      </div>

      <Row gutter={[24, 24]}>
        {/* 左侧配置区域 */}
        <Col xs={24} lg={16}>
          <Space direction="vertical" size={24} style={{ width: '100%' }}>
            {/* 关键词输入 */}
            <Card 
              title={
                <Space>
                  <SearchOutlined />
                  <span>关键词设置</span>
                </Space>
              }
              size="small"
            >
              <KeywordInput
                value={config.keywords}
                onChange={handleKeywordsChange}
                disabled={disabled || isSearching}
                maxCount={100}
              />
            </Card>

            {/* 参数设置 */}
            <ParameterSettings
              config={{
                targetCount: config.targetCount,
                sortMethod: config.sortMethod,
                maxPages: config.maxPages,
                headless: config.headless,
                enableFilter: config.enableFilter
              }}
              onChange={handleParametersChange}
              disabled={disabled || isSearching}
            />

            {/* 高级筛选 */}
            {config.enableFilter && (
              <AdvancedFilters
                filters={{
                  priceRange: config.priceRange,
                  minRating: config.minRating,
                  minSoldCount: config.minSoldCount,
                  excludeKeywords: config.excludeKeywords
                }}
                onChange={handleFiltersChange}
                disabled={disabled || isSearching}
              />
            )}
          </Space>
        </Col>

        {/* 右侧统计区域 */}
        <Col xs={24} lg={8}>
          <SearchStats
            keywords={config.keywords}
            config={{
              targetCount: config.targetCount,
              maxPages: config.maxPages,
              sortMethod: config.sortMethod
            }}
            disabled={disabled || isSearching}
          />
        </Col>
      </Row>
    </div>
  );
};

export default SearchConfig;