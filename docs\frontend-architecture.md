# 拼多多爬虫前端技术架构文档

## 📋 文档信息
- **文档版本**: v1.0.0
- **创建日期**: 2025-08-01
- **更新日期**: 2025-08-01
- **架构师**: Frontend Design Architect
- **项目**: 拼多多爬虫前端系统

## 🎯 架构概览

### 系统目标
为现有的拼多多爬虫Python后端系统开发功能完全对等的现代化React前端界面，确保前端运行结果与后端直接运行结果100%一致。

### 核心原则
1. **功能对等性**: 前端必须支持后端的所有功能
2. **数据一致性**: 导出数据与后端完全一致
3. **用户体验**: 现代化、直观、响应式的界面设计
4. **性能优化**: 快速加载、流畅交互、合理的资源使用
5. **类型安全**: TypeScript严格模式，完整的类型定义

## 🏗️ 技术架构

### 技术栈选择

#### 核心框架
- **React 18.2+**: 现代化前端框架，支持并发特性
- **TypeScript 5.0+**: 静态类型检查，提升代码质量
- **Vite 4.0+**: 快速的构建工具和开发服务器

#### UI组件库
- **Ant Design 5.x**: 企业级UI组件库
  - 组件丰富、设计一致、文档完善
  - 内置TypeScript支持
  - 主题定制能力强

#### 状态管理
- **Zustand 4.x**: 轻量级状态管理库
  - 简单易用，无样板代码
  - TypeScript友好
  - 支持中间件和持久化

#### 网络通信
- **Axios 1.x**: HTTP客户端
  - 请求/响应拦截器
  - 自动JSON解析
  - 错误处理机制

- **react-use-websocket 4.x**: WebSocket管理
  - 自动重连机制
  - 连接状态管理
  - 消息队列和过滤

#### 工具库
- **react-router-dom 6.x**: 路由管理
- **dayjs**: 日期处理库
- **file-saver**: 文件下载
- **react-error-boundary**: 错误边界
- **react-helmet-async**: 头部管理

## 📁 项目结构

```
frontend/
├── public/                          # 静态资源
│   ├── index.html                   # HTML模板
│   └── favicon.ico                  # 图标
├── src/                             # 源代码
│   ├── components/                  # 组件库
│   │   ├── common/                  # 通用组件
│   │   │   ├── ErrorBoundary/       # 错误边界
│   │   │   ├── Loading/             # 加载组件
│   │   │   ├── Layout/              # 布局组件
│   │   │   └── index.ts             # 导出文件
│   │   ├── crawler/                 # 爬虫相关组件
│   │   │   ├── CrawlControl/        # 爬取控制
│   │   │   │   ├── index.tsx
│   │   │   │   ├── CrawlConfig.tsx  # 爬取配置
│   │   │   │   └── CrawlControl.module.css
│   │   │   ├── ProgressMonitor/     # 进度监控
│   │   │   │   ├── index.tsx
│   │   │   │   ├── ProgressBar.tsx  # 进度条
│   │   │   │   ├── StatusIndicator.tsx # 状态指示器
│   │   │   │   └── ProgressMonitor.module.css
│   │   │   ├── DataPreview/         # 数据预览
│   │   │   │   ├── index.tsx
│   │   │   │   ├── ProductTable.tsx # 商品表格
│   │   │   │   ├── ProductCard.tsx  # 商品卡片
│   │   │   │   ├── GroupByKeyword.tsx # 关键词分组
│   │   │   │   └── DataPreview.module.css
│   │   │   ├── CookieManager/       # Cookie管理
│   │   │   │   ├── index.tsx
│   │   │   │   ├── CookieImport.tsx # Cookie导入
│   │   │   │   ├── CookieStatus.tsx # 状态显示
│   │   │   │   └── CookieManager.module.css
│   │   │   └── ExportManager/       # 导出管理
│   │   │       ├── index.tsx
│   │   │       ├── ExportConfig.tsx # 导出配置
│   │   │       └── ExportManager.module.css
│   │   └── index.ts                 # 组件统一导出
│   ├── hooks/                       # 自定义Hook
│   │   ├── useWebSocket.ts          # WebSocket管理
│   │   ├── useApi.ts                # API调用
│   │   ├── useCrawler.ts            # 爬虫状态管理
│   │   ├── useCookie.ts             # Cookie管理
│   │   └── index.ts
│   ├── services/                    # 服务层
│   │   ├── api/                     # API客户端
│   │   │   ├── crawler.ts           # 爬虫API
│   │   │   ├── cookie.ts            # Cookie API
│   │   │   ├── export.ts            # 导出API
│   │   │   ├── types.ts             # API类型定义
│   │   │   └── index.ts
│   │   ├── websocket/               # WebSocket服务
│   │   │   ├── CrawlerWebSocket.ts  # 爬虫WebSocket
│   │   │   ├── types.ts             # WebSocket类型
│   │   │   └── index.ts
│   │   └── index.ts
│   ├── stores/                      # 状态管理
│   │   ├── crawlerStore.ts          # 爬虫状态
│   │   ├── cookieStore.ts           # Cookie状态
│   │   ├── appStore.ts              # 应用状态
│   │   └── index.ts
│   ├── types/                       # 类型定义
│   │   ├── crawler.ts               # 爬虫相关类型
│   │   ├── cookie.ts                # Cookie类型
│   │   ├── product.ts               # 商品数据类型
│   │   ├── websocket.ts             # WebSocket类型
│   │   └── index.ts
│   ├── utils/                       # 工具函数
│   │   ├── request.ts               # 请求封装
│   │   ├── websocket.ts             # WebSocket工具
│   │   ├── format.ts                # 格式化工具
│   │   ├── validation.ts            # 验证工具
│   │   ├── storage.ts               # 存储工具
│   │   ├── constants.ts             # 常量定义
│   │   └── index.ts
│   ├── styles/                      # 样式文件
│   │   ├── globals.css              # 全局样式
│   │   ├── variables.css            # CSS变量
│   │   ├── antd-theme.ts            # Ant Design主题
│   │   └── index.ts
│   ├── App.tsx                      # 根组件
│   ├── main.tsx                     # 入口文件
│   └── vite-env.d.ts               # Vite类型声明
├── .env                            # 环境变量
├── .env.development                # 开发环境变量
├── .env.production                 # 生产环境变量
├── .gitignore                      # Git忽略文件
├── index.html                      # HTML入口
├── package.json                    # 项目配置
├── tsconfig.json                   # TypeScript配置
├── tsconfig.node.json              # Node.js TypeScript配置
├── vite.config.ts                  # Vite配置
└── README.md                       # 项目说明
```

## 🎨 组件架构设计

### 组件层次结构

```
App (根组件)
├── Layout (布局组件)
│   ├── Header (顶部导航)
│   ├── Sidebar (侧边栏)
│   └── Content (主内容区)
│       ├── CrawlControl (爬取控制)
│       │   ├── CrawlConfig (配置面板)
│       │   │   ├── KeywordInput (关键词输入)
│       │   │   ├── FilterConfig (筛选配置)
│       │   │   └── AdvancedSettings (高级设置)
│       │   └── ActionButtons (操作按钮)
│       ├── ProgressMonitor (进度监控)
│       │   ├── OverallProgress (总体进度)
│       │   ├── KeywordProgress (关键词进度)
│       │   ├── StatusIndicator (状态指示)
│       │   └── WebSocketStatus (连接状态)
│       ├── DataPreview (数据预览)
│       │   ├── ProductTable (商品表格)
│       │   │   ├── TableHeader (表头)
│       │   │   ├── TableBody (表体)
│       │   │   └── TablePagination (分页)
│       │   ├── KeywordTabs (关键词标签)
│       │   └── ProductStats (统计信息)
│       ├── CookieManager (Cookie管理)
│       │   ├── CookieImport (导入面板)
│       │   ├── CookieStatus (状态显示)
│       │   └── CookieList (Cookie列表)
│       └── ExportManager (导出管理)
│           ├── ExportConfig (导出配置)
│           ├── FormatSelector (格式选择)
│           └── DownloadProgress (下载进度)
└── ErrorBoundary (错误边界)
```

### 组件设计原则

#### 1. 单一职责原则
每个组件只负责一个明确的功能，便于维护和测试。

#### 2. 组合优于继承
使用组合模式构建复杂组件，提高代码复用性。

#### 3. Props接口设计
```typescript
// 示例：CrawlControl组件接口
interface CrawlControlProps {
  onStart: (config: CrawlConfig) => void;
  onStop: () => void;
  onPause: () => void;
  onResume: () => void;
  isRunning: boolean;
  isPaused: boolean;
  config: CrawlConfig;
  className?: string;
  disabled?: boolean;
}
```

## 🗃️ 状态管理架构

### Zustand Store设计

#### 1. Crawler Store (爬虫状态)
```typescript
interface CrawlerState {
  // 任务状态
  currentTask: CrawlTask | null;
  taskHistory: CrawlTask[];
  isRunning: boolean;
  isPaused: boolean;
  
  // 配置状态
  config: CrawlConfig;
  
  // 数据状态
  products: Product[];
  productsByKeyword: Record<string, Product[]>;
  statistics: CrawlStatistics;
  
  // 进度状态
  progress: ProgressInfo;
  
  // 操作方法
  startCrawl: (config: CrawlConfig) => Promise<void>;
  stopCrawl: () => Promise<void>;
  pauseCrawl: () => Promise<void>;
  resumeCrawl: () => Promise<void>;
  updateProgress: (progress: ProgressInfo) => void;
  addProducts: (products: Product[], keyword?: string) => void;
  clearData: () => void;
}
```

#### 2. Cookie Store (Cookie状态)
```typescript
interface CookieState {
  // Cookie数据
  cookies: CookieData[];
  status: CookieStatus;
  lastUpdate: string | null;
  
  // 状态标记
  isValid: boolean;
  isLoading: boolean;
  error: string | null;
  
  // 操作方法
  importCookies: (cookieData: string | CookieData[]) => Promise<void>;
  saveCookies: () => Promise<void>;
  clearCookies: () => Promise<void>;
  validateCookies: () => Promise<boolean>;
  refreshStatus: () => Promise<void>;
}
```

#### 3. App Store (应用状态)
```typescript
interface AppState {
  // UI状态
  theme: 'light' | 'dark';
  language: 'zh-CN' | 'en-US';
  sidebarCollapsed: boolean;
  
  // 系统状态
  isOnline: boolean;
  backendStatus: 'connected' | 'disconnected' | 'error';
  
  // 用户偏好
  preferences: UserPreferences;
  
  // 操作方法
  setTheme: (theme: 'light' | 'dark') => void;
  setLanguage: (lang: 'zh-CN' | 'en-US') => void;
  toggleSidebar: () => void;
  updateBackendStatus: (status: string) => void;
  savePreferences: (prefs: UserPreferences) => void;
}
```

## 🌐 网络通信架构

### HTTP API设计

#### 1. API客户端封装
```typescript
// services/api/client.ts
class ApiClient {
  private baseURL: string;
  private timeout: number;
  private axiosInstance: AxiosInstance;
  
  constructor(config: ApiConfig) {
    this.baseURL = config.baseURL;
    this.timeout = config.timeout;
    this.axiosInstance = this.createInstance();
    this.setupInterceptors();
  }
  
  private createInstance(): AxiosInstance {
    return axios.create({
      baseURL: this.baseURL,
      timeout: this.timeout,
      headers: {
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest',
      },
    });
  }
  
  private setupInterceptors() {
    // 请求拦截器
    this.axiosInstance.interceptors.request.use(
      (config) => {
        // 添加请求ID用于追踪
        config.headers['X-Request-ID'] = Date.now().toString();
        console.log(`API请求: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        console.error('请求拦截错误:', error);
        return Promise.reject(error);
      }
    );
    
    // 响应拦截器
    this.axiosInstance.interceptors.response.use(
      (response) => {
        console.log(`API响应: ${response.status} ${response.config.url}`);
        return response.data;
      },
      (error) => {
        const errorInfo = this.handleError(error);
        console.error('API错误:', errorInfo);
        return Promise.reject(errorInfo);
      }
    );
  }
  
  private handleError(error: AxiosError): ApiError {
    if (error.response) {
      // 服务器响应错误
      return {
        code: error.response.status,
        message: error.response.data?.message || error.message,
        details: error.response.data,
      };
    } else if (error.request) {
      // 网络错误
      return {
        code: 0,
        message: '网络连接失败，请检查网络状态',
        details: error.request,
      };
    } else {
      // 其他错误
      return {
        code: -1,
        message: error.message || '未知错误',
        details: error,
      };
    }
  }
  
  // 通用请求方法
  private async request<T>(config: AxiosRequestConfig): Promise<T> {
    return this.axiosInstance.request(config);
  }
  
  async get<T>(url: string, params?: any): Promise<T> {
    return this.request({ method: 'GET', url, params });
  }
  
  async post<T>(url: string, data?: any): Promise<T> {
    return this.request({ method: 'POST', url, data });
  }
  
  async put<T>(url: string, data?: any): Promise<T> {
    return this.request({ method: 'PUT', url, data });
  }
  
  async delete<T>(url: string): Promise<T> {
    return this.request({ method: 'DELETE', url });
  }
}

// services/api/crawler.ts - 爬虫API
export class CrawlerApi extends ApiClient {
  // 启动爬虫任务
  async startCrawl(config: CrawlConfig): Promise<CrawlResponse> {
    return this.post('/api/crawl/start', {
      keywords: config.keywords,
      targetCount: config.targetCount,
      sortMethod: config.sortMethod || 'default',
      maxPages: config.maxPages || 5,
      headless: config.headless ?? true,
      enableFilter: config.enableFilter ?? false,
    });
  }
  
  // 获取任务状态
  async getCrawlStatus(taskId: string): Promise<CrawlStatus> {
    return this.get(`/api/crawl/${taskId}/status`);
  }
  
  // 暂停任务
  async pauseCrawl(taskId: string): Promise<void> {
    return this.post(`/api/crawl/${taskId}/pause`);
  }
  
  // 恢复任务
  async resumeCrawl(taskId: string): Promise<void> {
    return this.post(`/api/crawl/${taskId}/resume`);
  }
  
  // 停止任务
  async stopCrawl(taskId: string): Promise<void> {
    return this.post(`/api/crawl/${taskId}/stop`);
  }
  
  // 获取预览数据
  async getPreviewData(taskId: string, limit: number = 20): Promise<PreviewData> {
    return this.get(`/api/crawl/${taskId}/preview`, { limit });
  }
  
  // 导出Excel
  async exportExcel(taskId: string): Promise<ExportResponse> {
    return this.post(`/api/export/${taskId}`);
  }
  
  // 导出CSV
  async exportCsv(taskId: string): Promise<ExportResponse> {
    return this.post(`/api/export/${taskId}/csv`);
  }
  
  // 下载导出文件
  async downloadExport(taskId: string, format: 'xlsx' | 'csv' = 'xlsx'): Promise<Blob> {
    const response = await this.axiosInstance.get(
      `/api/export/${taskId}/download`,
      {
        params: { format },
        responseType: 'blob',
      }
    );
    return response.data;
  }
}

// services/api/cookie.ts - Cookie API
export class CookieApi extends ApiClient {
  // 获取Cookie状态
  async getCookieStatus(): Promise<CookieStatus> {
    return this.get('/api/cookie/status');
  }
  
  // 验证Cookie
  async validateCookies(cookies: CookieData[]): Promise<ValidationResult> {
    return this.post('/api/cookie/validate', { cookies });
  }
  
  // 保存Cookie
  async saveCookies(cookies: CookieData[]): Promise<void> {
    return this.post('/api/cookie/save', { cookies });
  }
  
  // 导入Cookie
  async importCookies(data: CookieImportData): Promise<void> {
    return this.post('/api/cookie/import', data);
  }
  
  // 导出Cookie
  async exportCookies(): Promise<CookieExportData> {
    return this.get('/api/cookie/export');
  }
  
  // 清除Cookie
  async clearCookies(): Promise<void> {
    return this.delete('/api/cookie/clear');
  }
}
```

#### 2. API接口类型定义
```typescript
// types/api.ts
export interface ApiConfig {
  baseURL: string;
  timeout: number;
}

export interface ApiError {
  code: number;
  message: string;
  details?: any;
}

// 爬虫相关类型
export interface CrawlRequest {
  keywords: string[];
  targetCount: number;
  sortMethod?: 'default' | 'price_asc' | 'price_desc' | 'sales_desc';
  maxPages?: number;
  headless?: boolean;
  enableFilter?: boolean;
}

export interface CrawlResponse {
  success: boolean;
  taskId: string;
  message: string;
}

export interface CrawlStatus {
  taskId: string;
  status: 'pending' | 'running' | 'paused' | 'completed' | 'failed';
  progress: {
    current: number;
    total: number;
    percentage: number;
  };
  startTime: string;
  config: CrawlRequest;
  error?: string;
}

export interface PreviewData {
  data: Product[];
  total: number;
}

export interface ExportResponse {
  success: boolean;
  exportId: string;
  fileName: string;
  fileSize: number;
}

// Cookie相关类型
export interface CookieData {
  name: string;
  value: string;
  domain: string;
  path: string;
  expires?: number;
  httpOnly?: boolean;
  secure?: boolean;
  sameSite?: 'strict' | 'lax' | 'none';
}

export interface CookieStatus {
  exists: boolean;
  valid: boolean;
  expiresAt?: string;
  cookies: CookieData[];
}

export interface ValidationResult {
  valid: boolean;
  message: string;
}

export interface CookieImportData {
  cookies?: CookieData[];
  cookieString?: string;
}

export interface CookieExportData {
  success: boolean;
  data: {
    cookies: CookieData[];
  };
  message: string;
}
```

### WebSocket通信设计（react-use-websocket集成）

#### 1. WebSocket配置和Hook
```typescript
// hooks/useWebSocket.ts
import useWebSocketLib, { ReadyState } from 'react-use-websocket';

interface WebSocketConfig {
  shouldReconnect?: boolean;
  reconnectAttempts?: number;
  reconnectInterval?: number | ((attemptNumber: number) => number);
  onOpen?: () => void;
  onClose?: () => void;
  onError?: (error: Event) => void;
  onMessage?: (message: MessageEvent) => void;
}

export function useCrawlerWebSocket(taskId: string | null, config?: WebSocketConfig) {
  const socketUrl = taskId ? `${WS_BASE_URL}/ws/crawl/${taskId}` : null;
  
  const {
    sendMessage,
    sendJsonMessage,
    lastMessage,
    lastJsonMessage,
    readyState,
    getWebSocket,
  } = useWebSocketLib(
    socketUrl,
    {
      // 自动重连配置
      shouldReconnect: (closeEvent) => {
        console.log('WebSocket关闭事件:', closeEvent);
        return config?.shouldReconnect ?? true;
      },
      
      // 重连尝试次数
      reconnectAttempts: config?.reconnectAttempts ?? 10,
      
      // 重连间隔策略（指数退避）
      reconnectInterval: config?.reconnectInterval ?? ((attemptNumber) => {
        const baseDelay = 1000; // 1秒基础延迟
        const maxDelay = 10000; // 最大10秒延迟
        const exponentialDelay = Math.min(Math.pow(2, attemptNumber) * baseDelay, maxDelay);
        console.log(`WebSocket重连延迟: ${exponentialDelay}ms (尝试 ${attemptNumber + 1})`);
        return exponentialDelay;
      }),
      
      // 心跳配置
      heartbeat: {
        message: JSON.stringify({ type: 'ping' }),
        returnMessage: JSON.stringify({ type: 'pong' }),
        timeout: 60000, // 60秒超时
        interval: 25000, // 25秒间隔
      },
      
      // 连接生命周期回调
      onOpen: (event) => {
        console.log('WebSocket连接已建立:', event);
        config?.onOpen?.();
      },
      
      onClose: (event) => {
        console.log('WebSocket连接已关闭:', event.code, event.reason);
        config?.onClose?.();
      },
      
      onError: (event) => {
        console.error('WebSocket连接错误:', event);
        config?.onError?.(event);
      },
      
      onMessage: (event) => {
        console.log('WebSocket收到消息:', event.data);
        config?.onMessage?.(event);
      },
      
      // 过滤器：只处理有效的JSON消息
      filter: (message) => {
        try {
          const data = JSON.parse(message.data);
          return data && typeof data === 'object';
        } catch {
          return false;
        }
      },
      
      // 协议配置
      protocols: ['crawler-protocol'],
      
      // 查询参数
      queryParams: {
        version: '1.0',
        client: 'react-frontend',
      },
    }
  );
  
  // 连接状态映射
  const connectionStatus = {
    [ReadyState.CONNECTING]: 'connecting',
    [ReadyState.OPEN]: 'connected',
    [ReadyState.CLOSING]: 'closing',
    [ReadyState.CLOSED]: 'closed',
    [ReadyState.UNINSTANTIATED]: 'uninstantiated',
  }[readyState] as ConnectionStatus;
  
  // 连接状态检查
  const isConnected = readyState === ReadyState.OPEN;
  const isConnecting = readyState === ReadyState.CONNECTING;
  const isClosed = readyState === ReadyState.CLOSED;
  
  // 发送心跳消息
  const sendHeartbeat = useCallback(() => {
    if (isConnected) {
      sendJsonMessage({ type: 'ping', timestamp: Date.now() });
    }
  }, [isConnected, sendJsonMessage]);
  
  // 手动重连
  const reconnect = useCallback(() => {
    const ws = getWebSocket();
    if (ws) {
      ws.close();
    }
  }, [getWebSocket]);
  
  return {
    // 消息发送
    sendMessage,
    sendJsonMessage,
    
    // 消息接收
    lastMessage,
    lastJsonMessage,
    
    // 连接状态
    connectionStatus,
    isConnected,
    isConnecting,
    isClosed,
    
    // 连接控制
    reconnect,
    sendHeartbeat,
    
    // WebSocket实例
    getWebSocket,
  };
}
```

#### 2. WebSocket消息处理系统
```typescript
// services/websocket/messageHandler.ts
export interface WebSocketMessage {
  type: 'connected' | 'progress' | 'data' | 'keyword_started' | 'keyword_completed' | 'completed' | 'error' | 'heartbeat';
  data?: any;
  keyword?: string;
  keyword_index?: number;
  timestamp?: number;
}

export class WebSocketMessageHandler {
  private listeners: Map<string, Set<MessageListener>> = new Map();
  
  // 注册消息监听器
  public subscribe(type: string, listener: MessageListener): () => void {
    if (!this.listeners.has(type)) {
      this.listeners.set(type, new Set());
    }
    
    this.listeners.get(type)!.add(listener);
    
    // 返回取消订阅函数
    return () => {
      this.listeners.get(type)?.delete(listener);
    };
  }
  
  // 处理收到的消息
  public handleMessage(message: WebSocketMessage): void {
    const { type } = message;
    const listeners = this.listeners.get(type);
    
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(message);
        } catch (error) {
          console.error(`处理WebSocket消息失败 (type: ${type}):`, error);
        }
      });
    }
    
    // 通用消息处理
    this.handleCommonMessages(message);
  }
  
  private handleCommonMessages(message: WebSocketMessage): void {
    switch (message.type) {
      case 'connected':
        console.log('WebSocket连接已确认:', message.data);
        break;
        
      case 'heartbeat':
        console.debug('收到心跳消息');
        break;
        
      case 'error':
        console.error('WebSocket错误消息:', message.data);
        break;
        
      default:
        console.debug('收到WebSocket消息:', message.type, message.data);
    }
  }
}

// hooks/useWebSocketHandler.ts
export function useWebSocketHandler(taskId: string | null) {
  const crawlerStore = useCrawlerStore();
  const [messageHandler] = useState(() => new WebSocketMessageHandler());
  
  const { lastJsonMessage, connectionStatus, isConnected } = useCrawlerWebSocket(
    taskId,
    {
      onOpen: () => {
        console.log(`WebSocket连接建立: taskId=${taskId}`);
      },
      onClose: () => {
        console.log(`WebSocket连接关闭: taskId=${taskId}`);
      },
      onError: (error) => {
        console.error(`WebSocket连接错误: taskId=${taskId}`, error);
      },
    }
  );
  
  // 处理收到的消息
  useEffect(() => {
    if (lastJsonMessage) {
      messageHandler.handleMessage(lastJsonMessage as WebSocketMessage);
    }
  }, [lastJsonMessage, messageHandler]);
  
  // 注册爬虫特定的消息处理器
  useEffect(() => {
    const unsubscribers = [
      // 进度更新
      messageHandler.subscribe('progress', (message) => {
        crawlerStore.updateProgress(message.data);
      }),
      
      // 数据推送
      messageHandler.subscribe('data', (message) => {
        crawlerStore.addProducts(message.data, message.keyword);
      }),
      
      // 关键词开始
      messageHandler.subscribe('keyword_started', (message) => {
        crawlerStore.updateKeywordStatus(message.keyword!, 'started', message.data);
      }),
      
      // 关键词完成
      messageHandler.subscribe('keyword_completed', (message) => {
        crawlerStore.updateKeywordStatus(message.keyword!, 'completed', message.data);
      }),
      
      // 任务完成
      messageHandler.subscribe('completed', (message) => {
        crawlerStore.setTaskCompleted(message.data);
      }),
      
      // 错误处理
      messageHandler.subscribe('error', (message) => {
        crawlerStore.setError(message.data?.message || '未知错误');
      }),
    ];
    
    return () => {
      unsubscribers.forEach(unsubscribe => unsubscribe());
    };
  }, [messageHandler, crawlerStore]);
  
  return {
    connectionStatus,
    isConnected,
    messageHandler,
  };
}

// 类型定义
type MessageListener = (message: WebSocketMessage) => void;
type ConnectionStatus = 'connecting' | 'connected' | 'closing' | 'closed' | 'uninstantiated';
```

#### 3. WebSocket连接状态监控
```typescript
// hooks/useWebSocketStatus.ts
export function useWebSocketStatus(taskId: string | null) {
  const [connectionMetrics, setConnectionMetrics] = useState<ConnectionMetrics>({
    connectedAt: null,
    lastMessageAt: null,
    messageCount: 0,
    reconnectCount: 0,
    latency: 0,
  });
  
  const { connectionStatus, isConnected, lastJsonMessage } = useCrawlerWebSocket(
    taskId,
    {
      onOpen: () => {
        setConnectionMetrics(prev => ({
          ...prev,
          connectedAt: new Date(),
          reconnectCount: prev.reconnectCount + 1,
        }));
      },
      onMessage: () => {
        setConnectionMetrics(prev => ({
          ...prev,
          lastMessageAt: new Date(),
          messageCount: prev.messageCount + 1,
        }));
      },
    }
  );
  
  // 计算连接延迟
  useEffect(() => {
    if (isConnected) {
      const startTime = Date.now();
      
      // 发送ping消息并计算延迟
      const ws = getWebSocket();
      if (ws) {
        ws.send(JSON.stringify({ type: 'ping', timestamp: startTime }));
      }
    }
  }, [isConnected]);
  
  // 监听pong消息计算延迟
  useEffect(() => {
    if (lastJsonMessage?.type === 'pong') {
      const latency = Date.now() - (lastJsonMessage.timestamp || 0);
      setConnectionMetrics(prev => ({
        ...prev,
        latency,
      }));
    }
  }, [lastJsonMessage]);
  
  return {
    connectionStatus,
    isConnected,
    metrics: connectionMetrics,
  };
}

// utils/constants.ts - 常量定义
export const WS_BASE_URL = import.meta.env.VITE_WS_BASE_URL || 'ws://localhost:8000';
export const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';

export const WEBSOCKET_CONFIG = {
  RECONNECT_ATTEMPTS: 10,
  BASE_RECONNECT_DELAY: 1000,
  MAX_RECONNECT_DELAY: 10000,
  HEARTBEAT_INTERVAL: 25000,
  HEARTBEAT_TIMEOUT: 60000,
  MESSAGE_QUEUE_SIZE: 100,
} as const;

export const API_CONFIG = {
  TIMEOUT: 30000,
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,
} as const;

// types/websocket.ts - WebSocket类型定义
export interface ConnectionMetrics {
  connectedAt: Date | null;
  lastMessageAt: Date | null;
  messageCount: number;
  reconnectCount: number;
  latency: number;
}

export interface WebSocketState {
  taskId: string | null;
  connectionStatus: ConnectionStatus;
  isConnected: boolean;
  metrics: ConnectionMetrics;
  error: string | null;
}

export interface CrawlerWebSocketMessage {
  type: 'connected' | 'progress' | 'data' | 'keyword_started' | 'keyword_completed' | 
        'completed' | 'error' | 'heartbeat' | 'ping' | 'pong';
  data?: any;
  keyword?: string;
  keyword_index?: number;
  timestamp?: number;
}

// 后端WebSocket消息映射（与api_server.py保持一致）
export interface BackendWebSocketMessage {
  // 连接确认消息
  connected: {
    taskId: string;
    status: string;
    startTime: string;
  };
  
  // 进度更新消息
  progress: {
    current: number;
    total: number;
    percentage: number;
    status: string;
  };
  
  // 数据推送消息
  data: Product[];
  
  // 关键词处理消息
  keyword_started: {
    keyword: string;
    index: number;
    status: 'started';
  };
  
  keyword_completed: {
    keyword: string;
    index: number;
    collected_count: number;
    status: 'completed';
  };
  
  // 任务完成消息
  completed: {
    success: boolean;
    message: string;
    totalGoods: number;
  };
  
  // 错误消息
  error: {
    message: string;
  };
  
  // 心跳消息
  heartbeat: {};
}

// services/websocket/messageQueue.ts - 消息队列管理
export class WebSocketMessageQueue {
  private queue: WebSocketMessage[] = [];
  private maxSize: number;
  private processing = false;
  
  constructor(maxSize = WEBSOCKET_CONFIG.MESSAGE_QUEUE_SIZE) {
    this.maxSize = maxSize;
  }
  
  // 添加消息到队列
  public enqueue(message: WebSocketMessage): void {
    if (this.queue.length >= this.maxSize) {
      // 移除最旧的消息
      this.queue.shift();
    }
    
    this.queue.push({
      ...message,
      timestamp: Date.now(),
    });
  }
  
  // 批量处理消息
  public async processQueue(handler: (messages: WebSocketMessage[]) => Promise<void>): Promise<void> {
    if (this.processing || this.queue.length === 0) {
      return;
    }
    
    this.processing = true;
    
    try {
      const messages = this.queue.splice(0);
      await handler(messages);
    } catch (error) {
      console.error('处理WebSocket消息队列失败:', error);
    } finally {
      this.processing = false;
    }
  }
  
  // 获取队列大小
  public size(): number {
    return this.queue.length;
  }
  
  // 清理队列
  public clear(): void {
    this.queue.length = 0;
  }
}

## 📊 数据模型设计

### 核心数据类型

#### 1. 商品数据模型
```typescript
interface Product {
  // 基础信息
  goods_id: string;
  goods_name: string;
  price: number;
  original_price?: number;
  currency: string;
  
  // 媒体信息
  thumb_url: string;
  images: string[];
  
  // 销售信息
  sales: number;
  sales_tip: string;
  min_group_price: number;
  min_normal_price: number;
  
  // 评价信息
  review_count: number;
  average_rating: number;
  
  // 商家信息
  mall_name: string;
  mall_id: string;
  mall_rate: number;
  
  // 营销信息
  coupons: Coupon[];
  activity_tags: string[];
  subsidy_info: string; // 补贴详情
  
  // 技术信息
  goods_url: string;
  collect_time: string;
  keyword?: string; // 关联的搜索关键词
  
  // 扩展字段
  [key: string]: any;
}
```

#### 2. 爬取配置模型
```typescript
interface CrawlConfig {
  keywords: string[];
  targetCount: number;
  sortMethod: 'default' | 'price_asc' | 'price_desc' | 'sales_desc';
  maxPages: number;
  headless: boolean;
  enableFilter: boolean;
  filterConfig?: FilterConfig;
}

interface FilterConfig {
  minPrice?: number;
  maxPrice?: number;
  minSales?: number;
  brands?: string[];
  excludeKeywords?: string[];
}
```

#### 3. 任务状态模型
```typescript
interface CrawlTask {
  id: string;
  config: CrawlConfig;
  status: 'pending' | 'running' | 'paused' | 'completed' | 'failed';
  progress: ProgressInfo;
  startTime: string;
  endTime?: string;
  error?: string;
  results: Product[];
}

interface ProgressInfo {
  current: number;
  total: number;
  percentage: number;
  currentKeyword?: string;
  keywordProgress: Record<string, KeywordProgress>;
}

interface KeywordProgress {
  keyword: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  current: number;
  target: number;
  percentage: number;
}
```

## 🎨 UI/UX设计规范

### 设计系统

#### 1. 颜色系统
```css
:root {
  /* 主色调 */
  --primary-color: #ff5c00;
  --primary-hover: #ff7a33;
  --primary-active: #e64a00;
  
  /* 辅助色 */
  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #ff4d4f;
  --info-color: #1890ff;
  
  /* 中性色 */
  --gray-1: #ffffff;
  --gray-2: #fafafa;
  --gray-3: #f5f5f5;
  --gray-4: #f0f0f0;
  --gray-5: #d9d9d9;
  --gray-6: #bfbfbf;
  --gray-7: #8c8c8c;
  --gray-8: #595959;
  --gray-9: #434343;
  --gray-10: #262626;
  --gray-11: #1f1f1f;
  --gray-12: #141414;
  --gray-13: #000000;
  
  /* 功能色 */
  --background-color: var(--gray-1);
  --surface-color: var(--gray-2);
  --border-color: var(--gray-5);
  --text-primary: var(--gray-10);
  --text-secondary: var(--gray-7);
  --text-disabled: var(--gray-6);
}
```

#### 2. 字体系统
```css
:root {
  /* 字体族 */
  --font-family-base: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
  --font-family-code: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
  
  /* 字体大小 */
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-base: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-2xl: 24px;
  --font-size-3xl: 32px;
  
  /* 行高 */
  --line-height-tight: 1.25;
  --line-height-base: 1.5;
  --line-height-loose: 1.75;
  
  /* 字重 */
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
}
```

#### 3. 间距系统
```css
:root {
  /* 间距 */
  --space-1: 4px;
  --space-2: 8px;
  --space-3: 12px;
  --space-4: 16px;
  --space-5: 20px;
  --space-6: 24px;
  --space-8: 32px;
  --space-10: 40px;
  --space-12: 48px;
  --space-16: 64px;
  --space-20: 80px;
  --space-24: 96px;
  
  /* 圆角 */
  --border-radius-sm: 2px;
  --border-radius-base: 6px;
  --border-radius-lg: 8px;
  --border-radius-xl: 12px;
  --border-radius-full: 9999px;
  
  /* 阴影 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}
```

### 响应式设计

#### 1. 断点系统
```css
:root {
  --breakpoint-xs: 480px;
  --breakpoint-sm: 576px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 992px;
  --breakpoint-xl: 1200px;
  --breakpoint-2xl: 1600px;
}
```

#### 2. 布局规则
- **移动优先**: 从最小屏幕开始设计，逐步增强
- **栅格系统**: 使用Ant Design的24栅格系统
- **弹性布局**: 优先使用Flexbox和Grid布局
- **相对单位**: 使用rem、em、%等相对单位

## ⚡ 性能优化策略

### 1. 代码分割
```typescript
// 路由级别的代码分割
const CrawlControl = lazy(() => import('./components/crawler/CrawlControl'));
const DataPreview = lazy(() => import('./components/crawler/DataPreview'));
const CookieManager = lazy(() => import('./components/crawler/CookieManager'));

// 组件级别的代码分割
const ExportManager = lazy(() => import('./components/crawler/ExportManager'));
```

### 2. 组件优化
```typescript
// 使用React.memo减少不必要的重渲染
const ProductCard = React.memo<ProductCardProps>(({ product, onClick }) => {
  return (
    <Card onClick={() => onClick(product.goods_id)}>
      {/* 组件内容 */}
    </Card>
  );
});

// 使用useMemo缓存计算结果
const statistics = useMemo(() => {
  return calculateProductStatistics(products);
}, [products]);

// 使用useCallback缓存函数引用
const handleProductClick = useCallback((productId: string) => {
  // 处理逻辑
}, []);
```

### 3. 虚拟滚动
```typescript
// 对于大量商品数据使用虚拟滚动
import { FixedSizeList as List } from 'react-window';

const ProductList: React.FC<{ products: Product[] }> = ({ products }) => {
  const Row = ({ index, style }: any) => (
    <div style={style}>
      <ProductCard product={products[index]} />
    </div>
  );

  return (
    <List
      height={600}
      itemCount={products.length}
      itemSize={120}
      width="100%"
    >
      {Row}
    </List>
  );
};
```

### 4. 数据缓存
```typescript
// 使用React Query进行数据缓存
import { useQuery } from '@tanstack/react-query';

function useProductData(taskId: string) {
  return useQuery({
    queryKey: ['products', taskId],
    queryFn: () => api.getProducts(taskId),
    staleTime: 5 * 60 * 1000, // 5分钟
    cacheTime: 10 * 60 * 1000, // 10分钟
  });
}
```

## 🔒 安全考虑

### 1. 输入验证
```typescript
// 前端验证
function validateKeywords(keywords: string[]): ValidationResult {
  const errors: string[] = [];
  
  if (keywords.length === 0) {
    errors.push('至少需要一个关键词');
  }
  
  keywords.forEach((keyword, index) => {
    if (keyword.trim().length === 0) {
      errors.push(`第${index + 1}个关键词不能为空`);
    }
    
    if (keyword.length > 50) {
      errors.push(`第${index + 1}个关键词长度不能超过50个字符`);
    }
    
    // 检查特殊字符
    if (!/^[\u4e00-\u9fa5a-zA-Z0-9\s\-_]+$/.test(keyword)) {
      errors.push(`第${index + 1}个关键词包含非法字符`);
    }
  });
  
  return {
    isValid: errors.length === 0,
    errors
  };
}
```

### 2. XSS防护
```typescript
// 使用DOMPurify清理HTML内容
import DOMPurify from 'dompurify';

function SafeHtml({ html }: { html: string }) {
  const cleanHtml = DOMPurify.sanitize(html);
  return <div dangerouslySetInnerHTML={{ __html: cleanHtml }} />;
}
```

### 3. CSRF防护
```typescript
// 在请求头中添加CSRF Token
axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';
```

## 🧪 测试策略

### 1. 单元测试
```typescript
// 使用Jest和React Testing Library
describe('KeywordInput', () => {
  test('should parse comma-separated keywords', () => {
    const { getByRole, getByText } = render(
      <KeywordInput onChange={mockOnChange} />
    );
    
    const input = getByRole('textbox');
    fireEvent.change(input, { target: { value: '手机,电脑,平板' } });
    
    expect(getByText('手机')).toBeInTheDocument();
    expect(getByText('电脑')).toBeInTheDocument();
    expect(getByText('平板')).toBeInTheDocument();
  });
});
```

### 2. 集成测试
```typescript
// 测试组件间的交互
describe('CrawlControl Integration', () => {
  test('should start crawl when form is submitted', async () => {
    const mockStart = jest.fn();
    
    render(
      <CrawlControl onStart={mockStart} isRunning={false} />
    );
    
    // 填写表单
    fireEvent.change(screen.getByLabelText('关键词'), {
      target: { value: '手机' }
    });
    
    // 提交表单
    fireEvent.click(screen.getByText('开始爬取'));
    
    expect(mockStart).toHaveBeenCalledWith({
      keywords: ['手机'],
      targetCount: 100,
      // ... 其他配置
    });
  });
});
```

### 3. E2E测试
```typescript
// 使用Cypress进行端到端测试
describe('Crawler Workflow', () => {
  it('should complete full crawl workflow', () => {
    cy.visit('/');
    
    // 导入Cookie
    cy.get('[data-testid=cookie-import]').click();
    cy.get('[data-testid=cookie-input]').type('{selectall}' + cookieData);
    cy.get('[data-testid=save-cookie]').click();
    
    // 配置爬取
    cy.get('[data-testid=keyword-input]').type('手机');
    cy.get('[data-testid=target-count]').clear().type('10');
    
    // 开始爬取
    cy.get('[data-testid=start-crawl]').click();
    
    // 等待完成
    cy.get('[data-testid=progress-bar]', { timeout: 60000 })
      .should('have.attr', 'aria-valuenow', '100');
    
    // 检查结果
    cy.get('[data-testid=product-count]').should('contain', '10');
    
    // 导出数据
    cy.get('[data-testid=export-excel]').click();
    cy.readFile('cypress/downloads/拼多多商品数据_手机_*.xlsx')
      .should('exist');
  });
});
```

## 🚀 部署与构建

### 1. 构建配置
```typescript
// vite.config.ts
export default defineConfig({
  plugins: [react()],
  build: {
    target: 'es2015',
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false,
    minify: 'terser',
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          antd: ['antd'],
          utils: ['dayjs', 'axios'],
        },
      },
    },
  },
  server: {
    port: 5173,
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
      },
      '/ws': {
        target: 'ws://localhost:8000',
        ws: true,
      },
    },
  },
});
```

### 2. 环境配置
```bash
# .env.development
VITE_API_BASE_URL=http://localhost:8000
VITE_WS_BASE_URL=ws://localhost:8000
VITE_APP_TITLE=拼多多爬虫系统
VITE_ENVIRONMENT=development

# .env.production
VITE_API_BASE_URL=https://api.example.com
VITE_WS_BASE_URL=wss://api.example.com
VITE_APP_TITLE=拼多多爬虫系统
VITE_ENVIRONMENT=production
```

### 3. Docker部署
```dockerfile
# Dockerfile
FROM node:18-alpine as builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

## 📋 开发规范

### 1. 代码规范
```json
// .eslintrc.js
{
  "extends": [
    "@typescript-eslint/recommended",
    "plugin:react/recommended",
    "plugin:react-hooks/recommended"
  ],
  "rules": {
    "@typescript-eslint/no-unused-vars": "error",
    "react/prop-types": "off",
    "react/react-in-jsx-scope": "off"
  }
}
```

### 2. 提交规范
```bash
# 使用约定式提交
feat: 添加Cookie管理功能
fix: 修复WebSocket重连问题
docs: 更新API文档
style: 格式化代码
refactor: 重构数据预览组件
test: 添加单元测试
chore: 更新依赖包
```

### 3. 分支策略
- `main`: 主分支，生产环境代码
- `develop`: 开发分支，集成最新功能
- `feature/*`: 功能分支，开发新功能
- `fix/*`: 修复分支，修复Bug
- `release/*`: 发布分支，准备发布

## 📊 监控与维护

### 1. 性能监控
```typescript
// 性能监控
function usePerformanceMonitor() {
  useEffect(() => {
    // 监控页面加载时间
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach((entry) => {
        if (entry.entryType === 'navigation') {
          console.log('页面加载时间:', entry.loadEventEnd - entry.loadEventStart);
        }
      });
    });
    
    observer.observe({ entryTypes: ['navigation'] });
    
    return () => observer.disconnect();
  }, []);
}
```

### 2. 错误监控
```typescript
// 全局错误处理
window.addEventListener('error', (event) => {
  console.error('JavaScript错误:', event.error);
  // 发送错误报告到监控服务
});

window.addEventListener('unhandledrejection', (event) => {
  console.error('未处理的Promise拒绝:', event.reason);
  // 发送错误报告到监控服务
});
```

### 3. 用户行为分析
```typescript
// 用户行为追踪
function useAnalytics() {
  const trackEvent = useCallback((event: string, properties: any) => {
    // 发送分析数据
    console.log('用户事件:', event, properties);
  }, []);
  
  return { trackEvent };
}
```

## 🔄 迭代计划

### Phase 1: 核心功能 (Week 1-2)
- [x] 项目搭建和基础架构
- [x] API客户端实现
- [x] Cookie管理基础功能
- [x] 多关键词智能解析
- [x] WebSocket连接管理

### Phase 2: 数据展示 (Week 3-4)
- [ ] 实时数据预览
- [ ] 进度监控界面
- [ ] 商品数据表格
- [ ] 数据导出功能

### Phase 3: 用户体验 (Week 5-6)
- [ ] 响应式设计优化
- [ ] 交互动画效果
- [ ] 错误处理改进
- [ ] 性能优化

### Phase 4: 测试部署 (Week 7-8)
- [ ] 单元测试完善
- [ ] 集成测试
- [ ] E2E测试
- [ ] 生产部署

## 📝 总结

本架构文档提供了拼多多爬虫前端系统的完整技术实现方案，包括：

### 🎯 关键特性
1. **现代化技术栈**: React 18 + TypeScript + Vite + Ant Design
2. **专业WebSocket集成**: react-use-websocket，支持自动重连
3. **类型安全**: 完整的TypeScript类型定义
4. **组件化架构**: 模块化、可复用的组件设计
5. **状态管理**: Zustand轻量级状态管理
6. **性能优化**: 代码分割、虚拟滚动、缓存策略

### 🛡️ 质量保证
1. **功能对等**: 与后端API完全对等的功能实现
2. **数据一致性**: 确保导出数据与后端完全一致
3. **错误处理**: 完善的错误边界和用户友好提示
4. **测试覆盖**: 单元测试、集成测试、E2E测试
5. **安全防护**: XSS、CSRF防护，输入验证

### 🚀 开发效率
1. **开发规范**: ESLint、Prettier、约定式提交
2. **代码复用**: 通用组件库和工具函数
3. **调试支持**: 开发工具集成，错误追踪
4. **文档完善**: 组件文档、API文档、架构文档

本架构设计确保前端系统能够：
- ✅ 100%支持后端所有功能
- ✅ 提供现代化的用户体验
- ✅ 保证数据的完整性和一致性
- ✅ 具备良好的可维护性和扩展性
- ✅ 满足生产环境的稳定性要求

按照此架构实施，将能够构建出一个功能完整、性能优秀、用户体验出色的拼多多爬虫前端系统。