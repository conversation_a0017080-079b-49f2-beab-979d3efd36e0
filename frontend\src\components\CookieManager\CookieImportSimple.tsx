import React, { useState, useCallback } from 'react';
import {
  Card,
  Input,
  Button,
  Alert,
  Space,
  Typography,
  Badge,
  Tag,
  Row,
  Col,
  message
} from 'antd';
import {
  FileTextOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ImportOutlined
} from '@ant-design/icons';
import type { CookieImportProps, FormatDetectionResult } from './types';
import { detectAndParseCookies, validatePDDCookies } from './utils';

const { TextArea } = Input;
const { Text } = Typography;

const CookieImportSimple: React.FC<CookieImportProps> = ({ onImport, loading = false }) => {
  const [textInput, setTextInput] = useState<string>('');
  const [previewData, setPreviewData] = useState<FormatDetectionResult | null>(null);
  const [validationResult, setValidationResult] = useState<{
    valid: boolean;
    missing: string[];
    expired: string[];
  } | null>(null);

  // 文本输入处理
  const handleTextInput = useCallback((value: string) => {
    setTextInput(value);
    
    if (value.trim()) {
      const result = detectAndParseCookies(value.trim());
      setPreviewData(result);
      
      if (result.data.length > 0) {
        const validation = validatePDDCookies(result.data);
        setValidationResult(validation);
      } else {
        setValidationResult(null);
      }
    } else {
      setPreviewData(null);
      setValidationResult(null);
    }
  }, []);

  // 执行导入
  const handleImport = useCallback(async () => {
    if (!previewData || previewData.data.length === 0) {
      message.error('请先粘贴有效的Cookie数据');
      return;
    }

    try {
      await onImport({
        cookies: previewData.data
      });
      
      message.success('Cookie导入成功');
      // 清空数据
      setTextInput('');
      setPreviewData(null);
      setValidationResult(null);
    } catch (error) {
      console.error('导入失败:', error);
      message.error('Cookie导入失败，请检查格式');
    }
  }, [previewData, onImport]);

  // 渲染格式检测结果
  const renderFormatDetection = () => {
    if (!previewData) return null;

    const formatNames = {
      'json': 'JSON 格式',
      'netscape': 'Netscape 格式',
      'browser-export': '浏览器导出格式',
      'cookie-string': 'Cookie 字符串'
    };

    const confidenceColor = previewData.confidence > 0.8 ? 'success' : 
                           previewData.confidence > 0.5 ? 'warning' : 'error';

    return (
      <Alert
        message="格式检测结果"
        description={
          <Space direction="vertical" size="small" style={{ width: '100%' }}>
            <Row gutter={16}>
              <Col span={12}>
                <Text strong>检测到的格式：</Text>
                <Tag color="blue" style={{ marginLeft: 8 }}>
                  {formatNames[previewData.format] || '未知格式'}
                </Tag>
              </Col>
              <Col span={12}>
                <Text strong>置信度：</Text>
                <Badge 
                  status={confidenceColor} 
                  text={`${(previewData.confidence * 100).toFixed(0)}%`}
                  style={{ marginLeft: 8 }}
                />
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Text strong>Cookie数量：</Text>
                <Text style={{ marginLeft: 8 }}>{previewData.data.length} 个</Text>
              </Col>
              <Col span={12}>
                <Text strong>域名：</Text>
                <Text style={{ marginLeft: 8 }}>
                  {[...new Set(previewData.data.map(c => c.domain))].join(', ')}
                </Text>
              </Col>
            </Row>
          </Space>
        }
        type={previewData.confidence > 0.8 ? 'success' : 'warning'}
        showIcon
        style={{ marginBottom: 16 }}
      />
    );
  };

  // 渲染验证结果
  const renderValidationResult = () => {
    if (!validationResult) return null;

    if (validationResult.valid) {
      return (
        <Alert
          message="验证通过"
          description="Cookie数据有效，可以导入使用"
          type="success"
          showIcon
          icon={<CheckCircleOutlined />}
          style={{ marginBottom: 16 }}
        />
      );
    }

    return (
      <Alert
        message="验证警告"
        description={
          <Space direction="vertical" size="small">
            {validationResult.missing.length > 0 && (
              <div>
                <Text strong>缺少必要的Cookie：</Text>
                <Text type="danger" style={{ marginLeft: 8 }}>
                  {validationResult.missing.join(', ')}
                </Text>
              </div>
            )}
            {validationResult.expired.length > 0 && (
              <div>
                <Text strong>已过期的Cookie：</Text>
                <Text type="warning" style={{ marginLeft: 8 }}>
                  {validationResult.expired.join(', ')}
                </Text>
              </div>
            )}
          </Space>
        }
        type="warning"
        showIcon
        icon={<ExclamationCircleOutlined />}
        style={{ marginBottom: 16 }}
      />
    );
  };

  return (
    <Card
      title={
        <Space>
          <FileTextOutlined />
          <span>Cookie导入</span>
        </Space>
      }
      size="small"
    >
      <Space direction="vertical" style={{ width: '100%' }} size="middle">
        {/* 使用说明 */}
        <Alert
          message="使用说明"
          description="请将Cookie数据粘贴到下方文本框中，支持JSON、Netscape、浏览器导出等多种格式，系统会自动识别格式并解析。"
          type="info"
          showIcon
          closable
        />

        {/* 文本输入区域 */}
        <div>
          <Text strong style={{ display: 'block', marginBottom: 8 }}>
            粘贴Cookie数据：
          </Text>
          <TextArea
            value={textInput}
            onChange={(e) => handleTextInput(e.target.value)}
            placeholder={`请粘贴Cookie数据，支持以下格式：
1. JSON格式：[{"name":"PDDAccessToken","value":"xxx",...}]
2. Netscape格式：.pinduoduo.com	TRUE	/	FALSE	...
3. Cookie字符串：PDDAccessToken=xxx; api_uid=xxx
4. 浏览器导出格式：从浏览器插件导出的Cookie数据`}
            autoSize={{ minRows: 8, maxRows: 15 }}
            disabled={loading}
            style={{ fontFamily: 'monospace' }}
          />
        </div>

        {/* 格式检测结果 */}
        {renderFormatDetection()}

        {/* 验证结果 */}
        {renderValidationResult()}

        {/* 操作按钮 */}
        <Space>
          <Button
            type="primary"
            icon={<ImportOutlined />}
            onClick={handleImport}
            loading={loading}
            disabled={!previewData || previewData.data.length === 0}
          >
            导入Cookie
          </Button>
          <Button
            onClick={() => {
              setTextInput('');
              setPreviewData(null);
              setValidationResult(null);
            }}
            disabled={loading || !textInput}
          >
            清空
          </Button>
        </Space>
      </Space>
    </Card>
  );
};

export default CookieImportSimple;