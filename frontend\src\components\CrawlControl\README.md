# 爬取控制面板 (CrawlControl)

爬取控制面板是整个拼多多爬虫系统的核心控制中心，提供了完整的任务管理和控制功能。

## 功能特性

### ✨ 核心功能

1. **步骤引导** - 通过可视化步骤指导用户完成完整的爬取流程
2. **任务管理** - 支持多任务并行管理和状态跟踪
3. **实时监控** - WebSocket 实时监控爬取进度和数据收集
4. **历史记录** - 完整的任务历史记录和统计分析
5. **快速启动** - 预设配置和自定义配置快速启动功能

### 🎯 主要组件

#### 1. 主控制面板 (`index.tsx`)
- **步骤引导**: Cookie验证 → 搜索配置 → 任务启动 → 实时监控
- **标签页布局**: 控制面板、实时监控、数据预览、任务管理、历史记录
- **状态管理**: 统一的任务状态和配置管理
- **错误处理**: 友好的错误提示和恢复机制

#### 2. 任务控制 (`TaskControl.tsx`)
- **智能按钮**: 根据任务状态动态显示操作按钮
- **操作确认**: 危险操作需要用户确认
- **状态显示**: 实时显示任务运行状态
- **批量操作**: 支持多任务批量操作

#### 3. 任务列表 (`TaskList.tsx`)
- **任务展示**: 清晰展示所有活动任务信息
- **进度追踪**: 实时更新任务进度和统计信息
- **快速操作**: 一键暂停、恢复、停止任务
- **批量管理**: 支持多选和批量操作

#### 4. 任务历史 (`TaskHistory.tsx`)
- **历史记录**: 完整的任务执行历史
- **数据统计**: 详细的任务执行统计和分析
- **筛选搜索**: 支持多维度筛选和搜索
- **数据导出**: 支持历史数据导出

#### 5. 快速启动 (`QuickStart.tsx`)
- **预设配置**: 常用爬取场景的预设配置
- **自定义配置**: 保存和管理个人配置
- **一键启动**: 快速启动常用配置
- **使用统计**: 显示配置使用频次和最近使用时间

## 操作流程

### 🚀 完整流程

```mermaid
graph TD
    A[Cookie验证] --> B[搜索配置]
    B --> C[启动任务]
    C --> D[实时监控]
    D --> E[数据收集]
    E --> F[任务完成]
    
    B -.-> G[快速启动]
    G --> C
    
    D --> H[任务管理]
    F --> I[历史记录]
```

### 📋 详细步骤

#### 步骤1: Cookie验证
- 自动检测Cookie状态
- 验证Cookie有效性
- 提供Cookie管理界面
- 支持Cookie导入和更新

#### 步骤2: 搜索配置
- 关键词设置
- 分类筛选
- 价格范围设定
- 排序方式选择
- 爬取参数配置

#### 步骤3: 任务启动
- 配置验证
- 任务创建
- WebSocket连接建立
- 爬虫进程启动

#### 步骤4: 实时监控
- 进度实时更新
- 数据收集监控
- 错误状态提醒
- 性能指标显示

## 状态管理

### 任务状态流转

```
pending → running → completed
    ↓         ↓         ↑
cancelled ← paused → failed
```

- **pending**: 等待执行
- **running**: 正在运行
- **paused**: 已暂停（预留功能）
- **completed**: 执行完成
- **failed**: 执行失败
- **cancelled**: 已取消

### 数据持久化

#### 本地存储
- 快速配置 (`pdd-quick-configs`)
- 任务历史 (`pdd-task-history`)
- 用户偏好设置

#### 状态同步
- Zustand 全局状态管理
- WebSocket 实时数据同步
- LocalStorage 持久化存储

## API 集成

### 后端接口

| 接口 | 方法 | 描述 |
|------|------|------|
| `/api/cookies/validate` | GET | Cookie验证 |
| `/api/crawl/start` | POST | 启动爬取任务 |
| `/api/crawl/stop` | POST | 停止爬取任务 |
| `/api/crawl/status` | GET | 获取任务状态 |

### WebSocket 事件

| 事件类型 | 描述 |
|----------|------|
| `connected` | 连接建立 |
| `status` | 状态更新 |
| `progress` | 进度更新 |
| `data` | 数据接收 |
| `error` | 错误信息 |
| `completed` | 任务完成 |

## 配置选项

### 搜索配置
```typescript
interface SearchConfig {
  keyword: string;           // 搜索关键词
  category?: string;         // 商品分类
  minPrice?: number;         // 最低价格
  maxPrice?: number;         // 最高价格
  sortType?: string;         // 排序方式
  pageSize?: number;         // 每页数量
  maxPages?: number;         // 最大页数
}
```

### 快速配置
```typescript
interface QuickStartConfig {
  id: string;                // 配置ID
  name: string;              // 配置名称
  config: SearchConfig;      // 搜索配置
  lastUsed?: string;         // 最后使用时间
  useCount: number;          // 使用次数
}
```

## 使用示例

### 基本使用

```tsx
import CrawlControlPanel from '@/components/CrawlControl';

const App = () => {
  return (
    <div>
      <CrawlControlPanel 
        defaultStep="cookie"
        className="crawl-control"
      />
    </div>
  );
};
```

### 自定义配置

```tsx
<CrawlControlPanel 
  defaultStep="config"
  onTaskComplete={(task) => {
    console.log('任务完成:', task);
  }}
  onError={(error) => {
    console.error('错误:', error);
  }}
/>
```

## 样式定制

### 主题变量
```css
:root {
  --crawl-primary-color: #1890ff;
  --crawl-success-color: #52c41a;
  --crawl-warning-color: #faad14;
  --crawl-error-color: #ff4d4f;
}
```

### 自定义样式
```css
.crawl-control {
  .ant-steps-item-process .ant-steps-item-icon {
    background-color: var(--crawl-primary-color);
  }
}
```

## 性能优化

### 1. 虚拟化
- 大列表使用虚拟滚动
- 按需加载历史记录
- 分页展示任务列表

### 2. 缓存策略
- 配置信息缓存
- API响应缓存
- 组件状态缓存

### 3. 错误边界
- 组件级错误捕获
- 优雅降级处理
- 错误状态恢复

## 故障排除

### 常见问题

#### 1. Cookie验证失败
- 检查Cookie是否过期
- 确认Cookie格式正确
- 重新获取Cookie

#### 2. WebSocket连接失败
- 检查网络连接
- 确认服务器状态
- 重试连接机制

#### 3. 任务启动失败
- 验证配置参数
- 检查后端服务
- 查看错误日志

### 调试模式

开启调试模式查看详细信息：
```javascript
localStorage.setItem('DEBUG', 'crawl-control:*');
```

## 开发指南

### 添加新功能

1. **新增组件**
   ```bash
   src/components/CrawlControl/
   ├── NewFeature.tsx      # 新功能组件
   ├── types.ts           # 更新类型定义
   └── utils.ts           # 添加工具函数
   ```

2. **扩展状态管理**
   ```typescript
   // 在 appStore.ts 中添加新状态
   interface AppState {
     // ... 现有状态
     newFeatureState: NewFeatureState;
   }
   ```

3. **API集成**
   ```typescript
   // 在 api.ts 中添加新接口
   export const newFeatureApi = {
     getData: () => api.get('/api/new-feature'),
     updateData: (data) => api.post('/api/new-feature', data)
   };
   ```

### 代码规范

- 使用 TypeScript 严格模式
- 遵循 Ant Design 设计规范
- 组件采用函数式编程
- 使用 Hooks 管理状态
- 错误边界保护组件

## 更新日志

### v1.0.0 (2024-08-01)
- ✨ 初始版本发布
- 🎯 完整的任务管理功能
- 📊 实时监控和数据预览
- 🚀 快速启动和历史管理
- 🔧 Cookie管理和配置系统