<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务状态转换测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .state-diagram {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            margin: 16px 0;
            font-family: monospace;
            white-space: pre-line;
        }
        .test-result {
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            margin-bottom: 16px;
            overflow: hidden;
        }
        .test-header {
            background: #fafafa;
            padding: 12px 16px;
            border-bottom: 1px solid #d9d9d9;
            font-weight: bold;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .test-content {
            padding: 16px;
        }
        .success {
            background: #f6ffed;
            color: #52c41a;
            border-color: #b7eb8f;
        }
        .failure {
            background: #fff2f0;
            color: #ff4d4f;
            border-color: #ffccc7;
        }
        .state-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin: 16px 0;
        }
        .state-card {
            border: 2px solid #d9d9d9;
            border-radius: 6px;
            padding: 12px;
            text-align: center;
            transition: all 0.3s;
            cursor: pointer;
        }
        .state-card.pending { border-color: #faad14; background: #fffbe6; }
        .state-card.running { border-color: #1890ff; background: #e6f7ff; }
        .state-card.paused { border-color: #fa8c16; background: #fff7e6; }
        .state-card.completed { border-color: #52c41a; background: #f6ffed; }
        .state-card.failed { border-color: #ff4d4f; background: #fff2f0; }
        .state-card.cancelled { border-color: #8c8c8c; background: #f5f5f5; }
        .state-card.active {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        .actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 8px;
            margin: 16px 0;
        }
        .action-btn {
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            background: white;
            cursor: pointer;
            transition: all 0.2s;
        }
        .action-btn:hover:not(:disabled) {
            background: #f0f0f0;
            border-color: #1890ff;
        }
        .action-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            background: #f5f5f5;
        }
        .action-btn.primary { background: #1890ff; color: white; border-color: #1890ff; }
        .action-btn.danger { background: #ff4d4f; color: white; border-color: #ff4d4f; }
        .transition-log {
            background: #f5f5f5;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            padding: 12px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .stats {
            background: #e6f7ff;
            padding: 16px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #40a9ff;
        }
    </style>
</head>
<body>
    <h1>任务控制状态转换测试</h1>
    
    <div class="stats" id="testStats">
        <h3>测试统计</h3>
        <div>✅ 合法转换: <span id="validTransitions">0</span></div>
        <div>❌ 非法转换: <span id="invalidTransitions">0</span></div>
        <div>🔄 总测试次数: <span id="totalTests">0</span></div>
    </div>

    <div class="test-container">
        <h2>任务状态图</h2>
        <div class="state-diagram">任务状态转换图:

pending (等待中)
  ├─ start() → running (运行中)
  └─ cancel() → cancelled (已取消)

running (运行中)
  ├─ pause() → paused (已暂停)
  ├─ stop() → completed (已完成)
  ├─ error → failed (失败)
  └─ complete → completed (已完成)

paused (已暂停)
  ├─ resume() → running (运行中)
  └─ stop() → completed (已完成)

completed (已完成)
  └─ retry() → pending (等待中)

failed (失败)
  └─ retry() → pending (等待中)

cancelled (已取消)
  └─ retry() → pending (等待中)</div>
    </div>

    <div class="test-container">
        <h2>交互式状态测试</h2>
        <p>当前状态: <strong id="currentState">pending</strong></p>
        
        <div class="state-grid">
            <div class="state-card pending active" data-state="pending">
                <h4>pending</h4>
                <div>等待中</div>
            </div>
            <div class="state-card running" data-state="running">
                <h4>running</h4>
                <div>运行中</div>
            </div>
            <div class="state-card paused" data-state="paused">
                <h4>paused</h4>
                <div>已暂停</div>
            </div>
            <div class="state-card completed" data-state="completed">
                <h4>completed</h4>
                <div>已完成</div>
            </div>
            <div class="state-card failed" data-state="failed">
                <h4>failed</h4>
                <div>失败</div>
            </div>
            <div class="state-card cancelled" data-state="cancelled">
                <h4>cancelled</h4>
                <div>已取消</div>
            </div>
        </div>

        <h3>可用操作</h3>
        <div class="actions-grid">
            <button class="action-btn primary" id="startBtn" onclick="performAction('start')">开始</button>
            <button class="action-btn" id="pauseBtn" onclick="performAction('pause')" disabled>暂停</button>
            <button class="action-btn" id="resumeBtn" onclick="performAction('resume')" disabled>继续</button>
            <button class="action-btn danger" id="stopBtn" onclick="performAction('stop')" disabled>停止</button>
            <button class="action-btn danger" id="cancelBtn" onclick="performAction('cancel')" disabled>取消</button>
            <button class="action-btn" id="retryBtn" onclick="performAction('retry')" disabled>重试</button>
        </div>

        <h3>转换日志</h3>
        <div class="transition-log" id="transitionLog">
初始状态: pending
等待用户操作...
        </div>
    </div>

    <div class="test-container">
        <h2>自动化状态转换测试</h2>
        <button onclick="runAllTests()">运行所有测试</button>
        <button onclick="runValidTransitionsTest()">测试合法转换</button>
        <button onclick="runInvalidTransitionsTest()">测试非法转换</button>
        <button onclick="clearResults()">清除结果</button>
        <div id="autoTestResults"></div>
    </div>

    <script>
        // 状态转换规则 - 复制自前端代码
        const STATE_TRANSITIONS = {
            pending: ['running', 'cancelled'],
            running: ['paused', 'completed', 'failed'],
            paused: ['running', 'completed'],
            completed: ['pending'], // 通过retry
            failed: ['pending'],    // 通过retry
            cancelled: ['pending']  // 通过retry
        };

        // 操作到状态的映射
        const ACTION_STATE_MAP = {
            start: 'running',
            pause: 'paused', 
            resume: 'running',
            stop: 'completed',
            cancel: 'cancelled',
            retry: 'pending',
            error: 'failed',
            complete: 'completed'
        };

        // 状态显示名称
        const STATE_NAMES = {
            pending: '等待中',
            running: '运行中', 
            paused: '已暂停',
            completed: '已完成',
            failed: '失败',
            cancelled: '已取消'
        };

        // 当前状态
        let currentState = 'pending';
        let transitionCount = 0;
        let validTransitionCount = 0;
        let invalidTransitionCount = 0;

        // 从utils.ts复制的函数
        function canPerformAction(taskStatus, action) {
            switch (action) {
                case 'pause':
                    return taskStatus === 'running';
                case 'resume':
                    return taskStatus === 'paused';
                case 'stop':
                    return taskStatus === 'running' || taskStatus === 'paused';
                case 'cancel':
                    return taskStatus === 'pending' || taskStatus === 'running' || taskStatus === 'paused';
                case 'retry':
                    return taskStatus === 'failed' || taskStatus === 'cancelled';
                case 'start':
                    return taskStatus === 'pending';
                default:
                    return false;
            }
        }

        // 更新UI状态
        function updateUI() {
            // 更新当前状态显示
            document.getElementById('currentState').textContent = currentState;
            
            // 更新状态卡片
            document.querySelectorAll('.state-card').forEach(card => {
                const state = card.dataset.state;
                if (state === currentState) {
                    card.classList.add('active');
                } else {
                    card.classList.remove('active');
                }
            });

            // 更新按钮状态
            const actions = ['start', 'pause', 'resume', 'stop', 'cancel', 'retry'];
            actions.forEach(action => {
                const btn = document.getElementById(action + 'Btn');
                const canPerform = canPerformAction(currentState, action);
                btn.disabled = !canPerform;
                
                if (canPerform) {
                    btn.style.opacity = '1';
                } else {
                    btn.style.opacity = '0.5';
                }
            });

            // 更新统计
            document.getElementById('validTransitions').textContent = validTransitionCount;
            document.getElementById('invalidTransitions').textContent = invalidTransitionCount;
            document.getElementById('totalTests').textContent = transitionCount;
        }

        // 执行操作
        function performAction(action) {
            const canPerform = canPerformAction(currentState, action);
            const targetState = ACTION_STATE_MAP[action];
            const timestamp = new Date().toLocaleTimeString();
            
            transitionCount++;
            
            if (canPerform && targetState) {
                // 合法转换
                const oldState = currentState;
                currentState = targetState;
                validTransitionCount++;
                
                logTransition(`[${timestamp}] ✅ ${oldState} --${action}--> ${currentState} (合法)`);
                updateUI();
            } else {
                // 非法转换
                invalidTransitionCount++;
                logTransition(`[${timestamp}] ❌ ${currentState} --${action}--> ? (非法操作)`);
            }
        }

        // 记录转换日志
        function logTransition(message) {
            const log = document.getElementById('transitionLog');
            log.textContent += '\n' + message;
            log.scrollTop = log.scrollHeight;
        }

        // 运行所有测试
        function runAllTests() {
            runValidTransitionsTest();
            runInvalidTransitionsTest();
        }

        // 测试所有合法转换
        function runValidTransitionsTest() {
            const results = document.getElementById('autoTestResults');
            let testResults = [];

            // 测试所有状态的所有合法转换
            Object.keys(STATE_TRANSITIONS).forEach(fromState => {
                const possibleStates = STATE_TRANSITIONS[fromState];
                
                possibleStates.forEach(toState => {
                    // 找到能从fromState转换到toState的操作
                    const action = Object.keys(ACTION_STATE_MAP).find(act => 
                        ACTION_STATE_MAP[act] === toState && canPerformAction(fromState, act)
                    );
                    
                    if (action) {
                        const testPassed = canPerformAction(fromState, action);
                        testResults.push({
                            from: fromState,
                            to: toState,
                            action: action,
                            passed: testPassed,
                            type: 'valid'
                        });
                    }
                });
            });

            displayTestResults('合法转换测试', testResults, results);
        }

        // 测试所有非法转换
        function runInvalidTransitionsTest() {
            const results = document.getElementById('autoTestResults');
            let testResults = [];

            // 测试每个状态的非法操作
            const allStates = Object.keys(STATE_TRANSITIONS);
            const allActions = Object.keys(ACTION_STATE_MAP);

            allStates.forEach(state => {
                allActions.forEach(action => {
                    const shouldBeAllowed = canPerformAction(state, action);
                    const targetState = ACTION_STATE_MAP[action];
                    const isValidTransition = STATE_TRANSITIONS[state]?.includes(targetState);
                    
                    // 如果操作不被允许，或者转换不合法，这应该是一个非法转换
                    if (!shouldBeAllowed || !isValidTransition) {
                        testResults.push({
                            from: state,
                            to: targetState,
                            action: action,
                            passed: !shouldBeAllowed, // 非法转换测试：应该被阻止
                            type: 'invalid'
                        });
                    }
                });
            });

            displayTestResults('非法转换测试', testResults, results);
        }

        // 显示测试结果
        function displayTestResults(testName, results, container) {
            const passedCount = results.filter(r => r.passed).length;
            const totalCount = results.length;
            const isSuccess = passedCount === totalCount;

            const html = `
                <div class="test-result ${isSuccess ? 'success' : 'failure'}">
                    <div class="test-header">
                        ${isSuccess ? '✅' : '❌'} ${testName}
                        <span>${passedCount}/${totalCount} 通过</span>
                    </div>
                    <div class="test-content">
                        ${results.map(r => `
                            <div style="margin: 4px 0; padding: 4px; border-radius: 3px; background: ${r.passed ? '#f6ffed' : '#fff2f0'};">
                                ${r.passed ? '✅' : '❌'} 
                                ${STATE_NAMES[r.from]} 
                                --${r.action}--> 
                                ${r.to ? STATE_NAMES[r.to] : '?'}
                                ${r.type === 'invalid' ? ' (应被阻止)' : ''}
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;

            container.innerHTML += html;
        }

        // 清除结果
        function clearResults() {
            document.getElementById('autoTestResults').innerHTML = '';
            document.getElementById('transitionLog').textContent = '初始状态: pending\n等待用户操作...';
            
            // 重置状态
            currentState = 'pending';
            transitionCount = 0;
            validTransitionCount = 0;
            invalidTransitionCount = 0;
            updateUI();
        }

        // 状态卡片点击事件
        document.querySelectorAll('.state-card').forEach(card => {
            card.addEventListener('click', () => {
                const targetState = card.dataset.state;
                if (targetState !== currentState) {
                    // 直接设置状态（用于测试）
                    const oldState = currentState;
                    currentState = targetState;
                    const timestamp = new Date().toLocaleTimeString();
                    logTransition(`[${timestamp}] 🔧 ${oldState} --> ${currentState} (手动设置)`);
                    updateUI();
                }
            });
        });

        // 初始化
        window.onload = function() {
            updateUI();
            logTransition('任务状态转换测试已加载');
            logTransition('点击状态卡片可手动切换状态');
            logTransition('点击操作按钮测试状态转换逻辑');
        };
    </script>
</body>
</html>