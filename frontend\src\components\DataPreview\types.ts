/**
 * 数据预览组件类型定义
 */

import type { Product, ColumnConfig, DataFilter, DataPreviewStats, ExportRequest, ExportProgress } from '../../types';

// Re-export types from main types index
export type { Product, ColumnConfig, DataFilter, DataPreviewStats, ExportRequest, ExportProgress } from '../../types';

// 数据表格组件Props
export interface DataTableProps {
  data: Product[];
  loading?: boolean;
  pagination?: {
    current: number;
    pageSize: number;
    total: number;
    onChange: (page: number, pageSize: number) => void;
  };
  onRowClick?: (record: Product) => void;
  filters?: DataFilter;
  onFiltersChange?: (filters: DataFilter) => void;
  columns?: ColumnConfig[];
  onColumnsChange?: (columns: ColumnConfig[]) => void;
}

// 数据统计组件Props
export interface DataStatsProps {
  data: Product[];
  stats?: DataPreviewStats;
  loading?: boolean;
}

// 导出管理组件Props
export interface ExportManagerProps {
  data: Product[];
  onExport?: (request: ExportRequest) => Promise<void>;
  exportProgress?: ExportProgress;
  disabled?: boolean;
}

// 商品详情组件Props
export interface ProductDetailProps {
  product: Product | null;
  visible: boolean;
  onClose: () => void;
}

// 工具函数类型
export interface PriceStats {
  min: number;
  max: number;
  avg: number;
  median: number;
}

export interface SalesStats {
  total: number;
  avg: number;
  median: number;
  topProducts: Product[];
}

