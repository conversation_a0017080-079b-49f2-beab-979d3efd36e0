# API 接口文档

## 接口概览

前端系统通过RESTful API和WebSocket与后端服务进行通信。本文档详细描述了所有可用的API接口。

### 基础信息
- **基础URL**: `http://localhost:8001/api`
- **WebSocket URL**: `ws://localhost:8001/ws`
- **内容类型**: `application/json`
- **字符编码**: `UTF-8`

## 通用响应格式

所有API响应都遵循统一的格式：

```json
{
  "success": true,
  "data": {}, 
  "message": "操作成功",
  "timestamp": "2025-08-01T10:30:45Z"
}
```

### 响应字段说明
- `success`: 布尔值，表示请求是否成功
- `data`: 响应数据，具体结构根据接口而定
- `message`: 响应消息，成功或错误信息
- `timestamp`: 响应时间戳

### 错误响应格式
```json
{
  "success": false,
  "error": {
    "code": "INVALID_PARAMETER",
    "message": "参数验证失败",
    "details": "keywords字段不能为空"
  },
  "timestamp": "2025-08-01T10:30:45Z"
}
```

## 健康检查接口

### GET /health
检查API服务器健康状态

**请求参数**: 无

**响应示例**:
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "version": "2.2.0",
    "uptime": "2h 45m 30s",
    "timestamp": "2025-08-01T10:30:45Z"
  },
  "message": "服务运行正常"
}
```

## 爬虫控制接口

### POST /crawl/start
启动爬虫任务

**请求体**:
```json
{
  "keywords": ["手机", "电脑"],
  "sort": "sales_desc",
  "price_range": [100, 5000],
  "max_pages": 10,
  "filters": {
    "brands": ["华为", "小米"],
    "min_sales": 1000,
    "shop_type": "flagship"
  },
  "settings": {
    "interval": 2,
    "concurrent": 3,
    "retry_count": 3
  }
}
```

**参数说明**:
- `keywords`: 搜索关键词数组（必填）
- `sort`: 排序方式，可选值：`comprehensive`, `sales_desc`, `price_asc`, `price_desc`, `comment_desc`
- `price_range`: 价格区间 [最低价, 最高价]
- `max_pages`: 最大采集页数
- `filters`: 高级筛选条件
- `settings`: 采集设置

**响应示例**:
```json
{
  "success": true,
  "data": {
    "task_id": "task_20250801_103045",
    "estimated_duration": "15-20分钟",
    "estimated_products": "500-800个"
  },
  "message": "爬虫任务已启动"
}
```

### POST /crawl/stop
停止爬虫任务

**请求体**:
```json
{
  "task_id": "task_20250801_103045",
  "force": false
}
```

**参数说明**:
- `task_id`: 任务ID（可选，不传则停止当前任务）
- `force`: 是否强制停止

**响应示例**:
```json
{
  "success": true,
  "data": {
    "task_id": "task_20250801_103045",
    "status": "stopping",
    "products_collected": 342
  },
  "message": "爬虫任务正在停止"
}
```

### GET /crawl/status
获取爬虫状态

**请求参数**:
- `task_id`: 任务ID（可选）

**响应示例**:
```json
{
  "success": true,
  "data": {
    "task_id": "task_20250801_103045",
    "status": "running",
    "progress": {
      "current_keyword": "手机",
      "keyword_progress": "2/2",
      "current_page": 5,
      "total_pages": 10,
      "products_found": 245,
      "completion_percentage": 65
    },
    "statistics": {
      "start_time": "2025-08-01T10:30:45Z",
      "elapsed_time": "8m 32s",
      "estimated_remaining": "5m 12s",
      "average_speed": "0.5页/分钟",
      "success_rate": 95.2
    },
    "current_activity": "正在采集第5页数据"
  },
  "message": "状态获取成功"
}
```

### GET /crawl/tasks
获取任务列表

**请求参数**:
- `page`: 页码（默认1）
- `page_size`: 每页数量（默认20）
- `status`: 状态筛选（可选）

**响应示例**:
```json
{
  "success": true,
  "data": {
    "tasks": [
      {
        "task_id": "task_20250801_103045",
        "keywords": ["手机", "电脑"],
        "status": "completed",
        "created_at": "2025-08-01T10:30:45Z",
        "completed_at": "2025-08-01T10:45:32Z",
        "products_count": 456,
        "duration": "14m 47s"
      }
    ],
    "total": 1,
    "page": 1,
    "page_size": 20
  },
  "message": "任务列表获取成功"
}
```

## Cookie管理接口

### GET /cookie/status
获取Cookie状态

**响应示例**:
```json
{
  "success": true,
  "data": {
    "total_cookies": 12,
    "valid_cookies": 8,
    "expired_cookies": 3,
    "invalid_cookies": 1,
    "last_updated": "2025-08-01T09:15:30Z",
    "domains": [".yangkeduo.com", ".pdd.com"]
  },
  "message": "Cookie状态获取成功"
}
```

### POST /cookie/validate
验证Cookie

**请求体**:
```json
{
  "cookies": [
    {
      "name": "api_uid",
      "value": "CjNlBT...",
      "domain": ".yangkeduo.com",
      "path": "/",
      "expires": "2025-12-31T23:59:59Z"
    }
  ]
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "valid": true,
    "details": [
      {
        "name": "api_uid",
        "valid": true,
        "message": "Cookie有效",
        "expires_in": "152天"
      }
    ]
  },
  "message": "Cookie验证完成"
}
```

### POST /cookie/save
保存Cookie

**请求体**:
```json
{
  "cookies": [
    {
      "name": "api_uid",
      "value": "CjNlBT...",
      "domain": ".yangkeduo.com",
      "path": "/",
      "expires": "2025-12-31T23:59:59Z"
    }
  ],
  "overwrite": true
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "saved_count": 1,
    "skipped_count": 0,
    "total_cookies": 12
  },
  "message": "Cookie保存成功"
}
```

### POST /cookie/import
导入Cookie

**请求体**:
```json
{
  "format": "json",
  "data": "[{\"name\":\"api_uid\",\"value\":\"CjNlBT...\"}]"
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "imported_count": 8,
    "valid_count": 7,
    "invalid_count": 1,
    "details": [
      {
        "name": "api_uid",
        "status": "imported",
        "message": "导入成功"
      }
    ]
  },
  "message": "Cookie导入完成"
}
```

### DELETE /cookie/clear
清除Cookie

**请求体**:
```json
{
  "domains": [".yangkeduo.com"],
  "confirm": true
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "cleared_count": 12,
    "domains": [".yangkeduo.com", ".pdd.com"]
  },
  "message": "Cookie清除成功"
}
```

### GET /cookie/export
导出Cookie

**请求参数**:
- `format`: 导出格式（json/text）
- `domain`: 域名筛选（可选）

**响应示例**:
```json
{
  "success": true,
  "data": {
    "cookies": [
      {
        "name": "api_uid",
        "value": "CjNlBT...",
        "domain": ".yangkeduo.com",
        "path": "/",
        "expires": "2025-12-31T23:59:59Z"
      }
    ],
    "export_time": "2025-08-01T10:30:45Z",
    "total_count": 12
  },
  "message": "Cookie导出成功"
}
```

## 数据管理接口

### GET /products
获取产品数据

**请求参数**:
- `task_id`: 任务ID（可选）
- `page`: 页码（默认1）
- `page_size`: 每页数量（默认20，最大100）
- `keyword`: 搜索关键词（可选）
- `sort_by`: 排序字段（可选）
- `sort_order`: 排序方向 asc/desc（可选）
- `filters`: 筛选条件（JSON格式）

**响应示例**:
```json
{
  "success": true,
  "data": {
    "products": [
      {
        "id": "12345",
        "title": "华为 Mate 50 Pro",
        "price": 5999,
        "original_price": 6999,
        "image_url": "https://...",
        "sales": 10000,
        "comments": 5000,
        "rating": 4.8,
        "shop_name": "华为官方旗舰店",
        "shop_type": "flagship",
        "brand": "华为",
        "category": "手机",
        "subsidy_info": {
          "has_subsidy": true,
          "subsidy_amount": 200,
          "subsidy_type": "限时优惠",
          "original_price": 6999,
          "discounted_price": 5999
        },
        "specs": {
          "color": "曜金黑",
          "storage": "256GB",
          "ram": "8GB"
        },
        "created_at": "2025-08-01T10:30:45Z"
      }
    ],
    "total": 500,
    "page": 1,
    "page_size": 20,
    "has_next": true
  },
  "message": "数据获取成功"
}
```

### GET /products/{id}
获取单个产品详情

**路径参数**:
- `id`: 产品ID

**响应示例**:
```json
{
  "success": true,
  "data": {
    "id": "12345",
    "title": "华为 Mate 50 Pro",
    "description": "华为最新旗舰手机...",
    "price": 5999,
    "original_price": 6999,
    "images": [
      "https://img1.jpg",
      "https://img2.jpg"
    ],
    "sales": 10000,
    "comments": 5000,
    "rating": 4.8,
    "shop_info": {
      "name": "华为官方旗舰店",
      "type": "flagship",
      "rating": 4.9,
      "followers": 100000
    },
    "reviews": [
      {
        "user": "用户***",
        "rating": 5,
        "content": "非常好用",
        "date": "2025-07-30"
      }
    ],
    "subsidy_info": {
      "has_subsidy": true,
      "subsidy_amount": 200,
      "subsidy_type": "限时优惠",
      "subsidy_desc": "限时优惠200元，仅限今日"
    }
  },
  "message": "产品详情获取成功"
}
```

### GET /statistics
获取统计数据

**请求参数**:
- `task_id`: 任务ID（可选）
- `date_range`: 日期范围（可选）

**响应示例**:
```json
{
  "success": true,
  "data": {
    "overview": {
      "total_products": 1500,
      "total_tasks": 10,
      "success_rate": 95.2,
      "average_price": 299.5
    },
    "brand_distribution": [
      {"brand": "华为", "count": 150, "percentage": 10.0},
      {"brand": "小米", "count": 120, "percentage": 8.0}
    ],
    "price_distribution": [
      {"range": "0-100", "count": 300},
      {"range": "100-500", "count": 800},
      {"range": "500-1000", "count": 300},
      {"range": "1000+", "count": 100}
    ],
    "category_distribution": [
      {"category": "手机", "count": 500},
      {"category": "电脑", "count": 300}
    ],
    "subsidy_statistics": {
      "products_with_subsidy": 800,
      "total_subsidy_amount": 150000,
      "average_subsidy": 187.5,
      "subsidy_types": [
        {"type": "限时优惠", "count": 400},
        {"type": "新人专享", "count": 200},
        {"type": "会员专享", "count": 200}
      ]
    }
  },
  "message": "统计数据获取成功"
}
```

## 数据导出接口

### POST /export
创建导出任务

**请求体**:
```json
{
  "task_id": "task_20250801_103045",
  "format": "excel",
  "fields": [
    "title", "price", "sales", "rating", 
    "shop_name", "subsidy_info"
  ],
  "filters": {
    "price_range": [100, 1000],
    "has_subsidy": true
  },
  "filename": "产品数据_20250801"
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "export_id": "export_20250801_103045",
    "estimated_time": "2-3分钟",
    "estimated_size": "5.2MB"
  },
  "message": "导出任务已创建"
}
```

### GET /export/{export_id}/progress
获取导出进度

**路径参数**:
- `export_id`: 导出任务ID

**响应示例**:
```json
{
  "success": true,
  "data": {
    "export_id": "export_20250801_103045",
    "status": "processing",
    "progress": 65,
    "processed_records": 650,
    "total_records": 1000,
    "estimated_remaining": "1m 30s"
  },
  "message": "导出进度获取成功"
}
```

### GET /export/{export_id}/download
下载导出文件

**路径参数**:
- `export_id`: 导出任务ID

**响应**: 文件流（application/octet-stream）

### GET /export/history
获取导出历史

**请求参数**:
- `page`: 页码（默认1）
- `page_size`: 每页数量（默认20）

**响应示例**:
```json
{
  "success": true,
  "data": {
    "exports": [
      {
        "export_id": "export_20250801_103045",
        "task_id": "task_20250801_103045",
        "format": "excel",
        "filename": "产品数据_20250801.xlsx",
        "status": "completed",
        "file_size": 5242880,
        "records_count": 1000,
        "created_at": "2025-08-01T10:30:45Z",
        "completed_at": "2025-08-01T10:33:12Z"
      }
    ],
    "total": 5,
    "page": 1,
    "page_size": 20
  },
  "message": "导出历史获取成功"
}
```

### DELETE /export/{export_id}
删除导出文件

**路径参数**:
- `export_id`: 导出任务ID

**响应示例**:
```json
{
  "success": true,
  "data": {
    "export_id": "export_20250801_103045",
    "deleted": true
  },
  "message": "导出文件已删除"
}
```

## WebSocket 实时通信

### 连接建立
```javascript
const ws = new WebSocket('ws://localhost:8001/ws');
```

### 消息格式
所有WebSocket消息都采用JSON格式：

```json
{
  "type": "message_type",
  "data": {},
  "timestamp": "2025-08-01T10:30:45Z"
}
```

### 消息类型

#### 1. 连接确认 (connection_ack)
```json
{
  "type": "connection_ack",
  "data": {
    "client_id": "client_12345",
    "server_time": "2025-08-01T10:30:45Z"
  }
}
```

#### 2. 心跳 (heartbeat)
```json
{
  "type": "heartbeat",
  "data": {
    "server_time": "2025-08-01T10:30:45Z"
  }
}
```

#### 3. 任务状态更新 (task_status)
```json
{
  "type": "task_status",
  "data": {
    "task_id": "task_20250801_103045",
    "status": "running",
    "message": "任务正在运行"
  }
}
```

#### 4. 进度更新 (progress_update)
```json
{
  "type": "progress_update",
  "data": {
    "task_id": "task_20250801_103045",
    "current_keyword": "手机",
    "current_page": 5,
    "total_pages": 10,
    "products_found": 245,
    "completion_percentage": 65
  }
}
```

#### 5. 新产品数据 (new_product)
```json
{
  "type": "new_product",
  "data": {
    "task_id": "task_20250801_103045",
    "product": {
      "id": "12345",
      "title": "华为 Mate 50 Pro",
      "price": 5999,
      "subsidy_info": {
        "has_subsidy": true,
        "subsidy_amount": 200
      }
    }
  }
}
```

#### 6. 错误消息 (error)
```json
{
  "type": "error",
  "data": {
    "task_id": "task_20250801_103045",
    "error_code": "NETWORK_ERROR",
    "message": "网络连接错误",
    "details": "连接超时，正在重试"
  }
}
```

#### 7. 任务完成 (task_completed)
```json
{
  "type": "task_completed",
  "data": {
    "task_id": "task_20250801_103045",
    "status": "completed",
    "summary": {
      "total_products": 456,
      "success_rate": 95.2,
      "duration": "14m 47s"
    }
  }
}
```

#### 8. 统计更新 (stats_update)
```json
{
  "type": "stats_update",
  "data": {
    "task_id": "task_20250801_103045",
    "stats": {
      "products_count": 245,
      "average_price": 299.5,
      "subsidy_count": 150,
      "error_count": 2
    }
  }
}
```

## 错误码参考

### 通用错误码
- `INVALID_PARAMETER`: 参数错误
- `MISSING_PARAMETER`: 缺少必需参数
- `UNAUTHORIZED`: 未授权访问
- `FORBIDDEN`: 权限不足
- `NOT_FOUND`: 资源不存在
- `INTERNAL_ERROR`: 服务器内部错误

### 爬虫相关错误码
- `TASK_NOT_FOUND`: 任务不存在
- `TASK_ALREADY_RUNNING`: 任务已在运行
- `TASK_ALREADY_STOPPED`: 任务已停止
- `NO_VALID_COOKIES`: 没有有效的Cookie
- `RATE_LIMIT_EXCEEDED`: 请求频率超限
- `NETWORK_ERROR`: 网络连接错误

### Cookie相关错误码
- `INVALID_COOKIE_FORMAT`: Cookie格式错误
- `COOKIE_EXPIRED`: Cookie已过期
- `COOKIE_VALIDATION_FAILED`: Cookie验证失败
- `COOKIE_SAVE_FAILED`: Cookie保存失败

### 数据相关错误码
- `DATA_NOT_FOUND`: 数据不存在
- `EXPORT_FAILED`: 导出失败
- `FILE_NOT_FOUND`: 文件不存在
- `INVALID_FILE_FORMAT`: 文件格式错误

## 接口版本控制

当前API版本：`v1`

版本在HTTP头中指定：
```
Accept: application/json; version=1
```

## 安全性

### 请求验证
- 所有请求都会进行参数验证
- 敏感操作需要额外确认
- 防止XSS和SQL注入攻击

### 速率限制
- 普通接口：100请求/分钟
- 数据接口：50请求/分钟
- 导出接口：10请求/分钟

### CORS配置
```javascript
{
  "origin": ["http://localhost:5173", "http://localhost:3000"],
  "methods": ["GET", "POST", "PUT", "DELETE"],
  "allowedHeaders": ["Content-Type", "Authorization"]
}
```

## 开发调试

### 接口测试工具
推荐使用以下工具测试API：
- Postman
- Insomnia  
- curl命令行
- 浏览器开发者工具

### 示例curl命令
```bash
# 健康检查
curl -X GET http://localhost:8001/api/health

# 启动爬虫
curl -X POST http://localhost:8001/api/crawl/start \
  -H "Content-Type: application/json" \
  -d '{"keywords":["测试商品"],"max_pages":5}'

# 获取产品数据
curl -X GET "http://localhost:8001/api/products?page=1&page_size=10"
```

### WebSocket测试
```javascript
const ws = new WebSocket('ws://localhost:8001/ws');
ws.onopen = () => console.log('WebSocket连接已建立');
ws.onmessage = (event) => console.log('收到消息:', JSON.parse(event.data));
ws.onerror = (error) => console.error('WebSocket错误:', error);
```