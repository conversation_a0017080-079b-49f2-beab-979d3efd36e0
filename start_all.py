#!/usr/bin/env python3
"""
拼多多爬虫系统一键启动脚本
同时启动前端和后端服务
"""

import os
import sys
import subprocess
import platform
import time
import threading
import webbrowser
from pathlib import Path

class ColorPrint:
    """彩色输出工具类"""
    HEADER = '\033[95m'
    OKBLUE = '\033[94m'
    OKCYAN = '\033[96m'
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    
    @staticmethod
    def success(msg):
        print(f"{ColorPrint.OKGREEN}✅ {msg}{ColorPrint.ENDC}")
    
    @staticmethod
    def error(msg):
        print(f"{ColorPrint.FAIL}❌ {msg}{ColorPrint.ENDC}")
    
    @staticmethod
    def warning(msg):
        print(f"{ColorPrint.WARNING}⚠️  {msg}{ColorPrint.ENDC}")
    
    @staticmethod
    def info(msg):
        print(f"{ColorPrint.OKCYAN}ℹ️  {msg}{ColorPrint.ENDC}")
    
    @staticmethod
    def header(msg):
        print(f"{ColorPrint.HEADER}{ColorPrint.BOLD}{msg}{ColorPrint.ENDC}")

def check_environment():
    """检查运行环境"""
    print("\n📋 环境检查")
    print("-" * 40)
    
    # 检查Python
    python_version = f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
    ColorPrint.success(f"Python {python_version}")
    
    # 检查Node.js
    try:
        result = subprocess.run(['node', '--version'], capture_output=True, text=True)
        node_version = result.stdout.strip()
        ColorPrint.success(f"Node.js {node_version}")
    except FileNotFoundError:
        ColorPrint.error("Node.js未安装")
        return False
    
    # 检查npm
    try:
        # Windows系统可能需要使用npm.cmd
        npm_cmd = 'npm.cmd' if platform.system() == 'Windows' else 'npm'
        result = subprocess.run([npm_cmd, '--version'], capture_output=True, text=True, shell=True)
        if result.returncode == 0:
            npm_version = result.stdout.strip()
            ColorPrint.success(f"npm {npm_version}")
        else:
            # 尝试直接使用npm
            result = subprocess.run(['npm', '--version'], capture_output=True, text=True, shell=True)
            if result.returncode == 0:
                npm_version = result.stdout.strip()
                ColorPrint.success(f"npm {npm_version}")
            else:
                ColorPrint.error("npm未安装或无法访问")
                return False
    except Exception as e:
        ColorPrint.error(f"npm检查失败: {e}")
        return False
    
    return True

def start_backend():
    """在新线程中启动后端"""
    try:
        ColorPrint.info("正在启动后端服务...")
        
        # 切换到项目根目录
        os.chdir(Path(__file__).parent)
        
        # 启动uvicorn
        cmd = [
            sys.executable, '-m', 'uvicorn',
            'backend.api_server:app',
            '--host', '0.0.0.0',
            '--port', '8001',
            '--reload',
            '--log-level', 'info'
        ]
        
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        # 输出后端日志（带前缀）
        for line in process.stdout:
            print(f"[后端] {line}", end='')
        
    except Exception as e:
        ColorPrint.error(f"后端启动失败: {e}")

def start_frontend():
    """在新线程中启动前端"""
    try:
        ColorPrint.info("正在启动前端服务...")
        
        frontend_dir = Path(__file__).parent / 'frontend'
        
        # 检查依赖
        node_modules = frontend_dir / 'node_modules'
        if not node_modules.exists():
            ColorPrint.warning("前端依赖未安装，正在安装...")
            os.chdir(frontend_dir)
            # Windows系统使用npm.cmd
            npm_cmd = 'npm.cmd' if platform.system() == 'Windows' else 'npm'
            subprocess.run([npm_cmd, 'install'], check=True, shell=True)
            ColorPrint.success("前端依赖安装完成")
        
        # 切换到前端目录
        os.chdir(frontend_dir)
        
        # 根据操作系统选择命令
        if platform.system() == 'Windows':
            cmd = 'npm run dev'
        else:
            cmd = 'npm run dev'
        
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1,
            universal_newlines=True,
            shell=True
        )
        
        # 输出前端日志（带前缀）
        for line in process.stdout:
            print(f"[前端] {line}", end='')
        
    except Exception as e:
        ColorPrint.error(f"前端启动失败: {e}")

def wait_for_services():
    """等待服务启动完成"""
    import socket
    
    def check_port(port, max_attempts=30):
        for i in range(max_attempts):
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex(('localhost', port))
            sock.close()
            
            if result == 0:
                return True
            time.sleep(1)
        return False
    
    # 等待后端启动
    ColorPrint.info("等待后端服务启动...")
    if check_port(8001):
        ColorPrint.success("后端服务已就绪 (端口 8001)")
    else:
        ColorPrint.warning("后端服务启动超时")
    
    # 等待前端启动
    ColorPrint.info("等待前端服务启动...")
    if check_port(5173):
        ColorPrint.success("前端服务已就绪 (端口 5173)")
        return True
    else:
        ColorPrint.warning("前端服务启动超时")
        return False

def main():
    """主函数"""
    # 清屏
    os.system('cls' if platform.system() == 'Windows' else 'clear')
    
    # 显示标题
    ColorPrint.header("=" * 50)
    ColorPrint.header("🕷️  拼多多爬虫系统一键启动")
    ColorPrint.header("=" * 50)
    
    # 检查环境
    if not check_environment():
        ColorPrint.error("环境检查失败，请安装必要的依赖")
        sys.exit(1)
    
    print("\n🚀 服务启动")
    print("-" * 40)
    
    # 创建线程启动服务
    backend_thread = threading.Thread(target=start_backend, daemon=True)
    frontend_thread = threading.Thread(target=start_frontend, daemon=True)
    
    # 启动线程
    backend_thread.start()
    time.sleep(2)  # 让后端先启动
    frontend_thread.start()
    
    # 等待服务就绪
    print("\n⏳ 服务初始化")
    print("-" * 40)
    
    if wait_for_services():
        print("\n🎉 系统启动成功！")
        print("-" * 40)
        ColorPrint.info("前端地址: http://localhost:5173")
        ColorPrint.info("后端地址: http://localhost:8001")
        ColorPrint.info("API文档: http://localhost:8001/docs")
        print("-" * 40)
        
        # 自动打开浏览器
        time.sleep(2)
        ColorPrint.info("正在打开浏览器...")
        webbrowser.open('http://localhost:5173')
        
        print("\n按 Ctrl+C 停止所有服务\n")
        
        try:
            # 保持主线程运行
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n\n👋 正在停止所有服务...")
            ColorPrint.success("服务已停止")
    else:
        ColorPrint.error("服务启动失败")
        sys.exit(1)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 感谢使用！")
        sys.exit(0)
    except Exception as e:
        ColorPrint.error(f"发生错误: {e}")
        sys.exit(1)