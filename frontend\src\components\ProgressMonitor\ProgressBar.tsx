import React from 'react';
import { Progress } from 'antd';
import { formatTime } from './utils';
import type { ProgressBarProps } from './types';

const ProgressBar: React.FC<ProgressBarProps> = ({
  current,
  total,
  type = 'line',
  status = 'active',
  showInfo = true,
  size = 'default',
  strokeWidth,
  className,
}) => {
  // 计算进度百分比
  const percent = total > 0 ? Math.round((current / total) * 100) : 0;
  
  // 确定状态
  const progressStatus = current >= total ? 'success' : status;

  // 自定义格式化函数
  const format = (percent?: number) => {
    const actualPercent = percent || 0;
    if (type === 'circle') {
      return (
        <div style={{ textAlign: 'center', fontSize: '12px' }}>
          <div style={{ fontWeight: 'bold' }}>{actualPercent}%</div>
          <div style={{ color: '#666', marginTop: '4px' }}>
            {current}/{total}
          </div>
        </div>
      );
    }
    return `${current}/${total} (${actualPercent}%)`;
  };

  if (type === 'circle') {
    return (
      <div className={className}>
        <Progress
          type="circle"
          percent={percent}
          status={progressStatus}
          format={showInfo ? format : undefined}
          strokeWidth={strokeWidth || 6}
          size={size === 'small' ? 80 : 120}
        />
      </div>
    );
  }

  return (
    <div className={className}>
      <Progress
        percent={percent}
        status={progressStatus}
        showInfo={showInfo}
        format={showInfo ? format : undefined}
        strokeWidth={strokeWidth}
        size={size}
      />
    </div>
  );
};

interface EnhancedProgressBarProps extends ProgressBarProps {
  label?: string;
  estimatedTimeRemaining?: number;
  speed?: number;
  showDetails?: boolean;
}

export const EnhancedProgressBar: React.FC<EnhancedProgressBarProps> = ({
  label,
  estimatedTimeRemaining,
  speed,
  showDetails = true,
  ...progressProps
}) => {
  return (
    <div className="enhanced-progress-bar">
      {label && (
        <div 
          style={{ 
            marginBottom: '8px', 
            fontWeight: 'bold',
            color: '#262626'
          }}
        >
          {label}
        </div>
      )}
      
      <ProgressBar {...progressProps} />
      
      {showDetails && (
        <div 
          style={{ 
            marginTop: '8px', 
            display: 'flex', 
            justifyContent: 'space-between',
            fontSize: '12px',
            color: '#666'
          }}
        >
          <span>
            进度: {progressProps.current}/{progressProps.total}
          </span>
          
          {speed !== undefined && (
            <span>
              速度: {speed.toFixed(1)} 条/分钟
            </span>
          )}
          
          {estimatedTimeRemaining !== undefined && estimatedTimeRemaining > 0 && (
            <span>
              剩余: {formatTime(estimatedTimeRemaining)}
            </span>
          )}
        </div>
      )}
    </div>
  );
};

export default ProgressBar;