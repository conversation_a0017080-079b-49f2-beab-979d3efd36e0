// API相关类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

// 产品相关类型 - 包含完整的32个字段
export interface Product {
  // 基础信息
  goods_id: string;
  goods_name: string;
  keyword: string;
  
  // 价格信息
  price: number;
  original_price?: number;
  coupon_price?: number;
  market_price?: number;
  price_type_name?: string;
  
  // 销量与评价
  sales: number | string;
  comment_count?: number;
  rating?: number;
  sales_tip?: string;
  
  // 品牌与分类
  brand_name?: string;
  category?: string;
  
  // 补贴信息 - 重点字段
  subsidy_info?: string;
  
  // 活动信息
  activity_type_name?: string;
  merchant_type_name?: string;
  
  // 标签信息
  marketing_tags?: string;
  tags?: string;
  special_text?: string;
  
  // 图片与链接
  image_url: string;
  hd_thumb_url?: string;
  goods_url: string;
  
  // 时间戳
  created_time?: string;

  // 向后兼容字段
  id?: string;
  title?: string;
  originalPrice?: number;
  imageUrl?: string;
  shopName?: string;
  soldCount?: number;
  reviewCount?: number;
  brand?: string;
  specifications?: Record<string, string>;
  description?: string;
  url?: string;
}

// 搜索配置类型（旧版，保持向后兼容）
export interface SearchConfig {
  keyword: string;
  category?: string;
  minPrice?: number;
  maxPrice?: number;
  sortType?: 'sales' | 'price_asc' | 'price_desc' | 'rating' | 'latest';
  pageSize?: number;
  maxPages?: number;
}

// 新版多关键词搜索配置类型
export interface MultiKeywordSearchConfig {
  keywords: string[];
  targetCount: number;
  sortMethod: 'sales' | 'price_asc' | 'price_desc' | 'rating' | 'latest';
  maxPages: number;
  headless: boolean;
  enableFilter: boolean;
  // 过滤条件
  priceRange?: [number, number];
  minRating?: number;
  minSoldCount?: number;
  excludeKeywords?: string[];
}

// 爬虫状态类型
export interface CrawlStatus {
  isRunning: boolean;
  currentKeyword?: string;
  progress: {
    currentPage: number;
    totalPages: number;
    collectedCount: number;
    failedCount: number;
  };
  startTime?: string;
  estimatedEndTime?: string;
  error?: string;
}

// WebSocket消息类型
export interface WebSocketMessage {
  type: 'connected' | 'status' | 'progress' | 'data' | 'error' | 'completed' | 'keyword_started' | 'keyword_completed' | 'ping' | 'pong';
  payload: any;
  timestamp: string;
}

// 实时监控相关类型
export interface ProgressStats {
  currentKeyword?: string;
  keywordProgress: {
    current: number;
    total: number;
    completed: string[];
    failed: string[];
  };
  overallProgress: {
    currentPage: number;
    totalPages: number;
    collectedCount: number;
    failedCount: number;
    successRate: number;
  };
  performance: {
    speed: number; // 条/分钟
    averagePageTime: number; // 秒
    estimatedTimeRemaining: number; // 秒
  };
  startTime?: string;
  estimatedEndTime?: string;
}

export interface RealtimeStats {
  speed: number; // 爬取速度 (条/分钟)
  successRate: number; // 成功率
  errorCount: number; // 错误数量
  totalProcessed: number; // 已处理总数
  estimatedTimeRemaining: number; // 预计剩余时间 (秒)
}

export interface ConnectionStatus {
  isConnected: boolean;
  connectionState: 'connecting' | 'connected' | 'closing' | 'disconnected' | 'unknown';
  latency: number; // 延迟 (ms)
  reconnectAttempts: number;
  lastHeartbeat: Date | null;
  messagesReceived: number;
}

// 统计数据类型
export interface Statistics {
  totalProducts: number;
  totalKeywords: number;
  avgPrice: number;
  topBrands: Array<{ name: string; count: number }>;
  topCategories: Array<{ name: string; count: number }>;
  priceDistribution: Array<{ range: string; count: number }>;
}

// 表格列配置类型
export interface TableColumn {
  key: string;
  title: string;
  dataIndex: string;
  width?: number;
  sorter?: boolean;
  filters?: Array<{ text: string; value: string }>;
  render?: (value: any, record: any) => React.ReactNode;
}

// 过滤器配置类型
export interface FilterConfig {
  priceRange?: [number, number];
  brands?: string[];
  categories?: string[];
  minRating?: number;
  minSoldCount?: number;
}

// Cookie 相关类型
export interface Cookie {
  name: string;
  value: string;
  domain: string;
  path?: string;
  expires?: number;
  httpOnly?: boolean;
  secure?: boolean;
  sameSite?: 'Strict' | 'Lax' | 'None';
}

export interface CookieStatus {
  exists: boolean;
  valid: boolean;
  expiresAt?: string;
  cookies: Cookie[];
}

export interface CookieValidationResult {
  valid: boolean;
  message: string;
  details?: {
    missingCookies?: string[];
    expiredCookies?: string[];
    invalidCookies?: string[];
  };
}

// 数据预览相关类型
export interface DataPreviewStats {
  totalProducts: number;
  keywordStats: Array<{
    keyword: string;
    count: number;
    avgPrice: number;
    priceRange: [number, number];
  }>;
  priceDistribution: Array<{
    range: string;
    count: number;
    percentage: number;
  }>;
  brandDistribution: Array<{
    brand: string;
    count: number;
    percentage: number;
  }>;
  subsidyStats: {
    totalWithSubsidy: number;
    subsidyPercentage: number;
    govSubsidyCount: number;
    govSubsidyPercentage: number;
  };
  salesStats: {
    totalSales: number;
    avgSales: number;
    topProducts: Product[];
  };
}

// 导出相关类型
export interface ExportRequest {
  format: 'excel' | 'csv';
  filename?: string;
  includeImages?: boolean;
  customFields?: string[];
}

export interface ExportProgress {
  taskId: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  total: number;
  message?: string;
  downloadUrl?: string;
  error?: string;
}

// 表格配置类型
export interface ColumnConfig {
  key: string;
  title: string;
  dataIndex: string;
  width?: number;
  fixed?: 'left' | 'right';
  sorter?: boolean;
  render?: 'text' | 'price' | 'image' | 'link' | 'tag' | 'subsidy';
  visible: boolean;
}

// 过滤器类型
export interface DataFilter {
  keyword?: string;
  priceRange?: [number, number];
  brands?: string[];
  hasSubsidy?: boolean;
  minSales?: number;
  minRating?: number;
}