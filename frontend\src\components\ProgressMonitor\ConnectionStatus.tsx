import React, { useState, useEffect } from 'react';
import { Card, Badge, Button, Space, Typography, Row, Col } from 'antd';
import {
  WifiOutlined,
  DisconnectOutlined,
  ReloadOutlined,
  InfoCircleOutlined,
  ThunderboltOutlined,
  HeartOutlined,
} from '@ant-design/icons';
import { 
  getConnectionStatusColor, 
  getConnectionStatusText, 
  formatLatency 
} from './utils';
import type { ConnectionStatusProps } from './types';

const { Text } = Typography;

const ConnectionStatus: React.FC<ConnectionStatusProps> = ({
  isConnected,
  connectionState,
  latency,
  reconnectAttempts,
  lastHeartbeat,
  messagesReceived,
  onReconnect,
}) => {
  const [isReconnecting, setIsReconnecting] = useState(false);
  const [heartbeatAnimation, setHeartbeatAnimation] = useState(false);

  // 处理重连
  const handleReconnect = async () => {
    if (!onReconnect) return;
    
    setIsReconnecting(true);
    try {
      await onReconnect();
    } finally {
      setTimeout(() => setIsReconnecting(false), 1000);
    }
  };

  // 心跳动画效果
  useEffect(() => {
    if (lastHeartbeat) {
      setHeartbeatAnimation(true);
      const timer = setTimeout(() => setHeartbeatAnimation(false), 500);
      return () => clearTimeout(timer);
    }
  }, [lastHeartbeat]);

  // 获取状态图标
  const getStatusIcon = () => {
    if (isConnected) {
      return <WifiOutlined style={{ color: '#52c41a' }} />;
    } else {
      return <DisconnectOutlined style={{ color: '#ff4d4f' }} />;
    }
  };

  // 获取延迟状态
  const getLatencyStatus = (lat: number) => {
    if (lat < 100) return 'success';
    if (lat < 500) return 'warning';
    return 'error';
  };

  // 格式化心跳时间
  const formatHeartbeatTime = (date: Date | null) => {
    if (!date) return '无';
    const now = new Date();
    const diff = Math.floor((now.getTime() - date.getTime()) / 1000);
    
    if (diff < 5) return '刚刚';
    if (diff < 60) return `${diff}秒前`;
    if (diff < 3600) return `${Math.floor(diff / 60)}分钟前`;
    return date.toLocaleTimeString();
  };

  return (
    <Card 
      title={
        <Space>
          {getStatusIcon()}
          <span>连接状态</span>
          <Badge 
            color={getConnectionStatusColor(connectionState)}
            text={getConnectionStatusText(connectionState)}
          />
        </Space>
      }
      size="small"
      extra={
        !isConnected && onReconnect && (
          <Button
            size="small"
            type="primary"
            icon={<ReloadOutlined />}
            loading={isReconnecting}
            onClick={handleReconnect}
          >
            重连
          </Button>
        )
      }
      style={{ height: '100%' }}
    >
      <Space direction="vertical" style={{ width: '100%' }}>
        {/* 连接信息 */}
        <Row gutter={16}>
          <Col span={12}>
            <div>
              <Text type="secondary">连接状态</Text>
              <div>
                <Badge 
                  status={isConnected ? 'success' : 'error'}
                  text={
                    <Text strong style={{ color: getConnectionStatusColor(connectionState) }}>
                      {getConnectionStatusText(connectionState)}
                    </Text>
                  }
                />
              </div>
            </div>
          </Col>
          
          <Col span={12}>
            <div>
              <Text type="secondary">网络延迟</Text>
              <div>
                <Space>
                  <ThunderboltOutlined 
                    style={{ 
                      color: getLatencyStatus(latency) === 'success' ? '#52c41a' : 
                             getLatencyStatus(latency) === 'warning' ? '#faad14' : '#ff4d4f' 
                    }} 
                  />
                  <Text strong>
                    {isConnected ? formatLatency(latency) : '无连接'}
                  </Text>
                </Space>
              </div>
            </div>
          </Col>
        </Row>

        {/* 详细统计 */}
        <Row gutter={16}>
          <Col span={12}>
            <div>
              <Text type="secondary">接收消息</Text>
              <div>
                <Text strong style={{ color: '#1890ff' }}>
                  {messagesReceived} 条
                </Text>
              </div>
            </div>
          </Col>
          
          <Col span={12}>
            <div>
              <Text type="secondary">重连次数</Text>
              <div>
                <Text strong style={{ color: reconnectAttempts > 0 ? '#faad14' : '#52c41a' }}>
                  {reconnectAttempts} 次
                </Text>
              </div>
            </div>
          </Col>
        </Row>

        {/* 心跳信息 */}
        <Row>
          <Col span={24}>
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Space>
                <Text type="secondary">最后心跳</Text>
                <HeartOutlined 
                  style={{ 
                    color: isConnected ? '#ff4d4f' : '#d9d9d9',
                    animation: heartbeatAnimation ? 'heartbeat 0.5s ease-in-out' : 'none',
                  }} 
                />
              </Space>
              <Text strong>
                {formatHeartbeatTime(lastHeartbeat)}
              </Text>
            </div>
          </Col>
        </Row>

        {/* 连接质量指示器 */}
        <div 
          style={{
            height: '4px',
            background: '#f0f0f0',
            borderRadius: '2px',
            overflow: 'hidden',
            position: 'relative',
          }}
        >
          <div
            style={{
              height: '100%',
              background: isConnected 
                ? `linear-gradient(90deg, ${getConnectionStatusColor(connectionState)}, #87d068)`
                : '#ff4d4f',
              borderRadius: '2px',
              width: isConnected ? '100%' : '0%',
              transition: 'width 0.3s ease-in-out',
            }}
          />
        </div>

        {/* 提示信息 */}
        {!isConnected && (
          <div style={{ textAlign: 'center', marginTop: '8px' }}>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              <InfoCircleOutlined style={{ marginRight: '4px' }} />
              连接断开时无法接收实时数据
            </Text>
          </div>
        )}
      </Space>

      {/* CSS 动画定义 */}
      <style>{`
        @keyframes heartbeat {
          0% { transform: scale(1); }
          25% { transform: scale(1.1); }
          50% { transform: scale(1); }
          75% { transform: scale(1.1); }
          100% { transform: scale(1); }
        }
      `}</style>
    </Card>
  );
};

export default ConnectionStatus;