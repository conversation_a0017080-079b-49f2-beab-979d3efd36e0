{"validation_timestamp": "2025-07-30T16:29:43.193711", "performance_scores": {"brand_identification": 95.0, "system_consistency": 100.0, "field_decoding": 55.55555555555556}, "targets_achieved": {"brand_identification": true, "system_consistency": true, "field_decoding": false}, "improvements": {"brand_identification": 11.299999999999997, "system_consistency": 8.5, "field_decoding": -22.24444444444444}, "overall_score": 83.51851851851852, "detailed_results": {"brand_identification": {"total_tested": 100, "successful_identifications": 0, "enhanced_method_results": {}, "traditional_method_results": {"1": {"brand": "苏泊尔", "is_valid": true}, "2": {"brand": "容声", "is_valid": true}, "3": {"brand": "七星", "is_valid": true}, "4": {"brand": "卡萨帝", "is_valid": true}, "5": {"brand": "统帅", "is_valid": true}, "6": {"brand": "博世", "is_valid": true}, "7": {"brand": "海尔", "is_valid": true}, "8": {"brand": "容声", "is_valid": true}, "9": {"brand": "长虹", "is_valid": true}, "10": {"brand": "海尔", "is_valid": true}, "11": {"brand": "海尔", "is_valid": true}, "12": {"brand": "容声", "is_valid": true}, "13": {"brand": "小米", "is_valid": true}, "14": {"brand": "海尔", "is_valid": true}, "15": {"brand": "容声", "is_valid": true}, "16": {"brand": "小米", "is_valid": true}, "17": {"brand": "美的", "is_valid": true}, "18": {"brand": "海尔", "is_valid": true}, "19": {"brand": "美的", "is_valid": true}, "20": {"brand": "海尔", "is_valid": true}, "21": {"brand": "", "is_valid": false}, "22": {"brand": "统帅", "is_valid": true}, "23": {"brand": "海尔", "is_valid": true}, "24": {"brand": "统帅", "is_valid": false}, "25": {"brand": "海尔", "is_valid": true}, "26": {"brand": "海尔", "is_valid": true}, "27": {"brand": "海尔", "is_valid": true}, "28": {"brand": "长虹", "is_valid": true}, "29": {"brand": "海尔", "is_valid": true}, "30": {"brand": "海信", "is_valid": true}, "31": {"brand": "美的", "is_valid": true}, "32": {"brand": "海尔", "is_valid": true}, "33": {"brand": "美的", "is_valid": true}, "34": {"brand": "海尔", "is_valid": true}, "35": {"brand": "美的", "is_valid": true}, "36": {"brand": "海尔", "is_valid": true}, "37": {"brand": "海尔", "is_valid": true}, "38": {"brand": "美的", "is_valid": true}, "39": {"brand": "海尔", "is_valid": true}, "40": {"brand": "统帅", "is_valid": false}, "41": {"brand": "海尔", "is_valid": true}, "42": {"brand": "海尔", "is_valid": true}, "43": {"brand": "小米", "is_valid": true}, "44": {"brand": "统帅", "is_valid": true}, "45": {"brand": "美的", "is_valid": true}, "46": {"brand": "美的", "is_valid": true}, "47": {"brand": "志高", "is_valid": true}, "48": {"brand": "海尔", "is_valid": true}, "49": {"brand": "韩电", "is_valid": true}, "50": {"brand": "", "is_valid": false}, "51": {"brand": "统帅", "is_valid": false}, "52": {"brand": "统帅", "is_valid": true}, "53": {"brand": "格兰仕", "is_valid": true}, "54": {"brand": "统帅", "is_valid": true}, "55": {"brand": "美的", "is_valid": true}, "56": {"brand": "海信", "is_valid": true}, "57": {"brand": "奥克斯", "is_valid": true}, "58": {"brand": "统帅", "is_valid": true}, "59": {"brand": "海尔", "is_valid": true}, "60": {"brand": "容声", "is_valid": true}, "61": {"brand": "美的", "is_valid": true}, "62": {"brand": "海信", "is_valid": true}, "63": {"brand": "小米", "is_valid": true}, "64": {"brand": "海尔", "is_valid": true}, "65": {"brand": "三星", "is_valid": true}, "66": {"brand": "西门子", "is_valid": true}, "67": {"brand": "海尔", "is_valid": true}, "68": {"brand": "小米", "is_valid": true}, "69": {"brand": "海信", "is_valid": true}, "70": {"brand": "海尔", "is_valid": true}, "71": {"brand": "海尔", "is_valid": true}, "72": {"brand": "海尔", "is_valid": true}, "73": {"brand": "小米", "is_valid": true}, "74": {"brand": "新飞", "is_valid": true}, "75": {"brand": "海尔", "is_valid": true}, "76": {"brand": "统帅", "is_valid": true}, "77": {"brand": "海信", "is_valid": true}, "78": {"brand": "奥马", "is_valid": true}, "79": {"brand": "统帅", "is_valid": true}, "80": {"brand": "东芝", "is_valid": true}, "81": {"brand": "容声", "is_valid": true}, "82": {"brand": "tcl", "is_valid": true}, "83": {"brand": "卡萨帝", "is_valid": true}, "84": {"brand": "美的", "is_valid": true}, "85": {"brand": "海尔", "is_valid": true}, "86": {"brand": "卡萨帝", "is_valid": true}, "87": {"brand": "容声", "is_valid": true}, "88": {"brand": "容声", "is_valid": true}, "89": {"brand": "卡萨帝", "is_valid": true}, "90": {"brand": "美的", "is_valid": true}, "91": {"brand": "三星", "is_valid": true}, "92": {"brand": "统帅", "is_valid": true}, "93": {"brand": "海信", "is_valid": true}, "94": {"brand": "tcl", "is_valid": true}, "95": {"brand": "tcl", "is_valid": true}, "96": {"brand": "海尔", "is_valid": true}, "97": {"brand": "海尔", "is_valid": true}, "98": {"brand": "卡萨帝", "is_valid": true}, "99": {"brand": "统帅", "is_valid": true}, "100": {"brand": "小米", "is_valid": true}}, "accuracy_comparison": {"enhanced_accuracy": 0, "traditional_accuracy": 95.0, "improvement": -95.0, "target_achieved": false}, "sample_results": [{"goods_name": "苏泊尔冰箱四开门铜管双温双压缩机立式冰柜商用大容量厨房冷冻柜", "brand_id": 8, "original_brand": "苏泊尔", "enhanced_result": null, "traditional_result": "苏泊尔"}, {"goods_name": "Ron<PERSON>n/容声BCD-515WKK2FPGA 十字门离子净化智能风冷双循环冰箱", "brand_id": 0, "original_brand": "容声", "enhanced_result": null, "traditional_result": "容声"}, {"goods_name": "七星475升镜面十字四开门电冰箱双系统变频家用一级能效风冷无霜", "brand_id": 17168, "original_brand": "七星", "enhanced_result": null, "traditional_result": "七星"}, {"goods_name": "Casarte/卡萨帝 BCD-500WLCTS7MDEU1双系统500升对开三门家用冰箱", "brand_id": 234, "original_brand": "卡萨帝", "enhanced_result": null, "traditional_result": "卡萨帝"}, {"goods_name": "海尔统帅电冰箱520十字对开四门家用超薄零嵌入风冷无霜一级能效", "brand_id": 3838, "original_brand": "海尔", "enhanced_result": null, "traditional_result": "统帅"}, {"goods_name": "博世全域智净M8轻享版十字门冰箱K5C821E23C超薄风冷无霜一级变频", "brand_id": 0, "original_brand": "博世", "enhanced_result": null, "traditional_result": "博世"}, {"goods_name": "Haier海尔冰箱505L侧T三开门全空间保鲜零嵌入式双重净化家用冰箱", "brand_id": 234, "original_brand": "海尔", "enhanced_result": null, "traditional_result": "海尔"}, {"goods_name": "容声 BCD-542WRS2HPGA美式风冷无霜一级变频杀菌对开门三开门冰箱", "brand_id": 0, "original_brand": "容声", "enhanced_result": null, "traditional_result": "容声"}, {"goods_name": "补贴20%】长虹400升法式风冷冰箱零嵌底部散热保鲜变频无霜家用款", "brand_id": 228, "original_brand": "长虹", "enhanced_result": null, "traditional_result": "长虹"}, {"goods_name": "海尔冰箱520升对开门十字四开门超薄零嵌入BCD-520WGHTD1BGCU1", "brand_id": 234, "original_brand": "海尔", "enhanced_result": null, "traditional_result": "海尔"}]}, "system_consistency": {"total_tested": 50, "consistent_results": 50, "conflicts_detected": 0, "consistency_details": [], "conflict_resolution_success": 0, "consistency_rate": 100.0, "conflict_rate": 0.0, "target_achieved": true}, "field_decoding": {"total_fields": 9, "decodable_fields": 5, "field_analysis": {"brand_id": {"has_decoder": true, "decoder_type": "traditional"}, "price_type": {"has_decoder": true, "decoder_type": "traditional"}, "merchant_type": {"has_decoder": true, "decoder_type": "traditional"}, "shop_id": {"has_decoder": false, "decoder_type": "traditional"}, "activity_type": {"has_decoder": true, "decoder_type": "traditional"}, "goods_id": {"has_decoder": false, "decoder_type": "traditional"}, "mall_id": {"has_decoder": false, "decoder_type": "traditional"}, "ad_id": {"has_decoder": false, "decoder_type": "traditional"}, "event_type": {"has_decoder": true, "decoder_type": "traditional"}}, "decoding_examples": {}, "coverage_rate": 55.55555555555556, "target_achieved": false}}, "summary": {"total_targets": 4, "targets_achieved_count": 2, "success_rate": 66.66666666666666, "average_improvement": -0.8148148148148143}}