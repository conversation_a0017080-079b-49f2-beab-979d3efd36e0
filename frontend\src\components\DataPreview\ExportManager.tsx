/**
 * 导出管理组件 - 支持Excel和CSV格式导出
 */

import React, { useState, useCallback } from 'react';
import { Card, Button, Space, Select, Input, Checkbox, Progress, message, Modal, Form, Tag, Divider } from 'antd';
import { DownloadOutlined, FileExcelOutlined, FileTextOutlined, SettingOutlined, InfoCircleOutlined } from '@ant-design/icons';
import type { ExportRequest } from '../../types';
import type { ExportManagerProps } from './types';
import { generateExportFilename } from './utils';

const { Option } = Select;

const ExportManager: React.FC<ExportManagerProps> = ({
  data = [],
  onExport,
  exportProgress,
  disabled = false
}) => {
  const [exportModalVisible, setExportModalVisible] = useState(false);
  const [form] = Form.useForm();

  // 可导出的字段配置
  const availableFields = [
    { key: 'goods_id', label: '商品ID', required: true },
    { key: 'goods_name', label: '商品名称', required: true },
    { key: 'keyword', label: '搜索关键词', required: false },
    { key: 'price', label: '拼团价', required: false },
    { key: 'original_price', label: '原价', required: false },
    { key: 'coupon_price', label: '券后价', required: false },
    { key: 'market_price', label: '市场价', required: false },
    { key: 'sales', label: '销量', required: false },
    { key: 'comment_count', label: '评论数', required: false },
    { key: 'rating', label: '评分', required: false },
    { key: 'brand_name', label: '品牌名称', required: false },
    { key: 'category', label: '商品分类', required: false },
    { key: 'subsidy_info', label: '补贴详情', required: false, highlight: true },
    { key: 'activity_type_name', label: '活动类型', required: false },
    { key: 'merchant_type_name', label: '商家类型', required: false },
    { key: 'marketing_tags', label: '营销标签', required: false },
    { key: 'tags', label: '商品标签', required: false },
    { key: 'special_text', label: '特殊信息', required: false },
    { key: 'image_url', label: '商品图片', required: false },
    { key: 'hd_thumb_url', label: '高清图片', required: false },
    { key: 'goods_url', label: '商品链接', required: false },
    { key: 'created_time', label: '采集时间', required: false }
  ];

  // 快速导出 - Excel
  const handleQuickExportExcel = useCallback(async () => {
    if (!onExport || data.length === 0) return;

    const keywords = Array.from(new Set(data.map(p => p.keyword).filter(k => k)));
    const filename = generateExportFilename('excel', keywords);

    const request: ExportRequest = {
      format: 'excel',
      filename,
      includeImages: false,
      customFields: availableFields.map(f => f.key)
    };

    try {
      await onExport(request);
      message.success('Excel导出请求已提交');
    } catch (error) {
      message.error('导出失败: ' + (error as Error).message);
    }
  }, [data, onExport, availableFields]);

  // 快速导出 - CSV
  const handleQuickExportCSV = useCallback(async () => {
    if (!onExport || data.length === 0) return;

    const keywords = Array.from(new Set(data.map(p => p.keyword).filter(k => k)));
    const filename = generateExportFilename('csv', keywords);

    const request: ExportRequest = {
      format: 'csv',
      filename,
      includeImages: false,
      customFields: availableFields.map(f => f.key)
    };

    try {
      await onExport(request);
      message.success('CSV导出请求已提交');
    } catch (error) {
      message.error('导出失败: ' + (error as Error).message);
    }
  }, [data, onExport, availableFields]);

  // 自定义导出
  const handleCustomExport = useCallback(async () => {
    try {
      const values = await form.validateFields();
      
      if (!values.customFields || values.customFields.length === 0) {
        message.warning('请至少选择一个字段');
        return;
      }

      const keywords = Array.from(new Set(data.map(p => p.keyword).filter(k => k)));
      const defaultFilename = generateExportFilename(values.format, keywords);

      const request: ExportRequest = {
        format: values.format,
        filename: values.filename || defaultFilename,
        includeImages: values.includeImages || false,
        customFields: values.customFields
      };

      await onExport?.(request);
      message.success('导出请求已提交');
      setExportModalVisible(false);
      form.resetFields();
    } catch (error) {
      message.error('导出失败: ' + (error as Error).message);
    }
  }, [data, onExport, form]);

  // 数据统计信息
  const getDataStats = () => {
    const totalProducts = data.length;
    const keywords = Array.from(new Set(data.map(p => p.keyword).filter(k => k)));
    const withSubsidy = data.filter(p => p.subsidy_info?.trim()).length;
    const brands = Array.from(new Set(data.map(p => p.brand_name).filter(b => b)));

    return {
      totalProducts,
      keywordCount: keywords.length,
      subsidyCount: withSubsidy,
      brandCount: brands.length,
      keywords
    };
  };

  const stats = getDataStats();

  return (
    <div>
      <Card title="数据导出" extra={<DownloadOutlined />}>
        {/* 数据概览 */}
        <div style={{ marginBottom: 16, padding: 16, backgroundColor: '#fafafa', borderRadius: 6 }}>
          <h4 style={{ margin: '0 0 12px 0', display: 'flex', alignItems: 'center' }}>
            <InfoCircleOutlined style={{ marginRight: 8 }} />
            当前数据概览
          </h4>
          <Space wrap>
            <Tag color="blue">总商品数: {stats.totalProducts}</Tag>
            <Tag color="green">关键词数: {stats.keywordCount}</Tag>
            <Tag color="red">补贴商品: {stats.subsidyCount}</Tag>
            <Tag color="orange">品牌数: {stats.brandCount}</Tag>
          </Space>
          {stats.keywords.length > 0 && (
            <div style={{ marginTop: 8 }}>
              <span style={{ fontWeight: 'bold', marginRight: 8 }}>关键词:</span>
              <Space wrap>
                {stats.keywords.map(keyword => (
                  <Tag key={keyword}>{keyword}</Tag>
                ))}
              </Space>
            </div>
          )}
        </div>

        {/* 导出进度 */}
        {exportProgress && exportProgress.status !== 'completed' && (
          <div style={{ marginBottom: 16 }}>
            <h4>导出进度</h4>
            <Progress
              percent={Math.round((exportProgress.progress / exportProgress.total) * 100)}
              status={exportProgress.status === 'failed' ? 'exception' : 'active'}
              format={(percent) => `${exportProgress.progress}/${exportProgress.total} (${percent}%)`}
            />
            <div style={{ marginTop: 8, color: '#666' }}>
              {exportProgress.message || '正在处理...'}
            </div>
            {exportProgress.error && (
              <div style={{ marginTop: 4, color: '#f5222d' }}>
                {exportProgress.error}
              </div>
            )}
          </div>
        )}

        {/* 导出完成 */}
        {exportProgress && exportProgress.status === 'completed' && exportProgress.downloadUrl && (
          <div style={{ marginBottom: 16, padding: 12, backgroundColor: '#f6ffed', border: '1px solid #b7eb8f', borderRadius: 6 }}>
            <div style={{ color: '#52c41a', fontWeight: 'bold', marginBottom: 8 }}>
              ✅ 导出完成！
            </div>
            <Button
              type="primary"
              icon={<DownloadOutlined />}
              href={exportProgress.downloadUrl}
              download
            >
              下载文件
            </Button>
          </div>
        )}

        {/* 快速导出按钮 */}
        <div style={{ marginBottom: 16 }}>
          <h4>快速导出</h4>
          <Space>
            <Button
              type="primary"
              icon={<FileExcelOutlined />}
              onClick={handleQuickExportExcel}
              disabled={disabled || data.length === 0}
              loading={exportProgress?.status === 'processing' && exportProgress.taskId.includes('excel')}
            >
              导出Excel
            </Button>
            <Button
              icon={<FileTextOutlined />}
              onClick={handleQuickExportCSV}
              disabled={disabled || data.length === 0}
              loading={exportProgress?.status === 'processing' && exportProgress.taskId.includes('csv')}
            >
              导出CSV
            </Button>
            <Button
              icon={<SettingOutlined />}
              onClick={() => setExportModalVisible(true)}
              disabled={disabled || data.length === 0}
            >
              自定义导出
            </Button>
          </Space>
        </div>

        {/* 导出说明 */}
        <div style={{ fontSize: '12px', color: '#666' }}>
          <div>• 文件命名格式: 拼多多商品数据_关键词_时间戳.格式</div>
          <div>• Excel格式包含完整格式化和图表，CSV格式更适合数据处理</div>
          <div>• <strong style={{ color: '#f5222d' }}>补贴详情字段包含百亿补贴和政府补贴信息</strong></div>
          <div>• 导出的数据与后端格式100%一致，确保数据准确性</div>
        </div>
      </Card>

      {/* 自定义导出弹窗 */}
      <Modal
        title="自定义导出设置"
        open={exportModalVisible}
        onOk={handleCustomExport}
        onCancel={() => {
          setExportModalVisible(false);
          form.resetFields();
        }}
        width={600}
        okText="开始导出"
        cancelText="取消"
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            format: 'excel',
            includeImages: false,
            customFields: availableFields.filter(f => f.required || f.key === 'subsidy_info').map(f => f.key)
          }}
        >
          <Form.Item
            label="导出格式"
            name="format"
            rules={[{ required: true, message: '请选择导出格式' }]}
          >
            <Select>
              <Option value="excel">Excel (.xlsx)</Option>
              <Option value="csv">CSV (.csv)</Option>
            </Select>
          </Form.Item>

          <Form.Item
            label="文件名"
            name="filename"
            extra="留空则自动生成文件名"
          >
            <Input placeholder="自定义文件名（可选）" />
          </Form.Item>

          <Form.Item
            label="导出字段"
            name="customFields"
            rules={[{ required: true, message: '请至少选择一个字段' }]}
          >
            <Checkbox.Group style={{ width: '100%' }}>
              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: '8px 16px' }}>
                {availableFields.map(field => (
                  <Checkbox
                    key={field.key}
                    value={field.key}
                    disabled={field.required}
                  >
                    <span style={{ 
                      color: field.highlight ? '#f5222d' : field.required ? '#1890ff' : 'inherit',
                      fontWeight: field.highlight || field.required ? 'bold' : 'normal'
                    }}>
                      {field.label}
                      {field.required && <span style={{ color: '#f5222d' }}> *</span>}
                      {field.highlight && <span style={{ color: '#f5222d' }}> 🎯</span>}
                    </span>
                  </Checkbox>
                ))}
              </div>
            </Checkbox.Group>
          </Form.Item>

          <Form.Item label="其他选项">
            <Form.Item name="includeImages" valuePropName="checked" noStyle>
              <Checkbox>包含图片链接</Checkbox>
            </Form.Item>
          </Form.Item>

          <Divider />
          
          <div style={{ fontSize: '12px', color: '#666' }}>
            <div>• 标记 <span style={{ color: '#1890ff', fontWeight: 'bold' }}>*</span> 的字段为必需字段，不可取消</div>
            <div>• 标记 <span style={{ color: '#f5222d', fontWeight: 'bold' }}>🎯</span> 的为重点关注字段（补贴详情）</div>
            <div>• Excel格式支持格式化显示，CSV格式适合数据分析</div>
          </div>
        </Form>
      </Modal>
    </div>
  );
};

export default ExportManager;