import type { SearchConfig } from '@/types';

// 任务信息
export interface TaskInfo {
  id: string;
  name: string;
  config: SearchConfig;
  status: 'pending' | 'running' | 'paused' | 'completed' | 'failed' | 'cancelled';
  createdAt: string;
  startedAt?: string;
  completedAt?: string;
  progress: {
    currentPage: number;
    totalPages: number;
    collectedCount: number;
    failedCount: number;
  };
  statistics?: {
    duration: number;
    speed: number; // 条/分钟
    successRate: number;
  };
  error?: string;
}

// 任务历史记录
export interface TaskHistory extends TaskInfo {
  result?: {
    totalProducts: number;
    exportPath?: string;
    summary: string;
  };
}

// 快速启动配置
export interface QuickStartConfig {
  id: string;
  name: string;
  config: SearchConfig;
  lastUsed?: string;
  useCount: number;
}

// 控制面板状态
export interface ControlPanelState {
  currentStep: 'cookie' | 'config' | 'start' | 'monitor';
  isValidated: {
    cookie: boolean;
    config: boolean;
  };
  quickConfigs: QuickStartConfig[];
  activeTasks: TaskInfo[];
  taskHistory: TaskHistory[];
}

// 步骤验证结果
export interface StepValidation {
  valid: boolean;
  message: string;
  details?: string[];
}

// 任务控制操作
export interface TaskControlActions {
  start: (config: SearchConfig) => Promise<string>; // 返回 taskId
  pause: (taskId: string) => Promise<void>;
  resume: (taskId: string) => Promise<void>;
  stop: (taskId: string) => Promise<void>;
  cancel: (taskId: string) => Promise<void>;
  retry: (taskId: string) => Promise<string>; // 返回新的 taskId
}

// 批量操作
export interface BatchOperation {
  type: 'pause' | 'resume' | 'stop' | 'cancel' | 'delete';
  taskIds: string[];
  confirm?: string; // 确认消息
}