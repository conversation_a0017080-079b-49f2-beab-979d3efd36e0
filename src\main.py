"""
拼多多商品数据爬虫主程序
整合所有模块，提供完整的爬虫功能

版本: 2.0.0
更新时间: 2025-01-25
支持环境: Windows + WSL双环境
新增功能: 29个数据字段提取，增强反检测能力
"""

import asyncio
import datetime
import random
import signal
import sys
import platform
import psutil
import os
from pathlib import Path
from typing import Dict, List, Any, Optional

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from loguru import logger

from src.core.browser_manager import BrowserManager
from src.core.login_detector import LoginDetector
from src.core.anti_detection_simple import SimpleAntiDetectionManager as AntiDetectionManager
from src.core.api_response_monitor import APIResponseMonitor
from src.core.sort_manager import SortManager
from src.core.scroll_manager import SmartScrollManager
from src.data.processor import DataProcessor
from src.data.exporter import ExcelExporter
from src.utils.helpers import load_config
from src.utils.retry import retry_async
from src.utils.resource_cleaner import create_resource_cleaner, get_resource_cleaner
from src.monitoring.memory_monitor import MemoryMonitor, create_memory_monitor


class PDDCrawler:
    """
    拼多多爬虫主类 v2.0

    新增功能:
    - 29个数据字段提取
    - 增强反检测能力
    - 双环境支持(Windows+WSL)
    - 智能重试机制
    """

    def __init__(self, config_path: str = "config/settings.yaml"):
        """初始化爬虫"""
        self.config = load_config(config_path)

        # 环境检测
        self.environment = self._detect_environment()
        logger.info(f"🖥️  检测到运行环境: {self.environment}")

        # 初始化组件
        self.browser_manager = BrowserManager(config_path)
        self.login_detector = LoginDetector()
        self.anti_detection = AntiDetectionManager()
        self.api_monitor = APIResponseMonitor()
        self.sort_manager = SortManager()
        self.scroll_manager = SmartScrollManager()
        self.data_processor = DataProcessor()
        self.excel_exporter = ExcelExporter()

        # 新增：账号管理器
        from src.core.account_manager import AccountManager
        self.account_manager = AccountManager()

        # 新增：内存监控器
        memory_config = self.config.get("memory_monitoring", {})
        self.memory_monitor = create_memory_monitor(memory_config)
        
        # 注册各组件的内存监控
        self._register_memory_monitoring()
        
        # 设置内存告警回调
        self.memory_monitor.add_alert_callback(self._on_memory_alert)
        
        # 启动内存监控
        self.memory_monitor.start_monitoring()

        # 新增：资源清理器
        self.resource_cleaner = create_resource_cleaner(config_path)
        
        # 注册清理回调
        self._register_cleanup_callbacks()
        
        # 启动自动清理
        self.resource_cleaner.start_auto_cleanup()

        # 爬取配置
        self.search_config = self.config.get("search", {})
        self.keywords = self.search_config.get("keywords", ["手机"])
        self.max_pages = self.search_config.get("max_pages", 5)
        self.target_count = self.search_config.get("target_count", 100)  # 目标商品数量
        
        # 排序配置
        self.sorting_enabled = self.config.get("sorting", {}).get("enabled", False)
        
        # 数据存储
        self.all_data = []
        self.collected_data = []  # 当前收集的数据
        self._data_lock = asyncio.Lock()  # 添加数据锁，确保线程安全
        self._collected_goods_ids = set()  # 用于去重的商品ID集合
        self.current_keyword = None
        self.current_keyword_index = 0  # 当前关键词索引
        self.total_collected = 0  # 总收集数量
        self.is_running = False  # 添加运行状态标志
        self._keyword_target_reached = False  # 当前关键词是否已达到目标标志

        # 版本信息
        self.version = "2.0.0"
        self.field_count = 29  # 新增字段数量

        logger.info(f"🚀 拼多多爬虫 v{self.version} 初始化完成")
        logger.info(f"📊 支持 {self.field_count} 个数据字段提取")
        logger.info(f"🎯 目标收集: {self.target_count} 个商品")
        logger.info(f"🖥️  运行环境: {self.environment}")
        logger.info(f"🔍 内存监控已启用，监控间隔: {self.memory_monitor.monitoring_interval}秒")
        logger.info(f"🧹 资源清理已启用，清理间隔: {self.resource_cleaner.auto_cleanup_interval}秒")
    
    def set_headless(self, headless: bool):
        """设置无头模式"""
        # 更新浏览器配置
        if hasattr(self.browser_manager, 'config'):
            self.browser_manager.config['browser']['headless'] = headless
            self.browser_manager.config['cdp']['headless'] = headless
        logger.info(f"🖥️  设置无头模式: {headless}")
    
    def set_filter_enabled(self, enabled: bool):
        """设置是否启用商品筛选"""
        # 更新配置中的商品筛选设置
        if 'product_filter' in self.config:
            self.config['product_filter']['enabled'] = enabled
        else:
            self.config['product_filter'] = {'enabled': enabled}
        logger.info(f"🔍 设置商品筛选: {'启用' if enabled else '禁用'}")

    def _detect_environment(self) -> str:
        """检测运行环境"""
        system = platform.system()
        if system == "Windows":
            if "microsoft" in platform.uname().release.lower():
                return "WSL"
            else:
                return "Windows"
        elif system == "Linux":
            return "Linux"
        elif system == "Darwin":
            return "macOS"
        else:
            return f"Unknown ({system})"
    
    def _register_memory_monitoring(self) -> None:
        """注册各组件的内存监控"""
        # 主爬虫组件
        self.memory_monitor.register_component('crawler', warning_threshold_mb=500, critical_threshold_mb=1000)
        
        # 浏览器管理器
        self.memory_monitor.register_component('browser_manager', warning_threshold_mb=400, critical_threshold_mb=800)
        
        # 数据处理器
        self.memory_monitor.register_component('data_processor', warning_threshold_mb=300, critical_threshold_mb=600)
        
        # API监控器
        self.memory_monitor.register_component('api_monitor', warning_threshold_mb=150, critical_threshold_mb=300)
        
        # 滚动管理器
        self.memory_monitor.register_component('scroll_manager', warning_threshold_mb=100, critical_threshold_mb=200)
        
        # 反检测管理器
        self.memory_monitor.register_component('anti_detection', warning_threshold_mb=100, critical_threshold_mb=200)
        
        # 系统总体监控
        self.memory_monitor.register_component('system', warning_threshold_mb=1200, critical_threshold_mb=2000)
        
        logger.info("已注册所有组件的内存监控")
    
    def _register_cleanup_callbacks(self) -> None:
        """注册资源清理回调"""
        # 注册浏览器管理器清理回调
        def cleanup_browser():
            try:
                if hasattr(self.browser_manager, 'cleanup_browser_data'):
                    self.browser_manager.cleanup_browser_data()
                logger.debug("浏览器管理器清理完成")
            except Exception as e:
                logger.debug(f"浏览器管理器清理失败: {e}")
        
        # 注册内存监控器清理回调
        def cleanup_memory_monitor():
            try:
                self.memory_monitor.stop_monitoring()
                logger.debug("内存监控器清理完成")
            except Exception as e:
                logger.debug(f"内存监控器清理失败: {e}")
        
        # 注册数据处理器清理回调
        def cleanup_data_processor():
            try:
                # 清理处理过程中的临时数据
                if hasattr(self.data_processor, 'clear_cache'):
                    self.data_processor.clear_cache()
                logger.debug("数据处理器清理完成")
            except Exception as e:
                logger.debug(f"数据处理器清理失败: {e}")
        
        # 注册API监控器清理回调
        def cleanup_api_monitor():
            try:
                if hasattr(self.api_monitor, 'cleanup'):
                    self.api_monitor.cleanup()
                logger.debug("API监控器清理完成")
            except Exception as e:
                logger.debug(f"API监控器清理失败: {e}")
        
        # 注册所有清理回调
        self.resource_cleaner.register_shutdown_callback(cleanup_browser)
        self.resource_cleaner.register_shutdown_callback(cleanup_memory_monitor)
        self.resource_cleaner.register_shutdown_callback(cleanup_data_processor)
        self.resource_cleaner.register_shutdown_callback(cleanup_api_monitor)
        
        logger.info("已注册所有组件的清理回调")
    
    def _on_memory_alert(self, alert) -> None:
        """内存告警回调处理"""
        from src.monitoring.memory_monitor import MemoryAlert
        
        if alert.alert_type == "critical":
            logger.error(f"🚨 严重内存告警: {alert.message}")
            # 严重告警时尝试垃圾回收
            self.memory_monitor.force_garbage_collection(alert.component)
            
        elif alert.alert_type == "warning":
            logger.warning(f"⚠️ 内存使用告警: {alert.message}")
            
        elif alert.alert_type == "leak_detected":
            logger.error(f"🔴 内存泄漏检测: {alert.message}")
            # 内存泄漏时强制垃圾回收并记录详细信息
            collected = self.memory_monitor.force_garbage_collection(alert.component)
            logger.info(f"强制垃圾回收后清理了 {collected} 个对象")
            
            # 如果持续泄漏，可以考虑重启相关组件
            stats = self.memory_monitor.get_component_statistics(alert.component)
            if stats and stats.leak_probability > 0.8:
                logger.error(f"组件 {alert.component} 泄漏概率过高: {stats.leak_probability:.2f}")
    
    def _enhanced_memory_monitoring(self, component_name: str, stage: str = "") -> None:
        """增强的内存监控记录"""
        try:
            snapshot = self.memory_monitor.record_memory_usage(component_name, stage)
            if snapshot:
                logger.debug(f"📊 [{component_name}] {stage} - 内存: {snapshot.process_memory_mb}MB, "
                           f"CPU: {snapshot.cpu_percent:.1f}%, 线程: {snapshot.thread_count}")
        except Exception as e:
            logger.debug(f"内存监控记录失败: {e}")
    
    def get_memory_statistics(self) -> Dict[str, Any]:
        """获取内存统计信息"""
        try:
            all_stats = self.memory_monitor.get_all_statistics()
            recent_alerts = self.memory_monitor.get_recent_alerts(limit=5)
            
            return {
                "statistics": {name: {
                    "avg_memory_mb": stats.avg_memory_mb,
                    "max_memory_mb": stats.max_memory_mb,
                    "growth_rate": stats.memory_growth_rate,
                    "leak_probability": stats.leak_probability,
                    "alerts_count": stats.alerts_count
                } for name, stats in all_stats.items()},
                "recent_alerts": [{
                    "component": alert.component,
                    "type": alert.alert_type,
                    "message": alert.message,
                    "timestamp": alert.timestamp
                } for alert in recent_alerts],
                "total_alerts": self.memory_monitor.total_alerts,
                "total_gc_collections": self.memory_monitor.total_gc_collections
            }
        except Exception as e:
            logger.error(f"获取内存统计失败: {e}")
            return {"error": str(e)}
    
    async def start_crawling(self, keywords: Optional[List[str]] = None, keep_browser_open: bool = False) -> Dict[str, Any]:
        """
        开始爬取 - 支持目标数量控制和多关键词无缝衔接
        
        Args:
            keywords: 可选的关键词列表，如果不提供则使用配置文件中的关键词
            keep_browser_open: 是否保持浏览器打开（用于多关键词连续处理）
            
        Returns:
            Dict[str, Any]: 爬取结果摘要
        """
        try:
            self.is_running = True
            keywords = keywords or self.keywords
            
            if not keywords:
                raise ValueError("未提供搜索关键词")
            
            logger.info(f"开始爬取，目标: {self.target_count} 个商品")
            logger.info(f"关键词列表: {keywords}")
            
            # 记录爬取开始时的内存状态
            self._enhanced_memory_monitoring("crawler", "爬取开始")
            
            # 如果需要保持浏览器打开，使用已有的浏览器实例
            if keep_browser_open and self.browser_manager.context:
                logger.info("使用已有的浏览器会话")
                page = self.browser_manager.page or await self.browser_manager.create_page()
                result = await self._crawl_keywords_in_session(page, keywords)
            else:
                # 启动浏览器（只启动一次）
                async with self.browser_manager as browser_mgr:
                    await browser_mgr.create_context()
                    page = await browser_mgr.create_page()
                    result = await self._crawl_keywords_in_session(page, keywords)
            
            # 记录爬取完成时的内存状态
            self._enhanced_memory_monitoring("crawler", "爬取完成")
            
            return result
            
        except asyncio.CancelledError:
            logger.info("爬取任务被取消")
            raise
        except KeyboardInterrupt:
            logger.info("用户中断爬取")
            raise
        except Exception as e:
            logger.error(f"爬取过程中出错: {type(e).__name__}: {e}")
            # 记录详细错误信息
            import traceback
            logger.debug(traceback.format_exc())
            raise
        finally:
            self.is_running = False
            # 导出内存监控报告  
            try:
                report_path = self.memory_monitor.export_memory_report()
                if report_path:
                    logger.info(f"📊 内存监控报告已保存: {report_path}")
            except Exception as e:
                logger.debug(f"导出内存报告失败: {e}")
            
            # 执行资源清理
            try:
                logger.info("执行爬取完成后的资源清理...")
                cleanup_result = self.resource_cleaner.perform_cleanup()
                if cleanup_result.get("success"):
                    logger.info(f"🧹 资源清理完成，释放空间: {cleanup_result.get('total_freed_mb', 0):.2f}MB")
                else:
                    logger.warning(f"资源清理失败: {cleanup_result.get('error', '未知错误')}")
            except Exception as e:
                logger.debug(f"资源清理失败: {e}")
    
    async def _crawl_keywords_in_session(self, page, keywords: List[str]) -> Dict[str, Any]:
        """在同一浏览器会话中爬取多个关键词"""
        try:
                
            # 设置API监听回调
            self.api_monitor.set_data_callback(self._on_data_received)
            
            # 启动反风控监控
            await self.anti_detection.monitor_requests(page)
            
            # 记录浏览器启动后的内存状态
            self._enhanced_memory_monitoring("browser_manager", "浏览器启动完成")
            
            # 检查登录状态
            await self._navigate_to_homepage(page)
            await self.login_detector.ensure_logged_in(page)
            
            # 重置数据
            self.collected_data.clear()
            self._collected_goods_ids.clear()
            self.total_collected = 0
            
            # 逐个关键词爬取，直到达到目标数量
            for idx, keyword in enumerate(keywords):
                if not self.is_running:
                    break
                
                # 检查是否已达到总目标（每个关键词独立计算）
                # 注释掉这个检查，让每个关键词都能收集完整数量
                # if self.total_collected >= self.target_count:
                #     logger.success(f"已达到总目标数量 {self.target_count}，停止爬取")
                #     break
                
                self.current_keyword = keyword
                self.current_keyword_index = idx

                # 关键词间状态清理（除第一个关键词外）
                if idx > 0:
                    await self._cleanup_between_keywords(page)
                    # 额外等待确保页面稳定
                    await asyncio.sleep(2)
                    logger.info("✅ 页面已稳定，准备搜索新关键词")
                
                # 重置关键词目标达成标志
                self._keyword_target_reached = False

                # 计算当前关键词需要收集的数量
                # 每个关键词都收集完整的目标数量
                keyword_target = self.target_count
                
                logger.info(f"\n{'='*50}")
                logger.info(f"开始爬取关键词 [{idx+1}/{len(keywords)}]: {keyword}")
                logger.info(f"当前进度: {self.total_collected}/{self.target_count}")
                logger.info(f"本关键词目标: {keyword_target} 个")
                logger.info(f"{'='*50}")

                # 内存监控 - 关键词开始
                self._enhanced_memory_monitoring("crawler", f"关键词开始: {keyword}")

                # 记录关键词开始时的数量
                keyword_start_count = len(self.collected_data)
                
                # 爬取当前关键词
                await self._crawl_keyword_with_target(page, keyword, keyword_target)

                # 监听器状态检查
                self.api_monitor.log_monitoring_stats("关键词爬取完成")

                # 统计本关键词收集的数量
                keyword_collected = len(self.collected_data) - keyword_start_count
                self.total_collected = len(self.collected_data)
                
                logger.info(f"关键词 '{keyword}' 收集完成: {keyword_collected} 个商品")
                logger.info(f"总进度: {self.total_collected}/{self.target_count} ({(self.total_collected/self.target_count)*100:.1f}%)")

                # 内存监控 - 关键词完成
                self._enhanced_memory_monitoring("crawler", f"关键词完成: {keyword}")
                
                # 记录数据处理器的内存使用
                self._enhanced_memory_monitoring("data_processor", f"数据处理: {keyword}")

                # 定期清理内存（每个关键词完成后）
                if keyword_collected > 100:  # 收集超过100条数据后进行清理
                    logger.debug(f"关键词 '{keyword}' 收集完成，执行内存清理")
                    
                    # 清理已导出的数据，只保留必要的统计信息
                    if idx > 0:  # 不是第一个关键词，可以清理之前的详细数据
                        # 找出之前关键词的数据并只保留统计信息
                        prev_keyword_data = []
                        current_keyword_data = []
                        for item in self.collected_data:
                            if item.get('keyword') == keyword:
                                current_keyword_data.append(item)
                            else:
                                # 只保留之前关键词的基本信息
                                prev_keyword_data.append({
                                    'goods_id': item.get('goods_id'),
                                    'keyword': item.get('keyword'),
                                    'goods_name': item.get('goods_name')[:50],  # 只保留前50个字符
                                    'price': item.get('price')
                                })
                        
                        # 更新collected_data，减少内存占用
                        self.collected_data = prev_keyword_data + current_keyword_data
                        logger.debug(f"内存优化：精简了之前关键词的数据，释放内存")
                    
                    # 强制垃圾回收
                    collected = self.memory_monitor.force_garbage_collection("crawler")
                    logger.debug(f"垃圾回收完成，清理了 {collected} 个对象")
                    
                    # 清理API监控器的缓存
                    if hasattr(self.api_monitor, 'clear_response_cache'):
                        self.api_monitor.clear_response_cache()
                        logger.debug("清理了API监控器的响应缓存")

                # 继续下一个关键词（每个关键词独立目标）
                if idx < len(keywords) - 1:
                    logger.info(f"继续下一个关键词...")
                    # 使用智能延迟策略
                    await self._smart_delay_between_keywords(len(keywords), idx)
            
            # 处理收集到的数据
            result = await self._process_and_export_data()

            # 修复完成率计算逻辑
            total_target = len(keywords) * self.target_count  # 总目标 = 关键词数量 × 单个关键词目标
            result['target_count'] = total_target
            result['actual_count'] = self.total_collected
            result['completion_rate'] = f"{(self.total_collected/total_target)*100:.1f}%"
            result['keywords_count'] = len(keywords)
            result['per_keyword_target'] = self.target_count

            logger.success(f"\n{'='*60}")
            logger.success(f"爬取完成！总目标: {total_target} ({len(keywords)}关键词 × {self.target_count}条), 实际: {self.total_collected}")
            logger.success(f"完成率: {result['completion_rate']}")
            logger.success(f"{'='*60}")
            
            return result
            
        except Exception as e:
            logger.error(f"爬取过程中出错: {type(e).__name__}: {e}")
            import traceback
            logger.debug(traceback.format_exc())
            raise
    
    async def _navigate_to_homepage(self, page) -> None:
        """导航到首页"""
        base_url = self.config.get("target", {}).get("base_url", "https://mobile.yangkeduo.com")
        
        logger.info("导航到拼多多首页...")
        await retry_async(page.goto, base_url, wait_until="networkidle")
        
        # 等待页面加载
        await asyncio.sleep(3)

    async def _cleanup_between_keywords(self, page) -> None:
        """优化的关键词间清理（保持API监听器）"""
        try:
            logger.info("🧹 开始关键词间清理...")
            
            # 1. 先等待确保之前的请求都完成
            await asyncio.sleep(2)
            
            # 2. 重置API监听器的关键词上下文
            if hasattr(self.api_monitor, 'reset_keyword_context') and self.current_keyword:
                self.api_monitor.reset_keyword_context(self.current_keyword)
                logger.debug("✅ API监听器关键词上下文已重置")
            
            # 3. 清理API监听器的缓存数据
            if hasattr(self.api_monitor, 'clear_keyword_data'):
                # 清理上一个关键词的数据
                if self.current_keyword_index > 0 and self.keywords:
                    prev_keyword = self.keywords[self.current_keyword_index - 1]
                    self.api_monitor.clear_keyword_data(prev_keyword)
                logger.debug("✅ API监听器缓存已清理")
            
            # 4. 重置滚动管理器
            if hasattr(self.scroll_manager, 'reset_for_new_keyword'):
                self.scroll_manager.reset_for_new_keyword()
                logger.debug("✅ 滚动管理器已重置")
            
            # 5. 清理去重集合（只保留当前关键词相关的）
            if self.current_keyword and self._collected_goods_ids:
                # 创建新的去重集合，只保留当前关键词的数据
                new_dedup_set = set()
                for dedup_key in self._collected_goods_ids:
                    if f"_{self.current_keyword}" in dedup_key:
                        new_dedup_set.add(dedup_key)
                
                old_size = len(self._collected_goods_ids)
                self._collected_goods_ids = new_dedup_set
                cleaned_count = old_size - len(self._collected_goods_ids)
                if cleaned_count > 0:
                    logger.debug(f"✅ 清理了 {cleaned_count} 个去重记录")
            
            # 6. 强制垃圾回收
            import gc
            collected = gc.collect()
            logger.debug(f"✅ 垃圾回收完成，清理了 {collected} 个对象")
            
            # 2. 不再导航到首页，而是清理当前页面状态
            # 清理本地存储，但保持在当前页面
            await page.evaluate("""
                () => {
                    try {
                        // 清理搜索相关的存储
                        const keysToRemove = [];
                        for (let i = 0; i < localStorage.length; i++) {
                            const key = localStorage.key(i);
                            if (key && (key.includes('search') || key.includes('keyword'))) {
                                keysToRemove.push(key);
                            }
                        }
                        keysToRemove.forEach(key => localStorage.removeItem(key));
                        
                        // 清理sessionStorage中的搜索数据
                        sessionStorage.clear();
                        
                        // 清理搜索输入框（如果存在）
                        const searchInputs = document.querySelectorAll('input[type="search"], input[placeholder*="搜索"]');
                        searchInputs.forEach(input => {
                            input.value = '';
                            input.dispatchEvent(new Event('input', { bubbles: true }));
                        });
                    } catch(e) {
                        console.error('清理存储时出错:', e);
                    }
                }
            """)
            logger.debug("✅ 页面状态已清理（保持在当前页面）")
            
            # 3. 短暂延迟，让页面稳定
            await asyncio.sleep(1)
            
            logger.info("✅ 清理完成（API监听器保持活跃）")
            
        except Exception as e:
            logger.warning(f"⚠️ 深度清理时出现问题: {e}")
            # 清理失败不应该中断爬取流程

    async def _smart_delay_between_keywords(self, total_keywords: int, current_index: int) -> None:
        """优化的关键词间延迟策略"""
        try:
            # 优化后的基础延迟：大幅缩短
            base_delay = min(8, 2 + total_keywords * 0.5)  # 从5+1.5x降低到2+0.5x

            # 简化的进度因子：减少延迟增长
            progress_factor = 1 + (current_index / total_keywords) * 0.2  # 从0.5降低到0.2

            # 缩小随机因子范围
            import random
            random_factor = random.uniform(0.9, 1.1)  # 从0.8-1.2缩小到0.9-1.1

            # 计算最终延迟时间
            final_delay = base_delay * progress_factor * random_factor
            final_delay = max(1.5, min(10, final_delay))  # 限制在1.5-10秒之间（原来3-45秒）

            logger.info(f"⏱️ 优化延迟 {final_delay:.1f} 秒 (基础:{base_delay:.1f}s, 进度因子:{progress_factor:.2f}, 随机因子:{random_factor:.2f})")

            await asyncio.sleep(final_delay)

        except Exception as e:
            logger.warning(f"⚠️ 延迟执行失败，使用最小延迟: {e}")
            await asyncio.sleep(1.5)  # 回退到最小延迟


    async def _crawl_keyword(self, page, keyword: str) -> None:
        """
        爬取单个关键词
        
        Args:
            page: 页面对象
            keyword: 搜索关键词
        """
        try:
            # 设置API监听
            await self.api_monitor.setup_monitoring(page, keyword)
            
            # 导航到搜索页面
            await self._navigate_to_search(page, keyword)

            # 使用简化的反检测延迟（基于MediaCrawler）
            await self.anti_detection.add_simple_delay()

            # 模拟用户浏览行为（轻微滚动）
            await self._simulate_user_browsing(page)

            # 再次使用简化延迟
            await self.anti_detection.add_simple_delay()

            # 检查风控（页面级别）
            is_risk, risk_details = await self.anti_detection.check_risk_control(page)
            if is_risk:
                logger.warning(f"检测到页面级风控: {risk_details}")
                success = await self.anti_detection.handle_risk_control(self.browser_manager, page)
                if not success:
                    logger.error("风控处理失败，停止当前关键词爬取")
                    return
                else:
                    logger.info("风控处理成功，继续爬取")

            # 滚动页面触发更多数据加载
            await self._scroll_and_load_data(page)

            # 滚动后再次检查是否有API级别的风控
            if self.anti_detection.risk_detected:
                logger.warning("检测到API级别风控，尝试处理...")
                success = await self.anti_detection.handle_risk_control(self.browser_manager, page)
                if success:
                    logger.info("API风控处理成功，重新尝试爬取")
                    # 重新滚动加载数据
                    await asyncio.sleep(random.uniform(2, 5))
                    await self._scroll_and_load_data(page)
                else:
                    logger.error("API风控处理失败，停止当前关键词爬取")
                    return
            
            logger.info(f"关键词 '{keyword}' 爬取完成")
            
        except asyncio.TimeoutError:
            logger.error(f"爬取关键词 '{keyword}' 超时")
        except Exception as e:
            logger.error(f"爬取关键词 '{keyword}' 时出错: {type(e).__name__}: {e}")

    async def _crawl_keyword_with_target(self, page, keyword: str, target: int) -> None:
        """
        爬取单个关键词，带目标数量控制
        
        Args:
            page: 页面对象
            keyword: 搜索关键词
            target: 目标收集数量
        """
        try:
            # 设置API监听
            await self.api_monitor.setup_monitoring(page, keyword)
            
            # 记录API监控器内存状态
            self._enhanced_memory_monitoring("api_monitor", f"监听设置: {keyword}")
            
            # 导航到搜索页面
            await self._navigate_to_search(page, keyword)

            # 使用简化的反检测延迟（基于MediaCrawler）
            await self.anti_detection.add_simple_delay()

            # 模拟用户浏览行为（轻微滚动）
            await self._simulate_user_browsing(page)

            # 再次使用简化延迟
            await self.anti_detection.add_simple_delay()

            # 检查风控（页面级别）
            is_risk, risk_details = await self.anti_detection.check_risk_control(page)
            if is_risk:
                logger.warning(f"检测到页面级风控: {risk_details}")
                success = await self.anti_detection.handle_risk_control(self.browser_manager, page)
                if not success:
                    logger.error("风控处理失败，停止当前关键词爬取")
                    return
                else:
                    logger.info("风控处理成功，继续爬取")

            # 记录开始时的数据量
            start_count = len(self.collected_data)
            
            # 智能滚动页面，传递目标数量和目标检查回调
            self._enhanced_memory_monitoring("scroll_manager", f"开始滚动: {keyword}")
            
            collected_count = await self.scroll_manager.smart_scroll(
                page, 
                self._get_data_count, 
                target,
                self._is_keyword_target_reached
            )
            
            self._enhanced_memory_monitoring("scroll_manager", f"滚动完成: {keyword}")
            
            # 实际收集的数量
            actual_collected = len(self.collected_data) - start_count
            
            # 滚动后再次检查是否有API级别的风控
            if self.anti_detection.risk_detected:
                logger.warning("检测到API级别风控，尝试处理...")
                success = await self.anti_detection.handle_risk_control(self.browser_manager, page)
                if success:
                    logger.info("API风控处理成功")
                else:
                    logger.error("API风控处理失败")
            
            logger.info(f"关键词 '{keyword}' 实际收集: {actual_collected} 个商品")
            
        except asyncio.TimeoutError:
            logger.error(f"爬取关键词 '{keyword}' 超时")
        except Exception as e:
            logger.error(f"爬取关键词 '{keyword}' 时出错: {type(e).__name__}: {e}")
    
    async def _navigate_to_search(self, page, keyword: str) -> None:
        """导航到搜索页面（优化版）"""
        import urllib.parse

        # URL编码关键词，确保中文字符正确处理
        encoded_keyword = urllib.parse.quote(keyword, safe='')
        base_url = self.config.get('target', {}).get('base_url')

        # 检查当前是否已经在搜索页面
        current_url = page.url
        if "search_result.html" in current_url or "search" in current_url:
            logger.info(f"已在搜索页面，直接更新搜索关键词: {keyword}")
            
            # 尝试通过JavaScript更新搜索框并触发搜索
            search_success = await page.evaluate("""
                (keyword) => {
                    try {
                        // 查找搜索输入框
                        const searchInputs = document.querySelectorAll('input[type="search"], input[placeholder*="搜索"], input[class*="search"]');
                        if (searchInputs.length > 0) {
                            const input = searchInputs[0];
                            input.value = keyword;
                            input.focus();
                            
                            // 触发输入事件
                            input.dispatchEvent(new Event('input', { bubbles: true }));
                            input.dispatchEvent(new Event('change', { bubbles: true }));
                            
                            // 查找搜索按钮并点击
                            const searchBtns = document.querySelectorAll('button[class*="search"], div[class*="search-btn"]');
                            if (searchBtns.length > 0) {
                                searchBtns[0].click();
                                return true;
                            }
                            
                            // 如果没有找到按钮，尝试按回车
                            input.dispatchEvent(new KeyboardEvent('keypress', { key: 'Enter', keyCode: 13, bubbles: true }));
                            return true;
                        }
                        return false;
                    } catch(e) {
                        console.error('更新搜索框失败:', e);
                        return false;
                    }
                }
            """, keyword)
            
            if search_success:
                logger.info("✅ 触发了搜索，等待页面跳转...")
                
                # 等待页面导航完成
                try:
                    # 等待URL变化或新内容加载
                    await page.wait_for_load_state("networkidle", timeout=10000)
                    await asyncio.sleep(2)  # 额外等待以确保API响应
                    
                    # 验证搜索是否成功
                    new_url = page.url
                    if encoded_keyword in new_url or keyword in new_url:
                        logger.info("✅ 通过搜索框成功更新了关键词")
                        return
                    else:
                        logger.warning("⚠️ 搜索后URL未更新，将使用直接导航")
                except Exception as e:
                    logger.warning(f"⚠️ 等待搜索跳转失败: {e}，将使用直接导航")

        # 如果不在搜索页面或更新失败，使用直接URL导航
        search_url = f"{base_url}/search_result.html?search_key={encoded_keyword}"
        logger.info(f"导航到搜索URL: {search_url}")

        try:
            await retry_async(page.goto, search_url, wait_until="networkidle")
        except Exception as e:
            logger.error(f"搜索页面导航失败: {e}")
            raise
        
        # 等待搜索结果加载
        await asyncio.sleep(2)

        # 验证是否到达了正确的搜索页面
        current_url = page.url
        logger.info(f"当前页面URL: {current_url}")

        # 检查URL是否包含搜索关键词
        if encoded_keyword not in current_url and keyword not in current_url:
            logger.warning(f"页面URL不包含搜索关键词，可能被重定向了")

    async def _simulate_user_browsing(self, page) -> None:
        """模拟真实用户浏览行为"""
        try:
            # 模拟轻微滚动（查看页面内容）
            await page.evaluate("window.scrollBy(0, 200)")
            await asyncio.sleep(random.uniform(0.5, 1.5))

            # 模拟回到顶部
            await page.evaluate("window.scrollTo(0, 0)")
            await asyncio.sleep(random.uniform(0.3, 0.8))

            # 模拟鼠标移动（不点击）
            viewport = page.viewport_size
            if viewport:
                x = random.randint(100, viewport["width"] - 100)
                y = random.randint(100, viewport["height"] - 100)
                await page.mouse.move(x, y)
                await asyncio.sleep(random.uniform(0.2, 0.5))

            logger.debug("已模拟用户浏览行为")

        except Exception as e:
            logger.debug(f"模拟用户浏览行为时出错: {e}")


    async def _scroll_and_load_data(self, page) -> int:
        """智能滚动页面并加载数据"""
        logger.info("开始智能滚动加载数据")
        
        # 使用智能滚动管理器，传递目标数量和目标检查回调
        collected_count = await self.scroll_manager.smart_scroll(
            page, 
            self._get_data_count, 
            self.target_count,
            self._is_keyword_target_reached
        )
        
        logger.info(f"智能滚动完成，收集到 {collected_count} 条新数据")
        return collected_count

    async def crawl_with_sorting(self, keywords: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        支持排序的爬取方法
        
        Args:
            keywords: 可选的关键词列表
            
        Returns:
            Dict[str, Any]: 爬取结果摘要
        """
        try:
            self.is_running = True
            keywords = keywords or self.keywords
            
            if not keywords:
                raise ValueError("未提供搜索关键词")
            
            logger.info(f"开始支持排序的爬取，关键词: {keywords}")
            
            # 启动浏览器
            async with self.browser_manager as browser_mgr:
                await browser_mgr.create_context()
                page = await browser_mgr.create_page()
                
                # 设置API监听回调
                self.api_monitor.set_data_callback(self._on_data_received)
                
                # 启动反风控监控
                await self.anti_detection.monitor_requests(page)
                
                # 检查登录状态
                await self._navigate_to_homepage(page)
                await self.login_detector.ensure_logged_in(page)
                
                # 逐个关键词爬取
                for keyword in keywords:
                    if not self.is_running:
                        break
                    
                    logger.info(f"开始爬取关键词: {keyword}")
                    await self._crawl_keyword_with_sorting(page, keyword)
                    
                    # 关键词间的延迟
                    await self.anti_detection.add_simple_delay()
            
            # 处理收集到的数据
            result = await self._process_and_export_data_with_sorting()
            
            logger.info("支持排序的爬取完成")
            return result
            
        except asyncio.CancelledError:
            logger.info("爬取任务被取消")
            raise
        except KeyboardInterrupt:
            logger.info("用户中断爬取")
            raise
        except Exception as e:
            logger.error(f"爬取过程中出错: {type(e).__name__}: {e}")
            # 记录详细错误信息
            import traceback
            logger.debug(traceback.format_exc())
            raise
        finally:
            self.is_running = False
    
    async def _crawl_keyword_with_sorting(self, page, keyword: str) -> None:
        """
        爬取指定关键词的多种排序数据
        
        Args:
            page: 页面对象
            keyword: 搜索关键词
        """
        try:
            # 导航到搜索页面
            await self._navigate_to_search(page, keyword)
            
            # 等待页面加载
            await asyncio.sleep(3)
            
            # 检查风控
            is_risk, risk_details = await self.anti_detection.check_risk_control(page)
            if is_risk:
                logger.warning(f"检测到风控: {risk_details}")
                await self.anti_detection.handle_risk_control(self.browser_manager, page)
                return
            
            # 获取可用的排序选项
            if self.sort_manager.is_enabled():
                available_sorts = await self.sort_manager.get_available_sorts(page)
                if not available_sorts:
                    # 如果没有找到排序选项，使用默认排序
                    default_sort = self.sort_manager.get_default_sort()
                    if default_sort:
                        available_sorts = [default_sort]
                
                logger.info(f"关键词 '{keyword}' 可用排序: {[self.sort_manager.get_sort_name(s) for s in available_sorts]}")
            else:
                available_sorts = ["default"]
            
            # 遍历每种排序方式
            for sort_type in available_sorts:
                if not self.is_running:
                    break
                
                logger.info(f"开始爬取排序: {self.sort_manager.get_sort_name(sort_type)}")
                
                # 应用排序
                if sort_type != "default" and self.sort_manager.is_enabled():
                    sort_success = await self.sort_manager.apply_sort(page, sort_type)
                    if not sort_success:
                        logger.warning(f"排序应用失败，跳过: {sort_type}")
                        continue
                
                # 设置API监听，标记当前排序
                await self.api_monitor.setup_monitoring(page, f"{keyword}_{sort_type}")
                
                # 重置滚动策略
                self.scroll_manager.reset_strategy()
                
                # 智能滚动收集数据
                collected_count = await self._scroll_and_load_data(page)
                
                logger.info(f"排序 '{self.sort_manager.get_sort_name(sort_type)}' 收集到 {collected_count} 条数据")
                
                # 排序间的延迟
                await self.anti_detection.add_simple_delay()
            
            logger.info(f"关键词 '{keyword}' 所有排序爬取完成")
            
        except asyncio.TimeoutError:
            logger.error(f"爬取关键词 '{keyword}' 超时")
        except Exception as e:
            logger.error(f"爬取关键词 '{keyword}' 时出错: {type(e).__name__}: {e}")
    
    def _get_data_count(self) -> int:
        """获取当前关键词收集的数据数量"""
        # 只计算当前关键词的数据数量
        if self.current_keyword:
            return sum(1 for item in self.collected_data if item.get('keyword') == self.current_keyword)
        return len(self.collected_data)
    
    def _is_keyword_target_reached(self) -> bool:
        """检查当前关键词是否已达到目标数量"""
        return self._keyword_target_reached
    
    async def _on_data_received(self, goods_list: List[Dict[str, Any]]) -> None:
        """
        API数据接收回调（增强版：包含去重、目标数量控制和数据质量检查）

        Args:
            goods_list: 接收到的商品数据列表
        """
        logger.debug(f"_on_data_received 被调用，收到 {len(goods_list) if goods_list else 0} 条数据")
        
        if not goods_list:
            return

        async with self._data_lock:  # 使用锁确保线程安全
            try:
                # 检查当前关键词是否已达到目标（每个关键词独立目标）
                keyword_data_count = sum(1 for item in self.collected_data if item.get('keyword') == self.current_keyword)
                if keyword_data_count >= self.target_count:
                    logger.info(f"关键词 '{self.current_keyword}' 已达到目标数量 {self.target_count}，忽略新数据")
                    # 设置目标达成标志，通知滚动管理器停止
                    self._keyword_target_reached = True
                    return

                # 计算当前关键词还需要多少数据
                # 为了确保收集到补贴商品，我们收集更多数据然后在处理阶段筛选
                collection_multiplier = 5  # 收集目标数量的5倍数据
                remaining = max(self.target_count * collection_multiplier - keyword_data_count, self.target_count - keyword_data_count)

                new_goods = []
                duplicate_count = 0
                invalid_count = 0

                for goods in goods_list:
                    # 如果已经收集够了，停止
                    if len(new_goods) >= remaining:
                        break

                    # 数据质量检查
                    if not self._validate_goods_data(goods):
                        invalid_count += 1
                        continue

                    goods_id = goods.get("goods_id")
                    # 使用商品ID和关键词组合作为去重键
                    # 这样同一商品可以在不同关键词下存在
                    dedup_key = f"{goods_id}_{self.current_keyword}"
                    
                    if goods_id and dedup_key not in self._collected_goods_ids:
                        # 添加数据标记
                        goods['keyword'] = self.current_keyword
                        goods['collected_time'] = datetime.datetime.now().isoformat()
                        goods['collection_index'] = len(self.collected_data) + len(new_goods)

                        # 添加到去重集合和新数据列表
                        self._collected_goods_ids.add(dedup_key)
                        new_goods.append(goods)
                    else:
                        duplicate_count += 1

                # 批量添加新数据
                if new_goods:
                    self.collected_data.extend(new_goods)
                    current_keyword_count = keyword_data_count + len(new_goods)

                    logger.info(f"📦 接收数据 - 总数: {len(goods_list)}, 新增: {len(new_goods)}, "
                               f"重复: {duplicate_count}, 无效: {invalid_count}")
                    logger.info(f"🎯 关键词 '{self.current_keyword}' 进度: {current_keyword_count}/{self.target_count} "
                               f"({(current_keyword_count/self.target_count)*100:.1f}%)")

                    # 检查当前关键词是否达到目标
                    if current_keyword_count >= self.target_count:
                        logger.success(f"🎉 关键词 '{self.current_keyword}' 已达到目标数量 {self.target_count}！")
                        # 设置目标达成标志，通知滚动管理器停止
                        self._keyword_target_reached = True

                    # 更新总计数
                    self.total_collected = len(self.collected_data)
                else:
                    logger.debug(f"📦 接收到 {len(goods_list)} 条数据，无新增数据 "
                                f"(重复: {duplicate_count}, 无效: {invalid_count})")

            except Exception as e:
                logger.error(f"❌ 数据接收处理失败: {e}")
                import traceback
                logger.debug(traceback.format_exc())

    def _validate_goods_data(self, goods: Dict[str, Any]) -> bool:
        """验证商品数据质量"""
        try:
            # 必要字段检查
            required_fields = ["goods_id", "goods_name"]
            for field in required_fields:
                if not goods.get(field):
                    return False

            # 数据格式检查
            goods_id = goods.get("goods_id")
            if not str(goods_id).strip():
                return False

            goods_name = goods.get("goods_name", "").strip()
            if len(goods_name) < 2:  # 商品名称太短
                return False

            return True

        except Exception:
            return False
    
    async def _process_and_export_data(self, format: str = "xlsx") -> Dict[str, Any]:
        """处理和导出数据
        
        Args:
            format: 导出格式，支持 'xlsx' 和 'csv'
        """
        if not self.collected_data:
            logger.warning("未收集到任何数据")
            return {
                "success": False,
                "message": "未收集到任何数据",
                "total_goods": 0
            }
        
        logger.info(f"开始处理 {len(self.collected_data)} 条原始数据")
        
        # 统计原始数据的关键词分布
        keyword_stats = {}
        for item in self.collected_data:
            keyword = item.get('keyword', '未知')
            keyword_stats[keyword] = keyword_stats.get(keyword, 0) + 1
        logger.info(f"原始数据关键词分布: {keyword_stats}")
        
        # 按关键词分组进行处理（支持精确筛选）
        all_processed_data = []
        keyword_processing_stats = {}
        
        # 先按关键词分组原始数据
        raw_grouped_data = {}
        for item in self.collected_data:
            keyword = item.get('keyword', '未知')
            if keyword not in raw_grouped_data:
                raw_grouped_data[keyword] = []
            raw_grouped_data[keyword].append(item)
        
        # 对每个关键词的数据进行处理
        for keyword, keyword_data in raw_grouped_data.items():
            logger.info(f"处理关键词 '{keyword}' 的 {len(keyword_data)} 条数据")
            
            # 数据处理（包含精确筛选）
            processed_keyword_data = await self.data_processor.process_goods_data(
                keyword_data, 
                search_keyword=keyword
            )
            
            all_processed_data.extend(processed_keyword_data)
            keyword_processing_stats[keyword] = {
                'raw_count': len(keyword_data),
                'processed_count': len(processed_keyword_data),
                'filter_rate': 1 - (len(processed_keyword_data) / len(keyword_data)) if len(keyword_data) > 0 else 0
            }
        
        if not all_processed_data:
            logger.warning("数据处理后无有效数据")
            return {
                "success": False,
                "message": "数据处理后无有效数据",
                "total_goods": 0
            }
        
        logger.info(f"数据处理后剩余 {len(all_processed_data)} 条有效数据")
        
        # 记录处理统计
        for keyword, stats in keyword_processing_stats.items():
            logger.info(f"关键词 '{keyword}': {stats['raw_count']} -> {stats['processed_count']} "
                       f"(筛选率: {stats['filter_rate']:.1%})")
        
        # 统计处理后的关键词分布
        processed_keyword_stats = {}
        for item in all_processed_data:
            keyword = item.get('keyword', '未知')
            processed_keyword_stats[keyword] = processed_keyword_stats.get(keyword, 0) + 1
        logger.info(f"处理后数据关键词分布: {processed_keyword_stats}")
        
        # 按关键词分组
        grouped_data = await self.data_processor.group_data_by_keyword(all_processed_data)
        
        # 根据格式导出数据
        if format == "csv":
            export_file = await self.excel_exporter.export_to_csv(grouped_data)
        else:  # 默认xlsx
            export_file = await self.excel_exporter.export_data(grouped_data)
        
        # 生成摘要
        summary = self.excel_exporter.get_export_summary(export_file, grouped_data)
        summary['file_path'] = export_file
        summary['format'] = format
        summary.update({
            "success": True,
            "message": f"爬取和导出完成 ({format.upper()})",
            "raw_data_count": len(self.collected_data),
            "processed_data_count": len(all_processed_data),
            "keyword_processing_stats": keyword_processing_stats
        })
        
        # 数据统计
        stats = self.data_processor.get_data_statistics(all_processed_data)
        summary["statistics"] = stats
        
        return summary

    async def _process_and_export_data_with_sorting(self) -> Dict[str, Any]:
        """处理和导出支持排序的数据"""
        if not self.collected_data:
            logger.warning("未收集到任何数据")
            return {
                "success": False,
                "message": "未收集到任何数据",
                "total_goods": 0
            }
        
        logger.info(f"开始处理 {len(self.collected_data)} 条原始数据（支持排序）")
        
        # 数据处理
        processed_data = await self.data_processor.process_goods_data(self.collected_data)
        
        if not processed_data:
            logger.warning("数据处理后无有效数据")
            return {
                "success": False,
                "message": "数据处理后无有效数据",
                "total_goods": 0
            }
        
        # 按关键词和排序分组
        grouped_data = await self.data_processor.group_data_by_keyword_and_sort(processed_data)
        
        # 导出Excel（支持排序）
        export_file = await self.excel_exporter.export_data_with_sorting(grouped_data)
        
        # 生成摘要
        summary = self._get_export_summary_with_sorting(export_file, grouped_data)
        summary.update({
            "success": True,
            "message": "支持排序的爬取和导出完成",
            "raw_data_count": len(self.collected_data),
            "processed_data_count": len(processed_data)
        })
        
        # 数据统计
        stats = self.data_processor.get_data_statistics(processed_data)
        summary["statistics"] = stats
        
        return summary
    
    def _get_export_summary_with_sorting(self, file_path: str, grouped_data: Dict[str, Dict[str, List]]) -> Dict[str, Any]:
        """
        获取支持排序的导出摘要信息
        
        Args:
            file_path: 导出文件路径
            grouped_data: 按关键词和排序分组的数据
            
        Returns:
            Dict[str, Any]: 导出摘要
        """
        total_goods = 0
        keywords = list(grouped_data.keys())
        sort_summary = {}
        
        for keyword, sort_data in grouped_data.items():
            sort_summary[keyword] = {}
            for sort_type, goods_list in sort_data.items():
                count = len(goods_list)
                total_goods += count
                sort_name = self.sort_manager.get_sort_name(sort_type) if hasattr(self, 'sort_manager') else sort_type
                sort_summary[keyword][sort_name] = count
        
        summary = {
            "file_path": file_path,
            "total_keywords": len(keywords),
            "total_goods": total_goods,
            "keywords": keywords,
            "sort_summary": sort_summary,
            "export_time": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        # 文件大小
        try:
            file_size = Path(file_path).stat().st_size
            summary["file_size"] = file_size
            summary["file_size_mb"] = round(file_size / 1024 / 1024, 2)
        except Exception as e:
            logger.debug(f"获取文件大小失败: {e}")
        
        return summary
    
    async def start_browser_session(self) -> None:
        """启动浏览器会话（用于多关键词连续处理）"""
        if not self.browser_manager.context:
            await self.browser_manager.start()
            await self.browser_manager.create_context()
    
    async def close_browser_session(self) -> None:
        """关闭浏览器会话"""
        await self.browser_manager.close()
    
    def stop_crawling(self) -> None:
        """停止爬取"""
        logger.info("收到停止信号，正在停止爬虫...")
        self.is_running = False
        
        # 执行优雅关闭
        try:
            shutdown_result = self.resource_cleaner.graceful_shutdown()
            if shutdown_result.get("cleanup_performed"):
                logger.info(f"🧹 优雅关闭完成，释放空间: {shutdown_result.get('total_freed_mb', 0):.2f}MB")
        except Exception as e:
            logger.debug(f"优雅关闭失败: {e}")
    
    def get_progress(self) -> Dict[str, Any]:
        """获取爬取进度"""
        return {
            "is_running": self.is_running,
            "collected_count": len(self.collected_data),
            "monitored_count": self.api_monitor.get_data_count(),
            "risk_status": self.anti_detection.get_risk_status()
        }

    def show_progress_bar(self, current: int, total: int, keyword: str = "") -> None:
        """
        显示进度条
        
        Args:
            current: 当前数量
            total: 总目标数量
            keyword: 当前关键词
        """
        if total <= 0:
            return
            
        # 计算进度
        progress = min(current / total, 1.0)
        bar_length = 50
        filled_length = int(bar_length * progress)
        
        # 创建进度条
        bar = '█' * filled_length + '░' * (bar_length - filled_length)
        percent = progress * 100
        
        # 构建进度信息
        status = f"[{bar}] {percent:.1f}% - {current}/{total}"
        if keyword:
            status = f"{keyword}: {status}"
        
        # 使用logger显示（避免覆盖）
        logger.info(f"进度: {status}")
    
    def get_cleanup_stats(self) -> Dict[str, Any]:
        """获取资源清理统计信息"""
        try:
            return self.resource_cleaner.get_cleanup_stats()
        except Exception as e:
            logger.error(f"获取清理统计失败: {e}")
            return {"error": str(e)}
    
    def manual_cleanup(self, cleanup_types: Optional[List[str]] = None) -> Dict[str, Any]:
        """手动执行资源清理"""
        try:
            logger.info("开始手动执行资源清理...")
            result = self.resource_cleaner.perform_cleanup(cleanup_types)
            if result.get("success"):
                logger.success(f"🧹 手动清理完成，释放空间: {result.get('total_freed_mb', 0):.2f}MB")
            else:
                logger.error(f"手动清理失败: {result.get('error', '未知错误')}")
            return result
        except Exception as e:
            logger.error(f"手动清理异常: {e}")
            return {"success": False, "error": str(e)}
    
    def emergency_cleanup(self) -> Dict[str, Any]:
        """紧急资源清理"""
        try:
            logger.warning("🚨 开始紧急资源清理...")
            result = self.resource_cleaner.emergency_cleanup()
            if result.get("success"):
                logger.success(f"🚨 紧急清理完成，释放空间: {result.get('total_freed_mb', 0):.2f}MB")
            else:
                logger.error(f"紧急清理失败: {result.get('error', '未知错误')}")
            return result
        except Exception as e:
            logger.error(f"紧急清理异常: {e}")
            return {"success": False, "error": str(e)}


async def main():
    """主函数"""
    import argparse
    
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='拼多多商品爬虫')
    parser.add_argument('--keyword', type=str, help='搜索关键词（会覆盖配置文件中的关键词）')
    parser.add_argument('--max_products', type=int, help='目标商品数量（会覆盖配置文件中的目标数量）')
    parser.add_argument('--update-cookie', type=str, help='更新Cookie（格式：name1=value1; name2=value2）')
    parser.add_argument('--cookie-file', type=str, help='从文件更新Cookie')
    args = parser.parse_args()
    
    # 处理Cookie更新
    if args.update_cookie or args.cookie_file:
        from src.core.cookie_manager import CookieManager
        cookie_manager = CookieManager()
        
        try:
            if args.cookie_file:
                # 从文件更新Cookie
                logger.info(f"从文件更新Cookie: {args.cookie_file}")
                success = cookie_manager.update_cookie_from_file(args.cookie_file)
            else:
                # 从命令行字符串更新Cookie
                logger.info("从命令行更新Cookie")
                success = await cookie_manager.import_cookies_from_string(args.update_cookie)
            
            if success:
                logger.success("Cookie更新成功！")
            else:
                logger.error("Cookie更新失败")
                return
        except Exception as e:
            logger.error(f"更新Cookie时出错: {e}")
            return
    
    # 设置信号处理
    crawler = PDDCrawler()
    
    # 如果提供了命令行参数，覆盖配置
    if args.keyword:
        # 支持逗号分隔的多个关键词
        crawler.keywords = [k.strip() for k in args.keyword.split(',') if k.strip()]
        logger.info(f"使用命令行指定的关键词: {crawler.keywords}")
    
    if args.max_products:
        crawler.target_count = args.max_products
        logger.info(f"使用命令行指定的目标数量: {args.max_products}")
    
    def signal_handler(signum, frame):
        logger.info(f"收到信号 {signum}，正在停止...")
        crawler.stop_crawling()
        # 确保资源清理器也停止
        try:
            crawler.resource_cleaner.stop_auto_cleanup()
        except Exception as e:
            logger.debug(f"停止自动清理失败: {e}")
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # 根据配置自动选择爬取模式
        if crawler.sort_manager.is_enabled():
            logger.info("🎯 检测到排序功能已启用，使用多排序爬取模式")
            logger.info("⏰ 注意：多排序模式会显著增加爬取时间")
            result = await crawler.crawl_with_sorting()
        else:
            logger.info("🔥 使用标准爬取模式（推荐，速度更快）")
            logger.info("💡 如需启用排序功能，请编辑 config/settings.yaml 中的 sorting.enabled")
            result = await crawler.start_crawling()
        
        # 打印结果
        logger.success("=" * 50)
        logger.success("爬取完成！")
        logger.success(f"成功: {result.get('success', False)}")
        logger.success(f"消息: {result.get('message', '')}")
        logger.success(f"总商品数: {result.get('total_goods', 0)}")
        logger.success(f"导出文件: {result.get('file_path', '')}")
        
        # 显示排序摘要（如果有）
        if result.get('sort_summary'):
            logger.success("排序数据摘要:")
            for keyword, sort_data in result['sort_summary'].items():
                logger.success(f"  关键词 '{keyword}':")
                for sort_name, count in sort_data.items():
                    logger.success(f"    {sort_name}: {count} 条")
        
        if result.get('statistics'):
            stats = result['statistics']
            logger.success(f"关键词数: {stats.get('keyword_count', 0)}")
            logger.success(f"店铺数: {stats.get('shop_count', 0)}")
            if 'price_stats' in stats:
                price_stats = stats['price_stats']
                logger.success(f"价格范围: {price_stats['min']:.2f} - {price_stats['max']:.2f} 元")
        
        logger.success("=" * 50)
        
    except KeyboardInterrupt:
        logger.info("用户中断爬取")
    except Exception as e:
        logger.error(f"爬取失败: {e}")
        sys.exit(1)
    finally:
        # 最终优雅关闭
        try:
            logger.info("程序结束，执行最终清理...")
            final_cleanup = crawler.resource_cleaner.graceful_shutdown()
            if final_cleanup.get("cleanup_performed"):
                logger.info(f"🧹 最终清理完成，释放空间: {final_cleanup.get('total_freed_mb', 0):.2f}MB")
        except Exception as e:
            logger.debug(f"最终清理失败: {e}")


if __name__ == "__main__":
    asyncio.run(main())
