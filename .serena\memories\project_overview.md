# 拼多多爬虫项目概览

## 项目简介
这是一个基于Python的拼多多商品数据爬虫系统，版本2.0.0，支持Windows + WSL双环境运行。

## 技术栈
- **Python**: >=3.8
- **浏览器自动化**: Playwright + playwright-stealth
- **数据处理**: pandas, orjson, openpyxl
- **文本匹配**: rapidfuzz (高性能模糊匹配)
- **配置管理**: PyYAML
- **日志系统**: loguru
- **重试机制**: tenacity

## 核心功能
1. **29个数据字段提取**: 支持商品ID、名称、价格、品牌、销量、评分等全面信息
2. **智能反检测**: 基于MediaCrawler的反风控机制
3. **多关键词支持**: 支持批量关键词爬取，每个关键词独立目标数量
4. **精确商品筛选**: 基于统计分析的智能商品过滤系统
5. **内存监控**: 实时监控内存使用，自动垃圾回收
6. **资源清理**: 自动清理临时文件和缓存

## 项目结构
```
pdd2/
├── run_main.py              # 主启动脚本
├── src/
│   ├── main.py             # 主程序入口
│   ├── core/               # 核心模块
│   │   ├── browser_manager.py
│   │   ├── api_response_monitor.py
│   │   ├── scroll_manager.py
│   │   └── anti_detection_simple.py
│   ├── data/               # 数据处理
│   │   └── processor.py
│   ├── filters/            # 商品筛选器
│   │   ├── product_filter.py
│   │   └── statistical_product_filter.py
│   └── utils/              # 工具模块
├── config/
│   └── settings.yaml       # 主配置文件
├── requirements.txt        # 依赖配置
└── output/                 # 输出目录
```