import React from 'react';
import {
  Card,
  Statistic,
  Progress,
  Typography,
  Space,
  Row,
  Col,
  Timeline,
  Tag,
  Empty
} from 'antd';
import {
  BarChartOutlined,
  ClockCircleOutlined,
  SearchOutlined,
  TrophyOutlined,
  HistoryOutlined
} from '@ant-design/icons';
// import type { SearchStats as SearchStatsType } from './types';
import { estimateSearchMetrics } from './utils';

const { Text, Title } = Typography;

interface SearchStatsProps {
  keywords: string[];
  config: {
    targetCount: number;
    maxPages: number;
    sortMethod: string;
  };
  disabled?: boolean;
}

const SearchStats: React.FC<SearchStatsProps> = ({
  keywords,
  config
}) => {
  // 计算预估指标
  const metrics = estimateSearchMetrics(keywords, {
    keywords,
    targetCount: config.targetCount,
    sortMethod: config.sortMethod as any,
    maxPages: config.maxPages,
    headless: true,
    enableFilter: false
  });
  
  // 模拟历史搜索记录（实际项目中应从localStorage或API获取）
  const getHistoryData = () => {
    try {
      const history = localStorage.getItem('pdd_search_history');
      return history ? JSON.parse(history).slice(0, 5) : [];
    } catch {
      return [];
    }
  };

  const historyData = getHistoryData();

  // 计算效率评分
  const getEfficiencyScore = () => {
    let score = 100;
    
    // 关键词数量影响
    if (keywords.length > 20) score -= 10;
    if (keywords.length > 50) score -= 20;
    
    // 目标数量影响
    if (config.targetCount > 500) score -= 15;
    if (config.targetCount > 1000) score -= 25;
    
    // 页数影响
    if (config.maxPages > 20) score -= 10;
    if (config.maxPages > 30) score -= 20;
    
    return Math.max(score, 0);
  };

  const efficiencyScore = getEfficiencyScore();

  // 获取评分颜色
  const getScoreColor = (score: number) => {
    if (score >= 80) return '#52c41a';
    if (score >= 60) return '#faad14';
    return '#f5222d';
  };

  // 获取时间评级
  const getTimeRating = (minutes: number) => {
    if (minutes <= 5) return { level: '很快', color: 'green' };
    if (minutes <= 15) return { level: '较快', color: 'blue' };
    if (minutes <= 30) return { level: '一般', color: 'orange' };
    return { level: '较慢', color: 'red' };
  };

  const timeRating = getTimeRating(metrics.estimatedTime);

  return (
    <Card
      title={
        <Space>
          <BarChartOutlined />
          <span>搜索统计</span>
        </Space>
      }
      size="small"
    >
      {keywords.length === 0 ? (
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description="请先添加关键词"
          style={{ margin: '20px 0' }}
        />
      ) : (
        <>
          {/* 基础统计 */}
          <Row gutter={16} style={{ marginBottom: 24 }}>
            <Col xs={12} sm={6}>
              <Statistic
                title="关键词数量"
                value={keywords.length}
                prefix={<SearchOutlined />}
                suffix="个"
                valueStyle={{ color: '#1890ff' }}
              />
            </Col>
            <Col xs={12} sm={6}>
              <Statistic
                title="预估结果"
                value={metrics.estimatedResults}
                suffix="个"
                valueStyle={{ color: '#52c41a' }}
              />
            </Col>
            <Col xs={12} sm={6}>
              <Statistic
                title="预估耗时"
                value={metrics.estimatedTime}
                suffix="分钟"
                prefix={<ClockCircleOutlined />}
                valueStyle={{ color: timeRating.color }}
              />
            </Col>
            <Col xs={12} sm={6}>
              <div>
                <Text type="secondary" style={{ fontSize: '14px' }}>
                  效率评分
                </Text>
                <div style={{ marginTop: 4 }}>
                  <Text
                    style={{
                      fontSize: '24px',
                      fontWeight: 'bold',
                      color: getScoreColor(efficiencyScore)
                    }}
                  >
                    {efficiencyScore}
                  </Text>
                  <Text type="secondary" style={{ marginLeft: 4 }}>
                    /100
                  </Text>
                </div>
              </div>
            </Col>
          </Row>

          {/* 效率分析 */}
          <div style={{ marginBottom: 24 }}>
            <Title level={5} style={{ marginBottom: 12 }}>
              <TrophyOutlined style={{ marginRight: 8 }} />
              效率分析
            </Title>
            <Row gutter={16}>
              <Col xs={24} sm={12}>
                <div style={{ marginBottom: 16 }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
                    <Text>配置效率</Text>
                    <Tag color={getScoreColor(efficiencyScore)}>
                      {efficiencyScore >= 80 ? '优秀' : 
                       efficiencyScore >= 60 ? '良好' : '需优化'}
                    </Tag>
                  </div>
                  <Progress
                    percent={efficiencyScore}
                    strokeColor={getScoreColor(efficiencyScore)}
                    size="small"
                    showInfo={false}
                  />
                </div>
              </Col>
              <Col xs={24} sm={12}>
                <div style={{ marginBottom: 16 }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
                    <Text>预估速度</Text>
                    <Tag color={timeRating.color}>
                      {timeRating.level}
                    </Tag>
                  </div>
                  <Progress
                    percent={Math.max(100 - metrics.estimatedTime * 2, 10)}
                    strokeColor={timeRating.color}
                    size="small"
                    showInfo={false}
                  />
                </div>
              </Col>
            </Row>
          </div>

          {/* 配置建议 */}
          <div style={{ marginBottom: 24 }}>
            <Title level={5} style={{ marginBottom: 12 }}>
              优化建议
            </Title>
            <div style={{ fontSize: '12px', lineHeight: 1.6 }}>
              {keywords.length > 50 && (
                <div style={{ color: '#faad14', marginBottom: 4 }}>
                  • 关键词数量较多({keywords.length}个)，建议分批次执行
                </div>
              )}
              {config.targetCount > 500 && (
                <div style={{ color: '#faad14', marginBottom: 4 }}>
                  • 目标数量较高({config.targetCount}个)，可能影响采集速度
                </div>
              )}
              {config.maxPages > 20 && (
                <div style={{ color: '#faad14', marginBottom: 4 }}>
                  • 页数设置较高({config.maxPages}页)，可能获取过多低质量结果
                </div>
              )}
              {efficiencyScore >= 80 && (
                <div style={{ color: '#52c41a', marginBottom: 4 }}>
                  • 当前配置效率良好，可以开始搜索
                </div>
              )}
              {metrics.estimatedTime > 30 && (
                <div style={{ color: '#f5222d', marginBottom: 4 }}>
                  • 预估时间较长，建议减少关键词数量或目标数量
                </div>
              )}
            </div>
          </div>

          {/* 历史记录 */}
          {historyData.length > 0 && (
            <div>
              <Title level={5} style={{ marginBottom: 12 }}>
                <HistoryOutlined style={{ marginRight: 8 }} />
                最近搜索
              </Title>
              <Timeline style={{ fontSize: '12px' }}>
                {historyData.map((item: any, index: number) => (
                  <Timeline.Item key={index}>
                    <div style={{ fontSize: '12px' }}>
                      <div style={{ marginBottom: 4 }}>
                        <Text strong>
                          {item.keywords?.slice(0, 3).join(', ')}
                          {item.keywords?.length > 3 && '...'}
                        </Text>
                        <Text type="secondary" style={{ marginLeft: 8 }}>
                          ({item.resultCount || 0}个结果)
                        </Text>
                      </div>
                      <Text type="secondary">
                        {new Date(item.timestamp).toLocaleString()}
                      </Text>
                    </div>
                  </Timeline.Item>
                ))}
              </Timeline>
            </div>
          )}

          {/* 详细预估 */}
          <div style={{ 
            marginTop: 24, 
            padding: 12, 
            backgroundColor: '#f6f8fa', 
            borderRadius: 6 
          }}>
            <Title level={5} style={{ margin: 0, marginBottom: 8, color: '#666' }}>
              预估详情
            </Title>
            <div style={{ fontSize: '12px', color: '#666', lineHeight: 1.5 }}>
              <div>• 平均每个关键词预估结果: ~{Math.round(metrics.estimatedResults / keywords.length || 0)}个</div>
              <div>• 平均每页预估时间: ~2秒</div>
              <div>• 总预估页数: ~{Math.ceil(metrics.estimatedResults / 50)}页</div>
              <div>• 排序方式: {
                config.sortMethod === 'sales' ? '销量优先' :
                config.sortMethod === 'price_asc' ? '价格升序' :
                config.sortMethod === 'price_desc' ? '价格降序' :
                config.sortMethod === 'rating' ? '评分优先' : '最新发布'
              }</div>
            </div>
          </div>
        </>
      )}
    </Card>
  );
};

export default SearchStats;