import React, { useState, useCallback } from 'react';
import {
  Select,
  Input,
  Button,
  Space,
  Tooltip,
  Popover,
  List,
  Tag,
  Modal,
  message,
  Upload,
  Dropdown,
  Typography,
  Divider
} from 'antd';
import {
  PlusOutlined,
  DeleteOutlined,
  DownloadOutlined,
  UploadOutlined,
  HistoryOutlined,
  TagsOutlined,
  ClearOutlined,
  QuestionCircleOutlined
} from '@ant-design/icons';
import type { KeywordInputProps, KeywordTemplate } from './types';
import {
  parseKeywords,
  validateKeyword,
  keywordHistoryManager,
  keywordTemplateManager,
  exportKeywords,
  importKeywords
} from './utils';

const { TextArea } = Input;
const { Text } = Typography;

const KeywordInput: React.FC<KeywordInputProps> = ({
  value = [],
  onChange,
  placeholder = "请输入关键词，支持逗号、空格、分号分隔",
  maxCount = 100,
  disabled = false
}) => {
  const [inputValue, setInputValue] = useState('');
  const [showBatchInput, setShowBatchInput] = useState(false);
  const [batchInputValue, setBatchInputValue] = useState('');
  const [showHistory, setShowHistory] = useState(false);
  const [showTemplates, setShowTemplates] = useState(false);

  // 获取历史记录
  const keywordHistory = keywordHistoryManager.getHistory();
  const popularKeywords = keywordHistoryManager.getPopularKeywords(20);

  // 获取关键词模板
  const keywordTemplates = keywordTemplateManager.getDefaultTemplates();

  // 处理关键词添加
  const handleAdd = useCallback((newKeyword: string) => {
    if (!newKeyword) return;

    const validation = validateKeyword(newKeyword);
    if (!validation.valid) {
      message.error(validation.message);
      return;
    }

    if (value.includes(newKeyword)) {
      message.warning('关键词已存在');
      return;
    }

    if (value.length >= maxCount) {
      message.error(`最多只能添加${maxCount}个关键词`);
      return;
    }

    const newKeywords = [...value, newKeyword];
    onChange(newKeywords);
    
    // 添加到历史记录
    keywordHistoryManager.addToHistory([newKeyword]);
  }, [value, onChange, maxCount]);

  // 处理批量输入
  const handleBatchAdd = useCallback(() => {
    if (!batchInputValue.trim()) {
      message.warning('请输入关键词');
      return;
    }

    const newKeywords = parseKeywords(batchInputValue);
    if (newKeywords.length === 0) {
      message.warning('没有解析到有效关键词');
      return;
    }

    // 验证关键词
    const invalidKeywords: string[] = [];
    const validKeywords = newKeywords.filter(keyword => {
      const validation = validateKeyword(keyword);
      if (!validation.valid) {
        invalidKeywords.push(keyword);
        return false;
      }
      return true;
    });

    if (invalidKeywords.length > 0) {
      message.warning(`以下关键词无效: ${invalidKeywords.join(', ')}`);
    }

    // 去重
    const existingKeywords = value;
    const uniqueKeywords = validKeywords.filter(keyword => !existingKeywords.includes(keyword));
    
    if (uniqueKeywords.length === 0) {
      message.warning('所有关键词都已存在');
      return;
    }

    // 检查数量限制
    const totalCount = existingKeywords.length + uniqueKeywords.length;
    if (totalCount > maxCount) {
      const allowedCount = maxCount - existingKeywords.length;
      if (allowedCount <= 0) {
        message.error('关键词数量已达上限');
        return;
      }
      message.warning(`超出数量限制，只添加前${allowedCount}个关键词`);
      uniqueKeywords.splice(allowedCount);
    }

    const finalKeywords = [...existingKeywords, ...uniqueKeywords];
    onChange(finalKeywords);
    
    // 添加到历史记录
    keywordHistoryManager.addToHistory(uniqueKeywords);
    
    setBatchInputValue('');
    setShowBatchInput(false);
    message.success(`成功添加${uniqueKeywords.length}个关键词`);
  }, [batchInputValue, value, onChange, maxCount]);

  // 处理关键词删除 (由Tag组件的onClose处理，这里保留作为备用)
  // const handleRemove = useCallback((removedKeyword: string) => {
  //   const newKeywords = value.filter(keyword => keyword !== removedKeyword);
  //   onChange(newKeywords);
  // }, [value, onChange]);

  // 清空所有关键词
  const handleClear = useCallback(() => {
    Modal.confirm({
      title: '确认清空',
      content: '确定要清空所有关键词吗？',
      onOk: () => {
        onChange([]);
        message.success('已清空所有关键词');
      }
    });
  }, [onChange]);

  // 从历史记录添加
  const handleAddFromHistory = useCallback((keyword: string) => {
    handleAdd(keyword);
    setShowHistory(false);
  }, [handleAdd]);

  // 从模板添加
  const handleAddFromTemplate = useCallback((template: KeywordTemplate) => {
    const existingKeywords = value;
    const newKeywords = template.keywords.filter(keyword => !existingKeywords.includes(keyword));
    
    if (newKeywords.length === 0) {
      message.warning('模板中的关键词都已存在');
      return;
    }

    const totalCount = existingKeywords.length + newKeywords.length;
    if (totalCount > maxCount) {
      const allowedCount = maxCount - existingKeywords.length;
      if (allowedCount <= 0) {
        message.error('关键词数量已达上限');
        return;
      }
      newKeywords.splice(allowedCount);
      message.warning(`超出数量限制，只添加前${allowedCount}个关键词`);
    }

    const finalKeywords = [...existingKeywords, ...newKeywords];
    onChange(finalKeywords);
    keywordHistoryManager.addToHistory(newKeywords);
    setShowTemplates(false);
    message.success(`从模板"${template.name}"添加了${newKeywords.length}个关键词`);
  }, [value, onChange, maxCount]);

  // 处理文件导入
  const handleFileImport = useCallback((file: File) => {
    importKeywords(file)
      .then(keywords => {
        const existingKeywords = value;
        const newKeywords = keywords.filter(keyword => !existingKeywords.includes(keyword));
        
        if (newKeywords.length === 0) {
          message.warning('文件中的关键词都已存在');
          return;
        }

        const totalCount = existingKeywords.length + newKeywords.length;
        if (totalCount > maxCount) {
          const allowedCount = maxCount - existingKeywords.length;
          if (allowedCount <= 0) {
            message.error('关键词数量已达上限');
            return;
          }
          newKeywords.splice(allowedCount);
          message.warning(`超出数量限制，只导入前${allowedCount}个关键词`);
        }

        const finalKeywords = [...existingKeywords, ...newKeywords];
        onChange(finalKeywords);
        keywordHistoryManager.addToHistory(newKeywords);
        message.success(`成功导入${newKeywords.length}个关键词`);
      })
      .catch(error => {
        message.error(error.message);
      });
  }, [value, onChange, maxCount]);

  // 导出菜单
  const exportMenuItems = [
    {
      key: 'txt',
      label: '导出为文本文件',
      onClick: () => exportKeywords(value, 'txt')
    },
    {
      key: 'csv',
      label: '导出为CSV文件',
      onClick: () => exportKeywords(value, 'csv')
    },
    {
      key: 'json',
      label: '导出为JSON文件',
      onClick: () => exportKeywords(value, 'json')
    }
  ];

  // 历史记录内容
  const historyContent = (
    <div style={{ width: 300, maxHeight: 400, overflow: 'auto' }}>
      <div style={{ padding: '8px 0', fontWeight: 'bold' }}>
        <HistoryOutlined /> 关键词历史
      </div>
      <Divider style={{ margin: '8px 0' }} />
      {popularKeywords.length > 0 ? (
        <List
          size="small"
          dataSource={popularKeywords}
          renderItem={keyword => (
            <List.Item
              style={{ padding: '4px 0', cursor: 'pointer' }}
              onClick={() => handleAddFromHistory(keyword)}
            >
              <Text>{keyword}</Text>
              <Button type="link" size="small" icon={<PlusOutlined />} />
            </List.Item>
          )}
        />
      ) : (
        <Text type="secondary">暂无历史记录</Text>
      )}
      {keywordHistory.length > 0 && (
        <>
          <Divider style={{ margin: '8px 0' }} />
          <Button
            type="link"
            size="small"
            danger
            icon={<ClearOutlined />}
            onClick={() => {
              keywordHistoryManager.clearHistory();
              setShowHistory(false);
              message.success('已清空历史记录');
            }}
          >
            清空历史
          </Button>
        </>
      )}
    </div>
  );

  // 模板内容
  const templatesContent = (
    <div style={{ width: 350, maxHeight: 400, overflow: 'auto' }}>
      <div style={{ padding: '8px 0', fontWeight: 'bold' }}>
        <TagsOutlined /> 关键词模板
      </div>
      <Divider style={{ margin: '8px 0' }} />
      <List
        size="small"
        dataSource={keywordTemplates}
        renderItem={template => (
          <List.Item style={{ padding: '8px 0' }}>
            <div style={{ width: '100%' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <div>
                  <Text strong>{template.name}</Text>
                  <Text type="secondary" style={{ marginLeft: 8 }}>
                    ({template.keywords.length}个)
                  </Text>
                </div>
                <Button
                  type="primary"
                  size="small"
                  icon={<PlusOutlined />}
                  onClick={() => handleAddFromTemplate(template)}
                >
                  添加
                </Button>
              </div>
              {template.description && (
                <Text type="secondary" style={{ fontSize: '12px', display: 'block', marginTop: 4 }}>
                  {template.description}
                </Text>
              )}
              <div style={{ marginTop: 8 }}>
                {template.keywords.slice(0, 6).map(keyword => (
                  <Tag key={keyword} style={{ marginBottom: 4, fontSize: '12px' }}>
                    {keyword}
                  </Tag>
                ))}
                {template.keywords.length > 6 && (
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    ...等{template.keywords.length - 6}个
                  </Text>
                )}
              </div>
            </div>
          </List.Item>
        )}
      />
    </div>
  );

  return (
    <div>
      {/* 主输入区域 */}
      <div style={{ marginBottom: 16 }}>
        <Select
          mode="tags"
          value={value}
          placeholder={placeholder}
          onChange={onChange}
          onSearch={setInputValue}
          searchValue={inputValue}
          disabled={disabled}
          style={{ width: '100%' }}
          maxCount={maxCount}
          tokenSeparators={[',', '，', ';', '；', ' ']}
          styles={{ 
            popup: { 
              root: { display: 'none' } 
            } 
          }}
          tagRender={(props) => (
            <Tag
              {...props}
              closable={!disabled}
              onClose={props.onClose}
              style={{ marginBottom: 4 }}
            >
              {props.label}
            </Tag>
          )}
        />
      </div>

      {/* 工具栏 */}
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', flexWrap: 'wrap', gap: 8 }}>
        <Space wrap>
          {/* 批量输入 */}
          <Button
            icon={<PlusOutlined />}
            onClick={() => setShowBatchInput(true)}
            disabled={disabled}
          >
            批量输入
          </Button>

          {/* 历史记录 */}
          <Popover
            content={historyContent}
            title={null}
            trigger="click"
            open={showHistory}
            onOpenChange={setShowHistory}
            placement="bottomLeft"
          >
            <Button icon={<HistoryOutlined />} disabled={disabled}>
              历史记录
            </Button>
          </Popover>

          {/* 关键词模板 */}
          <Popover
            content={templatesContent}
            title={null}
            trigger="click"
            open={showTemplates}
            onOpenChange={setShowTemplates}
            placement="bottomLeft"
          >
            <Button icon={<TagsOutlined />} disabled={disabled}>
              模板
            </Button>
          </Popover>

          {/* 文件导入 */}
          <Upload
            beforeUpload={(file) => {
              handleFileImport(file);
              return false;
            }}
            showUploadList={false}
            accept=".txt,.csv,.json"
            disabled={disabled}
          >
            <Button icon={<UploadOutlined />} disabled={disabled}>
              导入
            </Button>
          </Upload>
        </Space>

        <Space wrap>
          {/* 统计信息 */}
          <Text type="secondary">
            {value.length}/{maxCount}
          </Text>

          {/* 导出 */}
          {value.length > 0 && (
            <Dropdown menu={{ items: exportMenuItems }} placement="bottomRight">
              <Button icon={<DownloadOutlined />}>
                导出
              </Button>
            </Dropdown>
          )}

          {/* 清空 */}
          {value.length > 0 && (
            <Button
              icon={<DeleteOutlined />}
              danger
              onClick={handleClear}
              disabled={disabled}
            >
              清空
            </Button>
          )}

          {/* 帮助 */}
          <Tooltip
            title={
              <div>
                <div>支持的分隔符：</div>
                <div>• 英文逗号 (,)</div>
                <div>• 中文逗号 (，)</div>
                <div>• 空格</div>
                <div>• 分号 (;、；)</div>
                <div>• 换行符</div>
              </div>
            }
          >
            <Button
              icon={<QuestionCircleOutlined />}
              type="text"
              size="small"
            />
          </Tooltip>
        </Space>
      </div>

      {/* 批量输入对话框 */}
      <Modal
        title="批量输入关键词"
        open={showBatchInput}
        onOk={handleBatchAdd}
        onCancel={() => {
          setShowBatchInput(false);
          setBatchInputValue('');
        }}
        width={600}
        okText="添加"
        cancelText="取消"
      >
        <div style={{ marginBottom: 16 }}>
          <Text type="secondary">
            请输入关键词，支持多种分隔符（逗号、空格、分号、换行符等）
          </Text>
        </div>
        <TextArea
          value={batchInputValue}
          onChange={(e) => setBatchInputValue(e.target.value)}
          placeholder="例如：手机,耳机;充电器 数据线"
          rows={8}
          showCount
          maxLength={5000}
        />
        {batchInputValue && (
          <div style={{ marginTop: 16 }}>
            <Text type="secondary">
              预览：将解析出 {parseKeywords(batchInputValue).length} 个关键词
            </Text>
            <div style={{ marginTop: 8, maxHeight: 120, overflow: 'auto' }}>
              {parseKeywords(batchInputValue).map(keyword => (
                <Tag key={keyword} style={{ marginBottom: 4 }}>
                  {keyword}
                </Tag>
              ))}
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default KeywordInput;