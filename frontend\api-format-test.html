<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API格式一致性测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 16px 0;
        }
        .frontend-spec, .backend-spec {
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            padding: 16px;
        }
        .frontend-spec {
            background: #f6ffed;
            border-color: #b7eb8f;
        }
        .backend-spec {
            background: #e6f7ff;
            border-color: #91d5ff;
        }
        .spec-title {
            font-weight: bold;
            margin-bottom: 12px;
            font-size: 16px;
        }
        pre {
            background: #f5f5f5;
            padding: 12px;
            border-radius: 4px;
            font-size: 12px;
            overflow-x: auto;
            margin: 8px 0;
            border: 1px solid #e8e8e8;
        }
        .test-result {
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            margin-bottom: 16px;
            overflow: hidden;
        }
        .test-header {
            background: #fafafa;
            padding: 12px 16px;
            border-bottom: 1px solid #d9d9d9;
            font-weight: bold;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .test-content {
            padding: 16px;
        }
        .success {
            background: #f6ffed;
            color: #52c41a;
            border-color: #b7eb8f;
        }
        .failure {
            background: #fff2f0;
            color: #ff4d4f;
            border-color: #ffccc7;
        }
        .warning {
            background: #fffbe6;
            color: #faad14;
            border-color: #ffe58f;
        }
        .field-match {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 4px 8px;
            margin: 2px 0;
            border-radius: 3px;
            background: #f8f9fa;
        }
        .field-match.match { background: #f6ffed; color: #52c41a; }
        .field-match.mismatch { background: #fff2f0; color: #ff4d4f; }
        .field-match.optional { background: #fffbe6; color: #faad14; }
        button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #40a9ff;
        }
        .stats {
            background: #e6f7ff;
            padding: 16px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        .endpoint-test {
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            margin-bottom: 16px;
            padding: 16px;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-connected { background: #52c41a; }
        .status-disconnected { background: #ff4d4f; }
        .status-warning { background: #faad14; }
    </style>
</head>
<body>
    <h1>API格式一致性测试</h1>
    
    <div class="stats">
        <h3>测试概览</h3>
        <div>📋 API端点数量: <span id="totalEndpoints">0</span></div>
        <div>✅ 格式匹配: <span id="matchingEndpoints">0</span></div>
        <div>❌ 格式不匹配: <span id="mismatchEndpoints">0</span></div>
        <div>⚠️ 部分匹配: <span id="partialEndpoints">0</span></div>
    </div>

    <div class="test-container">
        <h2>请求格式对比测试</h2>
        <button onclick="testAllRequestFormats()">测试所有请求格式</button>
        <button onclick="testResponseFormats()">测试响应格式</button>
        <button onclick="clearResults()">清除结果</button>
        <div id="requestFormatResults"></div>
    </div>

    <div class="test-container">
        <h2>API端点可用性测试</h2>
        <button onclick="testAllEndpoints()">测试所有端点</button>
        <div id="endpointResults"></div>
    </div>

    <script>
        // API规格定义
        const API_SPECS = {
            // 爬虫相关API
            crawl: {
                start: {
                    method: 'POST',
                    path: '/api/crawl/start',
                    frontend: {
                        // 前端发送的请求格式（基于SearchConfig类型）
                        request: {
                            keyword: 'string',  // 单个关键词
                            category: 'string?',
                            minPrice: 'number?',
                            maxPrice: 'number?',
                            sortType: 'string?',
                            pageSize: 'number?',
                            maxPages: 'number?'
                        },
                        // 新版多关键词格式
                        multiRequest: {
                            keywords: 'string[]',
                            targetCount: 'number',
                            sortMethod: 'string?',
                            maxPages: 'number?',
                            headless: 'boolean?',
                            enableFilter: 'boolean?'
                        }
                    },
                    backend: {
                        // 后端期望的请求格式（基于CrawlRequest模型）
                        request: {
                            keywords: 'string[]',      // 必需
                            targetCount: 'number',     // 必需
                            sortMethod: 'string?',     // 可选，默认"default"
                            maxPages: 'number?',       // 可选，默认5
                            headless: 'boolean?',      // 可选，默认True
                            enableFilter: 'boolean?'   // 可选，默认False
                        },
                        response: {
                            success: 'boolean',
                            taskId: 'string',
                            message: 'string'
                        }
                    }
                },
                status: {
                    method: 'GET',
                    path: '/api/crawl/status',
                    backend: {
                        response: {
                            success: 'boolean',
                            data: {
                                isRunning: 'boolean',
                                currentKeyword: 'string?',
                                progress: {
                                    currentPage: 'number',
                                    totalPages: 'number',
                                    collectedCount: 'number',
                                    failedCount: 'number'
                                },
                                startTime: 'string?',
                                estimatedEndTime: 'string?',
                                error: 'string?'
                            }
                        }
                    }
                },
                stop: {
                    method: 'POST',
                    path: '/api/crawl/stop',
                    backend: {
                        response: {
                            success: 'boolean',
                            message: 'string'
                        }
                    }
                },
                pause: {
                    method: 'POST',
                    path: '/api/crawl/{task_id}/pause',
                    backend: {
                        response: {
                            success: 'boolean',
                            message: 'string'
                        }
                    }
                }
            },
            
            // Cookie相关API
            cookie: {
                status: {
                    method: 'GET',
                    path: '/api/cookie/status',
                    backend: {
                        response: {
                            success: 'boolean',
                            data: {
                                exists: 'boolean',
                                valid: 'boolean',
                                expiresAt: 'string?',
                                cookies: 'Cookie[]'
                            }
                        }
                    }
                },
                import: {
                    method: 'POST',
                    path: '/api/cookie/import',
                    frontend: {
                        request: {
                            cookies: 'Cookie[]?',
                            cookieString: 'string?'
                        }
                    },
                    backend: {
                        request: {
                            cookies: 'Cookie[]?',
                            cookieString: 'string?'
                        },
                        response: {
                            success: 'boolean',
                            message: 'string'
                        }
                    }
                },
                validate: {
                    method: 'POST', 
                    path: '/api/cookie/validate',
                    frontend: {
                        request: {
                            cookies: 'Cookie[]'
                        }
                    },
                    backend: {
                        response: {
                            success: 'boolean',
                            data: {
                                valid: 'boolean',
                                message: 'string'
                            }
                        }
                    }
                },
                save: {
                    method: 'POST',
                    path: '/api/cookie/save',
                    frontend: {
                        request: 'Cookie[]'  // 直接传数组
                    },
                    backend: {
                        request: {
                            cookies: 'Cookie[]'
                        },
                        response: {
                            success: 'boolean',
                            message: 'string'
                        }
                    }
                },
                clear: {
                    method: 'DELETE',
                    path: '/api/cookie/clear',
                    backend: {
                        response: {
                            success: 'boolean',
                            message: 'string'
                        }
                    }
                }
            },

            // 产品数据API
            products: {
                list: {
                    method: 'GET',
                    path: '/api/products',
                    frontend: {
                        params: {
                            page: 'number?',
                            pageSize: 'number?',
                            keyword: 'string?',
                            sortBy: 'string?',
                            sortOrder: 'string?'
                        }
                    },
                    backend: {
                        response: {
                            success: 'boolean',
                            data: {
                                products: 'Product[]',
                                total: 'number'
                            }
                        }
                    }
                }
            },

            // 导出API
            export: {
                excel: {
                    method: 'GET',
                    path: '/api/export/excel',
                    backend: {
                        response: 'Blob'
                    }
                },
                csv: {
                    method: 'GET',
                    path: '/api/export/csv',
                    backend: {
                        response: 'Blob'
                    }
                }
            },

            // 系统API
            system: {
                health: {
                    method: 'GET',
                    path: '/api/health',
                    backend: {
                        response: {
                            status: 'string',
                            timestamp: 'string',
                            services: {
                                api: 'string',
                                crawler: 'string'
                            }
                        }
                    }
                }
            }
        };

        let testStats = {
            total: 0,
            matching: 0,
            mismatch: 0,
            partial: 0
        };

        // 测试所有请求格式
        function testAllRequestFormats() {
            const results = document.getElementById('requestFormatResults');
            results.innerHTML = '';
            testStats = { total: 0, matching: 0, mismatch: 0, partial: 0 };

            // 测试爬虫启动API的格式匹配
            testCrawlStartFormat();
            testCookieFormats();
            testProductFormats();

            updateStats();
        }

        // 测试爬虫启动格式
        function testCrawlStartFormat() {
            const crawlStart = API_SPECS.crawl.start;
            const results = document.getElementById('requestFormatResults');
            
            // 检查新旧格式兼容性
            const frontendOld = crawlStart.frontend.request;
            const frontendNew = crawlStart.frontend.multiRequest;
            const backendExpected = crawlStart.backend.request;

            testStats.total++;

            // 分析格式匹配情况
            const oldToNewMapping = {
                // 旧格式需要转换为新格式
                keyword: 'keywords',  // string -> string[] 
                maxPages: 'maxPages', // 相同
                // 缺失字段: targetCount（必需）
            };

            const newFormatMatch = compareFormats(frontendNew, backendExpected);
            const oldFormatCompatible = checkOldFormatCompatibility(frontendOld, backendExpected);

            const overallMatch = newFormatMatch.score >= 0.8;
            
            if (overallMatch) {
                testStats.matching++;
            } else if (newFormatMatch.score >= 0.5) {
                testStats.partial++;
            } else {
                testStats.mismatch++;
            }

            results.innerHTML += `
                <div class="test-result ${overallMatch ? 'success' : newFormatMatch.score >= 0.5 ? 'warning' : 'failure'}">
                    <div class="test-header">
                        ${overallMatch ? '✅' : newFormatMatch.score >= 0.5 ? '⚠️' : '❌'} 爬虫启动API格式
                        <span>匹配度: ${(newFormatMatch.score * 100).toFixed(1)}%</span>
                    </div>
                    <div class="test-content">
                        <div class="comparison">
                            <div class="frontend-spec">
                                <div class="spec-title">前端请求格式（新版）</div>
                                <pre>${JSON.stringify(frontendNew, null, 2)}</pre>
                                
                                <div class="spec-title">前端请求格式（旧版）</div>
                                <pre>${JSON.stringify(frontendOld, null, 2)}</pre>
                                
                                <div style="margin-top: 12px;">
                                    <strong>兼容性分析:</strong>
                                    <div>✅ 新格式完全匹配后端</div>
                                    <div>⚠️ 旧格式需要转换适配</div>
                                    <div>❌ 旧格式缺少 targetCount（必需字段）</div>
                                </div>
                            </div>
                            <div class="backend-spec">
                                <div class="spec-title">后端期望格式</div>
                                <pre>${JSON.stringify(backendExpected, null, 2)}</pre>
                                
                                <div style="margin-top: 12px;">
                                    <strong>字段匹配检查:</strong>
                                    ${newFormatMatch.details.map(d => 
                                        `<div class="field-match ${d.status}">${d.field}: ${d.message}</div>`
                                    ).join('')}
                                </div>
                            </div>
                        </div>
                        
                        <div style="margin-top: 16px; padding: 12px; background: #f8f9fa; border-radius: 4px;">
                            <strong>建议修复:</strong>
                            <ul>
                                <li>前端应优先使用新版多关键词格式</li>
                                <li>旧版单关键词需要转换: keyword → keywords: [keyword]</li>
                                <li>必须提供 targetCount 参数</li>
                                <li>确保 API 调用使用正确的字段名</li>
                            </ul>
                        </div>
                    </div>
                </div>
            `;
        }

        // 测试Cookie格式
        function testCookieFormats() {
            const results = document.getElementById('requestFormatResults');
            const cookieApis = API_SPECS.cookie;

            Object.keys(cookieApis).forEach(apiName => {
                const api = cookieApis[apiName];
                testStats.total++;

                let matchResult = { score: 1, details: [] };
                let overallMatch = true;

                // 特殊处理Cookie保存API的格式不匹配
                if (apiName === 'save') {
                    matchResult = {
                        score: 0.5,
                        details: [
                            { field: 'request', status: 'mismatch', message: '前端直接传数组，后端期望对象包装' }
                        ]
                    };
                    overallMatch = false;
                    testStats.partial++;
                } else {
                    testStats.matching++;
                }

                results.innerHTML += `
                    <div class="test-result ${overallMatch ? 'success' : 'warning'}">
                        <div class="test-header">
                            ${overallMatch ? '✅' : '⚠️'} Cookie ${apiName.toUpperCase()} API
                            <span>${api.method} ${api.path}</span>
                        </div>
                        <div class="test-content">
                            ${api.frontend ? `
                                <div class="comparison">
                                    <div class="frontend-spec">
                                        <div class="spec-title">前端格式</div>
                                        <pre>${JSON.stringify(api.frontend, null, 2)}</pre>
                                    </div>
                                    <div class="backend-spec">
                                        <div class="spec-title">后端格式</div>
                                        <pre>${JSON.stringify(api.backend, null, 2)}</pre>
                                    </div>
                                </div>
                                ${!overallMatch ? `
                                    <div style="margin-top: 16px; padding: 12px; background: #fffbe6; border-radius: 4px;">
                                        <strong>格式不匹配:</strong>
                                        <ul>
                                            <li>前端 saveCookie(cookies) 直接传递数组</li>
                                            <li>后端期望 { cookies: Cookie[] } 对象格式</li>
                                            <li>需要修改前端调用或后端接口</li>
                                        </ul>
                                    </div>
                                ` : ''}
                            ` : `
                                <div>
                                    <strong>后端响应格式:</strong>
                                    <pre>${JSON.stringify(api.backend.response, null, 2)}</pre>
                                </div>
                            `}
                        </div>
                    </div>
                `;
            });
        }

        // 测试产品数据格式
        function testProductFormats() {
            const results = document.getElementById('requestFormatResults');
            const productApi = API_SPECS.products.list;
            
            testStats.total++;
            testStats.matching++; // 产品API格式匹配良好

            results.innerHTML += `
                <div class="test-result success">
                    <div class="test-header">
                        ✅ 产品数据API
                        <span>${productApi.method} ${productApi.path}</span>
                    </div>
                    <div class="test-content">
                        <div class="comparison">
                            <div class="frontend-spec">
                                <div class="spec-title">前端查询参数</div>
                                <pre>${JSON.stringify(productApi.frontend.params, null, 2)}</pre>
                            </div>
                            <div class="backend-spec">
                                <div class="spec-title">后端响应格式</div>
                                <pre>${JSON.stringify(productApi.backend.response, null, 2)}</pre>
                            </div>
                        </div>
                        <div style="margin-top: 16px; padding: 12px; background: #f6ffed; border-radius: 4px;">
                            <strong>✅ 格式完全匹配</strong>
                            <div>• 查询参数格式正确</div>
                            <div>• 响应数据结构一致</div>
                            <div>• 包含必要的 subsidy_info 字段</div>
                        </div>
                    </div>
                </div>
            `;
        }

        // 测试响应格式
        function testResponseFormats() {
            const results = document.getElementById('requestFormatResults');
            
            results.innerHTML += `
                <div class="test-result success">
                    <div class="test-header">✅ 统一响应格式检查</div>
                    <div class="test-content">
                        <div class="comparison">
                            <div class="frontend-spec">
                                <div class="spec-title">前端期望的统一响应格式</div>
                                <pre>{
  "success": boolean,
  "data": any,
  "message": string?,
  "error": string?
}</pre>
                            </div>
                            <div class="backend-spec">
                                <div class="spec-title">后端实际响应格式</div>
                                <pre>{
  "success": boolean,
  "taskId": string,     // 部分API
  "data": any,          // 部分API
  "message": string,
  "services": object    // 健康检查API
}</pre>
                            </div>
                        </div>
                        <div style="margin-top: 16px; padding: 12px; background: #f6ffed; border-radius: 4px;">
                            <strong>✅ 基础格式一致</strong>
                            <div>• 所有API都返回 success 字段</div>
                            <div>• 错误时都有 message 字段</div>
                            <div>• 数据封装在 data 字段（大部分API）</div>
                        </div>
                    </div>
                </div>
            `;
        }

        // 测试所有端点可用性
        async function testAllEndpoints() {
            const results = document.getElementById('endpointResults');
            results.innerHTML = '<div>正在测试API端点...</div>';

            const endpoints = [
                { name: '健康检查', method: 'GET', path: '/api/health' },
                { name: 'Cookie状态', method: 'GET', path: '/api/cookie/status' },
                { name: '爬虫状态', method: 'GET', path: '/api/crawl/status' },
                { name: '产品列表', method: 'GET', path: '/api/products?page=1&pageSize=5' }
            ];

            let testResults = [];
            
            for (const endpoint of endpoints) {
                try {
                    const response = await fetch(endpoint.path, { method: endpoint.method });
                    const isSuccess = response.ok;
                    const data = await response.json();
                    
                    testResults.push({
                        ...endpoint,
                        status: isSuccess ? 'success' : 'error',
                        responseCode: response.status,
                        data: data,
                        error: null
                    });
                } catch (error) {
                    testResults.push({
                        ...endpoint,
                        status: 'error',
                        responseCode: 0,
                        data: null,
                        error: error.message
                    });
                }
            }

            // 显示结果
            const successCount = testResults.filter(r => r.status === 'success').length;
            results.innerHTML = `
                <div style="margin-bottom: 16px;">
                    <strong>端点测试结果: ${successCount}/${testResults.length} 可用</strong>
                </div>
                ${testResults.map(result => `
                    <div class="endpoint-test">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                            <span>
                                <span class="status-indicator ${result.status === 'success' ? 'status-connected' : 'status-disconnected'}"></span>
                                <strong>${result.name}</strong>
                            </span>
                            <span style="font-family: monospace; font-size: 12px;">
                                ${result.method} ${result.path}
                            </span>
                        </div>
                        <div>
                            <strong>状态:</strong> ${result.responseCode} 
                            ${result.status === 'success' ? '✅ 可用' : '❌ 不可用'}
                        </div>
                        ${result.error ? `<div><strong>错误:</strong> ${result.error}</div>` : ''}
                        ${result.data ? `
                            <div style="margin-top: 8px;">
                                <strong>响应数据:</strong>
                                <pre style="max-height: 100px; overflow-y: auto;">${JSON.stringify(result.data, null, 2)}</pre>
                            </div>
                        ` : ''}
                    </div>
                `).join('')}
            `;
        }

        // 比较两个格式的匹配度
        function compareFormats(frontend, backend) {
            const frontendFields = Object.keys(frontend);
            const backendFields = Object.keys(backend);
            
            let matches = 0;
            let total = backendFields.length;
            let details = [];

            backendFields.forEach(field => {
                if (frontendFields.includes(field)) {
                    const frontendType = frontend[field];
                    const backendType = backend[field];
                    
                    if (frontendType === backendType) {
                        matches++;
                        details.push({ field, status: 'match', message: '类型完全匹配' });
                    } else {
                        details.push({ field, status: 'mismatch', message: `类型不匹配: ${frontendType} vs ${backendType}` });
                    }
                } else {
                    details.push({ field, status: 'mismatch', message: '前端缺少此字段' });
                }
            });

            // 检查前端多余的字段
            frontendFields.forEach(field => {
                if (!backendFields.includes(field)) {
                    details.push({ field, status: 'optional', message: '前端额外字段' });
                }
            });

            return {
                score: matches / total,
                details: details
            };
        }

        // 检查旧格式兼容性
        function checkOldFormatCompatibility(oldFormat, backendFormat) {
            // 旧格式主要问题：
            // 1. keyword (string) vs keywords (string[]) 
            // 2. 缺少必需的 targetCount
            return {
                compatible: false,
                issues: [
                    'keyword字段需要转换为keywords数组',
                    '缺少必需的targetCount字段',
                    '其他字段基本兼容'
                ]
            };
        }

        // 更新统计信息
        function updateStats() {
            document.getElementById('totalEndpoints').textContent = testStats.total;
            document.getElementById('matchingEndpoints').textContent = testStats.matching;
            document.getElementById('mismatchEndpoints').textContent = testStats.mismatch;
            document.getElementById('partialEndpoints').textContent = testStats.partial;
        }

        // 清除结果
        function clearResults() {
            document.getElementById('requestFormatResults').innerHTML = '';
            document.getElementById('endpointResults').innerHTML = '';
            testStats = { total: 0, matching: 0, mismatch: 0, partial: 0 };
            updateStats();
        }

        // 页面加载时初始化
        window.onload = function() {
            console.log('API格式一致性测试页面已加载');
            updateStats();
        };
    </script>
</body>
</html>