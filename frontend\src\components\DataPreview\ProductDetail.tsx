/**
 * 商品详情组件 - 展示单个商品的完整信息
 */

import React, { useMemo } from 'react';
import { Modal, Descriptions, Image, Tag, Space, Button, message, Divider, Typography } from 'antd';
import { LinkOutlined, CopyOutlined, GiftOutlined, ShoppingCartOutlined } from '@ant-design/icons';
import type { ProductDetailProps } from './types';
import { formatPrice, formatSales, formatRating } from './utils';

const { Text, Title } = Typography;

const ProductDetail: React.FC<ProductDetailProps> = ({
  product,
  visible,
  onClose
}) => {
  // 处理复制功能
  const handleCopy = (text: string, label: string) => {
    navigator.clipboard.writeText(text).then(() => {
      message.success(`${label}已复制到剪贴板`);
    }).catch(() => {
      message.error('复制失败');
    });
  };

  // 处理标签显示
  const renderTags = (tagsStr: string | undefined, color: string = 'blue') => {
    if (!tagsStr?.trim()) return null;
    
    const tags = tagsStr.split(',').map(tag => tag.trim()).filter(tag => tag);
    return (
      <Space wrap>
        {tags.map((tag, index) => (
          <Tag key={index} color={tag.includes('百亿补贴') ? 'red' : color}>
            {tag}
          </Tag>
        ))}
      </Space>
    );
  };

  // 渲染补贴信息
  const renderSubsidyInfo = (subsidyInfo: string | undefined) => {
    if (!subsidyInfo?.trim()) {
      return <Tag color="default">无补贴</Tag>;
    }

    const tags = [];
    if (subsidyInfo.includes('百亿补贴')) {
      tags.push(<Tag key="billion" color="red" icon={<GiftOutlined />}>百亿补贴</Tag>);
    }
    if (subsidyInfo.includes('国补') || subsidyInfo.includes('政府补贴')) {
      tags.push(<Tag key="gov" color="blue" icon={<GiftOutlined />}>政府补贴</Tag>);
    }
    if (subsidyInfo.includes('优惠券')) {
      tags.push(<Tag key="coupon" color="green">优惠券</Tag>);
    }
    if (subsidyInfo.includes('满减')) {
      tags.push(<Tag key="discount" color="orange">满减活动</Tag>);
    }

    if (tags.length === 0) {
      return <Tag color="purple">{subsidyInfo}</Tag>;
    }

    return <Space wrap>{tags}</Space>;
  };

  // 商品详情数据
  const productInfo = useMemo(() => {
    if (!product) return null;

    return [
      {
        label: '基本信息',
        items: [
          { label: '商品ID', value: product.goods_id, copyable: true },
          { label: '商品名称', value: product.goods_name, copyable: true },
          { label: '搜索关键词', value: product.keyword },
          { label: '品牌名称', value: product.brand_name || '未知' },
          { label: '商品分类', value: product.category || '未分类' },
        ]
      },
      {
        label: '价格信息',
        items: [
          { label: '拼团价', value: formatPrice(product.price), highlight: true },
          { label: '原价', value: formatPrice(product.original_price) },
          { label: '券后价', value: formatPrice(product.coupon_price) },
          { label: '市场价', value: formatPrice(product.market_price) },
          { label: '价格类型', value: product.price_type_name },
        ].filter(item => item.value && item.value !== '-')
      },
      {
        label: '销量与评价',
        items: [
          { label: '销量', value: formatSales(product.sales), highlight: true },
          { label: '评论数', value: product.comment_count?.toLocaleString() || '-' },
          { label: '评分', value: formatRating(product.rating) },
          { label: '销量描述', value: product.sales_tip },
        ].filter(item => item.value && item.value !== '-')
      },
      {
        label: '补贴与活动',
        items: [
          { 
            label: '补贴详情', 
            value: product.subsidy_info, 
            render: () => renderSubsidyInfo(product.subsidy_info),
            highlight: true 
          },
          { label: '活动类型', value: product.activity_type_name },
          { label: '商家类型', value: product.merchant_type_name },
        ].filter(item => item.value)
      },
      {
        label: '标签信息',
        items: [
          { 
            label: '营销标签', 
            value: product.marketing_tags,
            render: () => renderTags(product.marketing_tags, 'red')
          },
          { 
            label: '商品标签', 
            value: product.tags,
            render: () => renderTags(product.tags, 'blue')
          },
          { label: '特殊信息', value: product.special_text },
        ].filter(item => item.value)
      },
      {
        label: '链接信息',
        items: [
          { 
            label: '商品链接', 
            value: product.goods_url,
            render: () => (
              <Button 
                type="link" 
                icon={<LinkOutlined />}
                onClick={() => window.open(product.goods_url, '_blank')}
              >
                查看商品
              </Button>
            )
          },
          { label: '采集时间', value: product.created_time ? new Date(product.created_time).toLocaleString() : '-' },
        ].filter(item => item.value && item.value !== '-')
      }
    ];
  }, [product]);

  if (!product) return null;

  return (
    <Modal
      title={
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <ShoppingCartOutlined style={{ marginRight: 8 }} />
          商品详情
        </div>
      }
      open={visible}
      onCancel={onClose}
      footer={[
        <Button key="close" onClick={onClose}>
          关闭
        </Button>,
        <Button 
          key="view" 
          type="primary" 
          icon={<LinkOutlined />}
          onClick={() => window.open(product.goods_url, '_blank')}
        >
          查看商品
        </Button>
      ]}
      width={800}
      style={{ top: 20 }}
      styles={{ body: { maxHeight: '70vh', overflowY: 'auto' } }}
    >
      {/* 商品图片和基本信息 */}
      <div style={{ display: 'flex', marginBottom: 24, gap: 16 }}>
        {/* 商品图片 */}
        <div style={{ flexShrink: 0 }}>
          <Image
            src={product.hd_thumb_url || product.image_url}
            alt={product.goods_name}
            width={200}
            height={200}
            style={{ objectFit: 'cover', borderRadius: 8, border: '1px solid #d9d9d9' }}
            fallback="data:image/png;base64,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"
          />
        </div>

        {/* 基本信息 */}
        <div style={{ flex: 1 }}>
          <Title level={4} style={{ margin: '0 0 12px 0', lineHeight: 1.4 }}>
            {product.goods_name}
          </Title>
          
          <Space direction="vertical" size={8} style={{ width: '100%' }}>
            <div>
              <Text strong>拼团价: </Text>
              <Text style={{ fontSize: '20px', color: '#f5222d', fontWeight: 'bold' }}>
                {formatPrice(product.price)}
              </Text>
              {product.original_price && Number(product.original_price) > Number(product.price) && (
                <Text delete style={{ marginLeft: 8, color: '#999' }}>
                  {formatPrice(product.original_price)}
                </Text>
              )}
            </div>

            <div>
              <Text strong>销量: </Text>
              <Text style={{ fontSize: '16px', color: '#52c41a', fontWeight: 'bold' }}>
                {formatSales(product.sales)}
              </Text>
              {product.rating && (
                <>
                  <Divider type="vertical" />
                  <Text strong>评分: </Text>
                  <Text style={{ color: '#faad14' }}>{formatRating(product.rating)}</Text>
                </>
              )}
            </div>

            <div>
              <Text strong>补贴情况: </Text>
              {renderSubsidyInfo(product.subsidy_info)}
            </div>

            {product.brand_name && (
              <div>
                <Text strong>品牌: </Text>
                <Tag color="blue">{product.brand_name}</Tag>
              </div>
            )}
          </Space>
        </div>
      </div>

      {/* 详细信息 */}
      {productInfo?.map((section, sectionIndex) => (
        <div key={sectionIndex} style={{ marginBottom: 24 }}>
          <Title level={5} style={{ marginBottom: 16, color: '#1890ff' }}>
            {section.label}
          </Title>
          
          <Descriptions column={2} size="small" bordered>
            {section.items.map((item, itemIndex) => (
              <Descriptions.Item
                key={itemIndex}
                label={item.label}
                labelStyle={{ 
                  fontWeight: 'bold',
                  backgroundColor: '#fafafa',
                  width: '120px'
                }}
                contentStyle={{
                  fontWeight: (item as any).highlight ? 'bold' : 'normal',
                  color: (item as any).highlight ? '#f5222d' : 'inherit'
                }}
              >
                <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                  {(item as any).render ? (
                    (item as any).render()
                  ) : (
                    <span>{item.value}</span>
                  )}
                  
                  {(item as any).copyable && item.value && (
                    <Button
                      type="text"
                      size="small"
                      icon={<CopyOutlined />}
                      onClick={() => handleCopy(item.value as string, item.label)}
                      style={{ padding: '0 4px' }}
                    />
                  )}
                </div>
              </Descriptions.Item>
            ))}
          </Descriptions>
        </div>
      ))}
    </Modal>
  );
};

export default ProductDetail;