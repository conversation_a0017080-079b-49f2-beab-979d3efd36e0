# 实时进度监控组件

## 概述

ProgressMonitor 是一个完整的 WebSocket 实时监控解决方案，用于显示爬虫任务的实时进度和状态信息。

## 组件结构

```
ProgressMonitor/
├── index.tsx            # 主监控组件
├── ProgressBar.tsx      # 进度条组件
├── KeywordProgress.tsx  # 关键词进度组件
├── RealtimeStats.tsx    # 实时统计组件
├── ConnectionStatus.tsx # 连接状态组件
├── types.ts            # 类型定义
├── utils.ts            # 工具函数
└── README.md           # 文档
```

## 核心功能

### 1. WebSocket 实时通信
- 使用 `react-use-websocket` 库实现稳定连接
- 自动重连机制（指数退避算法）
- 心跳检测（30秒间隔）
- 连接状态管理和监控

### 2. 进度监控
- **整体进度**：环形和条形进度条
- **关键词进度**：多关键词处理状态跟踪
- **实时统计**：速度、成功率、错误统计
- **连接状态**：网络延迟、重连次数、心跳状态

### 3. 视觉效果
- 平滑动画效果
- 数值变化突出显示
- 状态颜色编码
- 响应式布局

## 使用方法

### 基本用法

```tsx
import ProgressMonitor from '@/components/ProgressMonitor';

function CrawlerPage() {
  const [taskId, setTaskId] = useState<string>();

  return (
    <ProgressMonitor 
      taskId={taskId}
      showAdvanced={true}
      refreshInterval={1000}
    />
  );
}
```

### 属性说明

```tsx
interface ProgressMonitorProps {
  taskId?: string;           // 任务ID，用于WebSocket连接
  showAdvanced?: boolean;    // 是否显示高级信息
  refreshInterval?: number;  // 刷新间隔（毫秒）
  className?: string;        // CSS类名
}
```

## WebSocket 消息格式

### 发送消息

```json
{
  "type": "ping",
  "timestamp": "2024-08-01T10:00:00.000Z"
}
```

### 接收消息

#### 连接成功
```json
{
  "type": "connected",
  "payload": {},
  "timestamp": "2024-08-01T10:00:00.000Z"
}
```

#### 进度更新
```json
{
  "type": "progress",
  "payload": {
    "currentPage": 5,
    "totalPages": 50,
    "collectedCount": 100,
    "failedCount": 2
  },
  "timestamp": "2024-08-01T10:00:00.000Z"
}
```

#### 关键词状态
```json
{
  "type": "keyword_started",
  "payload": {
    "keyword": "手机"
  },
  "timestamp": "2024-08-01T10:00:00.000Z"
}
```

#### 数据接收
```json
{
  "type": "data",
  "payload": {
    "products": [...]
  },
  "timestamp": "2024-08-01T10:00:00.000Z"
}
```

#### 任务完成
```json
{
  "type": "completed",
  "payload": {
    "totalProcessed": 1000,
    "successCount": 980,
    "failedCount": 20
  },
  "timestamp": "2024-08-01T10:00:00.000Z"
}
```

#### 错误信息
```json
{
  "type": "error",
  "payload": {
    "message": "网络连接失败"
  },
  "timestamp": "2024-08-01T10:00:00.000Z"
}
```

## 子组件详解

### ProgressBar
进度条组件，支持环形和条形两种样式。

```tsx
<ProgressBar
  current={10}
  total={100}
  type="circle"
  status="active"
  showInfo={true}
/>
```

### KeywordProgress
关键词进度追踪，显示当前处理的关键词及其状态。

```tsx
<KeywordProgress
  currentKeyword="手机"
  completedKeywords={["电脑", "平板"]}
  failedKeywords={["相机"]}
  totalKeywords={10}
/>
```

### RealtimeStats
实时统计信息，包含速度、成功率等指标。

```tsx
<RealtimeStats
  speed={30}
  successRate={95.5}
  errorCount={5}
  totalProcessed={1000}
  estimatedTimeRemaining={300}
  showChart={true}
/>
```

### ConnectionStatus
WebSocket连接状态监控。

```tsx
<ConnectionStatus
  isConnected={true}
  connectionState="connected"
  latency={150}
  reconnectAttempts={0}
  lastHeartbeat={new Date()}
  messagesReceived={100}
  onReconnect={handleReconnect}
/>
```

## 工具函数

### 时间格式化
```tsx
import { formatTime, formatSpeed } from './utils';

const timeString = formatTime(300); // "5分0秒"
const speedString = formatSpeed(30); // "30 条/分钟"
```

### 数据处理
```tsx
import { updateChartData, calculateEstimatedTime } from './utils';

const newData = updateChartData(oldData, newValue, 50);
const estimatedTime = calculateEstimatedTime(10, 100, 30);
```

## 性能优化

### 1. 数据更新节流
- 使用 `throttle` 函数限制更新频率
- 避免过于频繁的状态更新

### 2. 组件记忆化
- 使用 `useCallback` 和 `useMemo` 优化渲染
- 减少不必要的重新渲染

### 3. 图表数据限制
- 限制图表数据点数量（默认50个）
- 自动清理过期数据

## 错误处理

### 1. 连接错误
- 自动重连机制
- 用户手动重连选项
- 错误状态显示

### 2. 数据解析错误
- JSON解析异常处理
- 消息格式验证
- 降级显示

### 3. 网络异常
- 断线检测
- 重连策略
- 状态恢复

## 自定义样式

### CSS变量
```css
:root {
  --progress-primary-color: #1890ff;
  --progress-success-color: #52c41a;
  --progress-error-color: #ff4d4f;
  --progress-warning-color: #faad14;
}
```

### 主题配置
```tsx
const theme: ProgressTheme = {
  primaryColor: '#1890ff',
  successColor: '#52c41a',
  errorColor: '#ff4d4f',
  warningColor: '#faad14',
  textColor: '#262626',
  backgroundColor: '#ffffff',
  borderRadius: 6,
};
```

## 测试

### 单元测试
```bash
npm test -- ProgressMonitor
```

### 集成测试
```bash
npm run test:integration
```

## 故障排除

### 常见问题

1. **WebSocket连接失败**
   - 检查后端服务是否运行
   - 验证URL格式是否正确
   - 检查网络防火墙设置

2. **数据不更新**
   - 确认WebSocket连接状态
   - 检查消息格式是否正确
   - 验证事件监听器是否正确注册

3. **性能问题**
   - 调整刷新间隔
   - 限制图表数据点数量
   - 使用节流函数优化更新频率

### 调试技巧

1. **开启调试日志**
```tsx
const { stats } = useWebSocket(taskId);
console.log('WebSocket Stats:', stats);
```

2. **检查连接状态**
```tsx
const { connectionState, readyState } = useWebSocket(taskId);
console.log('Connection:', connectionState, readyState);
```

3. **监控消息流**
```tsx
// 在useWebSocket中添加
console.log('Received message:', message);
```

## 版本历史

- **v1.0.0**: 初始版本，基本功能实现
- **v1.1.0**: 增加心跳检测和自动重连
- **v1.2.0**: 添加实时图表和动画效果
- **v1.3.0**: 性能优化和错误处理改进