const express = require('express');
const cors = require('cors');
const app = express();
const port = 8001;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Store cookies in memory
let storedCookies = [];

// API Routes
app.get('/api/health', (req, res) => {
  res.json({ status: 'healthy', service: 'PDD Crawler API' });
});

app.get('/api/cookie/status', (req, res) => {
  res.json({
    success: true,
    data: {
      exists: storedCookies.length > 0,
      valid: storedCookies.length > 0,
      cookies: storedCookies,
      expiresAt: null
    }
  });
});

app.post('/api/cookie/import', (req, res) => {
  const { cookies, cookie_list } = req.body;
  
  if (typeof cookies === 'string') {
    // Parse cookie string
    const parsedCookies = cookies.split(';').map(item => {
      const [name, value] = item.trim().split('=');
      return { name, value, domain: '.pinduoduo.com' };
    }).filter(c => c.name && c.value);
    
    storedCookies = parsedCookies;
  } else if (Array.isArray(cookies)) {
    // Handle array of cookie objects
    storedCookies = cookies;
  } else if (cookie_list) {
    storedCookies = cookie_list;
  }
  
  res.json({
    success: true,
    message: `成功导入 ${storedCookies.length} 个Cookie`
  });
});

app.post('/api/cookie/validate', (req, res) => {
  if (storedCookies.length > 0) {
    res.json({
      success: true,
      data: {
        valid: true,
        message: 'Cookie验证成功'
      }
    });
  } else {
    res.json({
      success: false,
      data: {
        valid: false,
        message: '没有Cookie数据'
      }
    });
  }
});

app.post('/api/cookie/save', (req, res) => {
  res.json({
    success: true,
    message: 'Cookie已保存'
  });
});

app.delete('/api/cookie/clear', (req, res) => {
  storedCookies = [];
  res.json({
    success: true,
    message: 'Cookie已清除'
  });
});

app.post('/api/crawl/start', (req, res) => {
  res.json({
    success: true,
    task_id: 'test-task-001',
    message: '爬虫任务已启动'
  });
});

app.get('/api/crawl/:taskId/status', (req, res) => {
  res.json({
    success: true,
    data: {
      task_id: req.params.taskId,
      status: 'running',
      progress: {
        currentPage: 1,
        totalPages: 10,
        collectedCount: 5
      }
    }
  });
});

// Start server
app.listen(port, () => {
  console.log(`简化的后端API服务器运行在 http://localhost:${port}`);
  console.log(`API文档: http://localhost:${port}/api/health`);
});