// ProgressMonitor 组件类型定义
export interface ProgressBarProps {
  current: number;
  total: number;
  type?: 'circle' | 'line';
  status?: 'normal' | 'active' | 'success' | 'exception';
  showInfo?: boolean;
  size?: 'default' | 'small';
  strokeWidth?: number;
  className?: string;
}

export interface KeywordProgressProps {
  currentKeyword?: string;
  completedKeywords: string[];
  failedKeywords: string[];
  totalKeywords: number;
  animationDuration?: number;
}

export interface RealtimeStatsProps {
  speed: number; // 条/分钟
  successRate: number; // 百分比
  errorCount: number;
  totalProcessed: number;
  estimatedTimeRemaining: number; // 秒
  showChart?: boolean;
}

export interface ConnectionStatusProps {
  isConnected: boolean;
  connectionState: string;
  latency: number;
  reconnectAttempts: number;
  lastHeartbeat: Date | null;
  messagesReceived: number;
  onReconnect?: () => void;
}

export interface ProgressMonitorProps {
  taskId?: string;
  showAdvanced?: boolean;
  refreshInterval?: number;
  className?: string;
}

// 图表数据类型
export interface ChartDataPoint {
  timestamp: number;
  value: number;
  label?: string;
}

export interface SpeedChart {
  data: ChartDataPoint[];
  maxPoints?: number;
}

// 实时统计数据类型（本地使用）
export interface RealtimeStats {
  speed: number;
  successRate: number;
  errorCount: number;
  totalProcessed: number;
  estimatedTimeRemaining: number;
}

// 动画配置
export interface AnimationConfig {
  duration: number;
  easing: 'linear' | 'ease' | 'ease-in' | 'ease-out' | 'ease-in-out';
  delay?: number;
}

// 主题配置
export interface ProgressTheme {
  primaryColor: string;
  successColor: string;
  errorColor: string;
  warningColor: string;
  textColor: string;
  backgroundColor: string;
  borderRadius: number;
}