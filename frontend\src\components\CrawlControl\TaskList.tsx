import React, { useState } from 'react';
import {
  Card,
  List,
  Tag,
  Progress,
  Button,
  Space,
  Typography,
  Empty,
  Checkbox,
  Dropdown,
  Modal,
  message,
  Badge
} from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
  ReloadOutlined,
  DeleteOutlined,
  MoreOutlined,
  ExclamationCircleOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined
} from '@ant-design/icons';
import type { MenuProps } from 'antd';
import type { TaskInfo, TaskControlActions, BatchOperation } from './types';
import {
  formatTaskStatus,
  calculateTaskProgress,
  formatDuration,
  estimateRemainingTime,
  canPerformAction,
  getConfigSummary
} from './utils';

const { Text, Paragraph } = Typography;

interface TaskListProps {
  tasks: TaskInfo[];
  loading?: boolean;
  actions: TaskControlActions;
  onTaskSelect?: (task: TaskInfo) => void;
  onTaskDelete?: (taskId: string) => void;
  onBatchOperation?: (operation: BatchOperation) => void;
  selectedTaskId?: string;
  showBatchActions?: boolean;
  maxHeight?: number;
}

const TaskList: React.FC<TaskListProps> = ({
  tasks,
  loading = false,
  actions,
  onTaskSelect,
  onTaskDelete,
  onBatchOperation,
  selectedTaskId,
  showBatchActions = true,
  maxHeight = 600
}) => {
  const [selectedTaskIds, setSelectedTaskIds] = useState<string[]>([]);
  const [operationLoading, setOperationLoading] = useState<Record<string, boolean>>({});

  // 处理任务选择
  const handleTaskCheck = (taskId: string, checked: boolean) => {
    if (checked) {
      setSelectedTaskIds(prev => [...prev, taskId]);
    } else {
      setSelectedTaskIds(prev => prev.filter(id => id !== taskId));
    }
  };

  // 全选/取消全选
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedTaskIds(tasks.map(t => t.id));
    } else {
      setSelectedTaskIds([]);
    }
  };

  // 执行任务操作
  const executeTaskAction = async (
    taskId: string,
    action: 'pause' | 'resume' | 'stop' | 'cancel' | 'retry'
  ) => {
    setOperationLoading(prev => ({ ...prev, [taskId]: true }));
    
    try {
      switch (action) {
        case 'pause':
          await actions.pause(taskId);
          message.success('任务已暂停');
          break;
        case 'resume':
          await actions.resume(taskId);
          message.success('任务已恢复');
          break;
        case 'stop':
          await actions.stop(taskId);
          message.success('任务已停止');
          break;
        case 'cancel':
          await actions.cancel(taskId);
          message.success('任务已取消');
          break;
        case 'retry': {
          const newTaskId = await actions.retry(taskId);
          message.success(`任务已重新启动，新任务ID: ${newTaskId}`);
          break;
        }
      }
    } catch (error) {
      message.error(`操作失败: ${(error as Error).message}`);
    } finally {
      setOperationLoading(prev => ({ ...prev, [taskId]: false }));
    }
  };

  // 批量操作
  const handleBatchOperation = (type: BatchOperation['type']) => {
    if (selectedTaskIds.length === 0) {
      message.warning('请先选择要操作的任务');
      return;
    }

    const operation: BatchOperation = {
      type,
      taskIds: selectedTaskIds
    };

    const actionMap = {
      pause: '暂停',
      resume: '恢复',
      stop: '停止',
      cancel: '取消',
      delete: '删除'
    };

    Modal.confirm({
      title: `确认${actionMap[type]}任务？`,
      content: `将${actionMap[type]} ${selectedTaskIds.length} 个任务，此操作不可撤销。`,
      icon: <ExclamationCircleOutlined />,
      okText: '确认',
      cancelText: '取消',
      okButtonProps: { danger: type === 'stop' || type === 'cancel' || type === 'delete' },
      onOk: () => {
        if (onBatchOperation) {
          onBatchOperation(operation);
        }
        setSelectedTaskIds([]);
      }
    });
  };

  // 获取任务操作菜单
  const getTaskMenu = (task: TaskInfo): MenuProps['items'] => {
    const items: MenuProps['items'] = [];

    if (canPerformAction(task, 'pause')) {
      items.push({
        key: 'pause',
        label: '暂停',
        icon: <PauseCircleOutlined />,
        onClick: () => executeTaskAction(task.id, 'pause')
      });
    }

    if (canPerformAction(task, 'resume')) {
      items.push({
        key: 'resume',
        label: '恢复',
        icon: <PlayCircleOutlined />,
        onClick: () => executeTaskAction(task.id, 'resume')
      });
    }

    if (canPerformAction(task, 'stop')) {
      items.push({
        key: 'stop',
        label: '停止',
        icon: <StopOutlined />,
        danger: true,
        onClick: () => executeTaskAction(task.id, 'stop')
      });
    }

    if (canPerformAction(task, 'cancel')) {
      items.push({
        key: 'cancel',
        label: '取消',
        icon: <StopOutlined />,
        danger: true,
        onClick: () => executeTaskAction(task.id, 'cancel')
      });
    }

    if (canPerformAction(task, 'retry')) {
      items.push({
        key: 'retry',
        label: '重新运行',
        icon: <ReloadOutlined />,
        onClick: () => executeTaskAction(task.id, 'retry')
      });
    }

    if (task.status === 'completed' || task.status === 'failed' || task.status === 'cancelled') {
      items.push({
        type: 'divider'
      });
      items.push({
        key: 'delete',
        label: '删除',
        icon: <DeleteOutlined />,
        danger: true,
        onClick: () => {
          Modal.confirm({
            title: '确认删除任务？',
            content: '删除后任务记录将无法恢复。',
            icon: <ExclamationCircleOutlined />,
            okText: '确认删除',
            cancelText: '取消',
            okButtonProps: { danger: true },
            onOk: () => onTaskDelete?.(task.id)
          });
        }
      });
    }

    return items;
  };

  // 批量操作菜单
  const batchMenuItems: MenuProps['items'] = [
    {
      key: 'pause',
      label: '批量暂停',
      icon: <PauseCircleOutlined />,
      onClick: () => handleBatchOperation('pause')
    },
    {
      key: 'resume',
      label: '批量恢复',
      icon: <PlayCircleOutlined />,
      onClick: () => handleBatchOperation('resume')
    },
    {
      key: 'stop',
      label: '批量停止',
      icon: <StopOutlined />,
      danger: true,
      onClick: () => handleBatchOperation('stop')
    },
    {
      key: 'cancel',
      label: '批量取消',
      icon: <StopOutlined />,
      danger: true,
      onClick: () => handleBatchOperation('cancel')
    },
    {
      type: 'divider'
    },
    {
      key: 'delete',
      label: '批量删除',
      icon: <DeleteOutlined />,
      danger: true,
      onClick: () => handleBatchOperation('delete')
    }
  ];

  // 渲染任务项
  const renderTaskItem = (task: TaskInfo) => {
    const { text: statusText, color: statusColor } = formatTaskStatus(task.status);
    const progress = calculateTaskProgress(task);
    const configSummary = getConfigSummary(task.config);
    const isSelected = selectedTaskId === task.id;
    const isChecked = selectedTaskIds.includes(task.id);
    const isLoading = operationLoading[task.id];

    return (
      <List.Item
        key={task.id}
        style={{
          padding: '16px',
          border: isSelected ? '2px solid #1890ff' : '1px solid #f0f0f0',
          borderRadius: '8px',
          marginBottom: '8px',
          backgroundColor: isSelected ? '#f6ffed' : '#fff',
          cursor: 'pointer'
        }}
        onClick={() => onTaskSelect?.(task)}
        actions={[
          <Dropdown
            key="more"
            menu={{ items: getTaskMenu(task) }}
            trigger={['click']}
            placement="bottomRight"
          >
            <Button
              icon={<MoreOutlined />}
              loading={isLoading}
              onClick={(e) => e.stopPropagation()}
            />
          </Dropdown>
        ]}
      >
        <List.Item.Meta
          avatar={
            showBatchActions && (
              <Checkbox
                checked={isChecked}
                onChange={(e) => {
                  e.stopPropagation();
                  handleTaskCheck(task.id, e.target.checked);
                }}
              />
            )
          }
          title={
            <Space>
              <Text strong>{task.name}</Text>
              <Tag color={statusColor}>{statusText}</Tag>
              {task.status === 'running' && (
                <Badge status="processing" text="进行中" />
              )}
            </Space>
          }
          description={
            <div>
              <Paragraph ellipsis={{ rows: 2, expandable: true }}>
                {configSummary.join(' • ')}
              </Paragraph>
              
              {/* 进度信息 */}
              {task.status === 'running' || task.status === 'paused' ? (
                <div style={{ margin: '8px 0' }}>
                  <Progress 
                    percent={progress}
                    size="small"
                    status={task.status === 'paused' ? 'exception' : 'active'}
                    format={() => `${task.progress.currentPage}/${task.progress.totalPages}`}
                  />
                  <Space size="large" style={{ marginTop: '4px', fontSize: '12px', color: '#666' }}>
                    <span>已收集: {task.progress.collectedCount}</span>
                    <span>失败: {task.progress.failedCount}</span>
                    {task.startedAt && task.status === 'running' && (
                      <span>预计剩余: {estimateRemainingTime(task)}</span>
                    )}
                  </Space>
                </div>
              ) : task.status === 'completed' && task.statistics ? (
                <div style={{ margin: '8px 0', fontSize: '12px', color: '#666' }}>
                  <Space size="large">
                    <span>总计: {task.progress.collectedCount} 个</span>
                    <span>用时: {formatDuration(task.startedAt!, task.completedAt)}</span>
                    <span>速度: {task.statistics.speed?.toFixed(1)} 条/分钟</span>
                    <span>成功率: {(task.statistics.successRate * 100).toFixed(1)}%</span>
                  </Space>
                </div>
              ) : null}

              {/* 时间信息 */}
              <div style={{ fontSize: '12px', color: '#999', marginTop: '4px' }}>
                <Space>
                  <span><ClockCircleOutlined /> 创建: {new Date(task.createdAt).toLocaleString()}</span>
                  {task.startedAt && (
                    <span>开始: {new Date(task.startedAt).toLocaleString()}</span>
                  )}
                  {task.completedAt && (
                    <span><CheckCircleOutlined /> 完成: {new Date(task.completedAt).toLocaleString()}</span>
                  )}
                </Space>
              </div>

              {/* 错误信息 */}
              {task.error && (
                <div style={{ marginTop: '8px' }}>
                  <Text type="danger" style={{ fontSize: '12px' }}>
                    错误: {task.error}
                  </Text>
                </div>
              )}
            </div>
          }
        />
      </List.Item>
    );
  };

  return (
    <Card
      title={
        <Space>
          <span>任务列表</span>
          <Badge count={tasks.length} style={{ backgroundColor: '#52c41a' }} />
        </Space>
      }
      extra={
        showBatchActions && tasks.length > 0 && (
          <Space>
            <Checkbox
              indeterminate={selectedTaskIds.length > 0 && selectedTaskIds.length < tasks.length}
              checked={selectedTaskIds.length === tasks.length}
              onChange={(e) => handleSelectAll(e.target.checked)}
            >
              全选
            </Checkbox>
            
            {selectedTaskIds.length > 0 && (
              <Dropdown
                menu={{ items: batchMenuItems }}
                trigger={['click']}
                placement="bottomLeft"
              >
                <Button size="small">
                  批量操作 ({selectedTaskIds.length})
                </Button>
              </Dropdown>
            )}
          </Space>
        )
      }
      bodyStyle={{ padding: 0 }}
    >
      {tasks.length === 0 ? (
        <Empty
          description="暂无任务"
          style={{ padding: '40px 0' }}
        />
      ) : (
        <div style={{ maxHeight, overflowY: 'auto', padding: '16px' }}>
          <List
            dataSource={tasks}
            renderItem={renderTaskItem}
            loading={loading}
            size="small"
          />
        </div>
      )}
    </Card>
  );
};

export default TaskList;