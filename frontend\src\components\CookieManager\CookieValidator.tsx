import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Alert,
  Progress,
  Space,
  Typography,
  List,
  Row,
  Col
} from 'antd';
import {
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  CloseCircleOutlined,
  ReloadOutlined,
  InfoCircleOutlined,
  SecurityScanOutlined
} from '@ant-design/icons';
import type { CookieValidatorProps, Cookie } from './types';
import { validateCookie, validatePDDCookies, formatExpiryTime } from './utils';

const { Text } = Typography;

interface ValidationDetail {
  category: string;
  items: Array<{
    name: string;
    status: 'success' | 'warning' | 'error';
    message: string;
    details?: string;
  }>;
}

const CookieValidator: React.FC<CookieValidatorProps> = ({ 
  cookies, 
  onValidate, 
  loading = false 
}) => {
  const [validating, setValidating] = useState(false);
  const [validationResults, setValidationResults] = useState<ValidationDetail[]>([]);
  const [overallStatus, setOverallStatus] = useState<'success' | 'warning' | 'error' | null>(null);

  // 执行验证
  const performValidation = async () => {
    setValidating(true);
    
    try {
      const results: ValidationDetail[] = [];
      
      // 1. 基础Cookie验证
      const basicValidation = validateBasicCookies(cookies);
      results.push(basicValidation);
      
      // 2. 拼多多专用Cookie验证
      const pddValidation = validatePDDSpecificCookies(cookies);
      results.push(pddValidation);
      
      // 3. 安全性验证
      const securityValidation = validateCookieSecurity(cookies);
      results.push(securityValidation);
      
      // 4. 过期时间验证
      const expiryValidation = validateCookieExpiry(cookies);
      results.push(expiryValidation);
      
      setValidationResults(results);
      
      // 计算整体状态
      const hasError = results.some(r => r.items.some(i => i.status === 'error'));
      const hasWarning = results.some(r => r.items.some(i => i.status === 'warning'));
      
      const status = hasError ? 'error' : hasWarning ? 'warning' : 'success';
      setOverallStatus(status);
      
      // 回调验证结果
      if (onValidate) {
        const pddCheck = validatePDDCookies(cookies);
        onValidate({
          valid: status === 'success' && pddCheck.valid,
          message: getOverallMessage(status, pddCheck),
          details: {
            missingCookies: pddCheck.missing,
            expiredCookies: pddCheck.expired
          }
        });
      }
      
    } catch (error) {
      console.error('验证过程出错:', error);
      setOverallStatus('error');
    } finally {
      setValidating(false);
    }
  };

  // 基础Cookie验证
  const validateBasicCookies = (cookies: Cookie[]): ValidationDetail => {
    const items = [];
    
    // 检查是否有Cookie
    if (cookies.length === 0) {
      items.push({
        name: 'Cookie数量',
        status: 'error' as const,
        message: '没有检测到任何Cookie',
        details: '请确保已正确导入Cookie数据'
      });
    } else {
      items.push({
        name: 'Cookie数量',
        status: 'success' as const,
        message: `检测到 ${cookies.length} 个Cookie`,
        details: '数量正常'
      });
    }
    
    // 检查Cookie格式
    let validCookies = 0;
    let invalidCookies = 0;
    
    cookies.forEach(cookie => {
      const validation = validateCookie(cookie);
      if (validation.valid) {
        validCookies++;
      } else {
        invalidCookies++;
      }
    });
    
    if (invalidCookies > 0) {
      items.push({
        name: 'Cookie格式',
        status: 'warning' as const,
        message: `${invalidCookies} 个Cookie格式有问题`,
        details: `${validCookies} 个有效，${invalidCookies} 个无效`
      });
    } else if (validCookies > 0) {
      items.push({
        name: 'Cookie格式',
        status: 'success' as const,
        message: '所有Cookie格式正确',
        details: `${validCookies} 个Cookie格式验证通过`
      });
    }
    
    return {
      category: '基础验证',
      items
    };
  };

  // 拼多多专用Cookie验证
  const validatePDDSpecificCookies = (cookies: Cookie[]): ValidationDetail => {
    const items: Array<{
      name: string;
      status: 'success' | 'warning' | 'error';
      message: string;
      details?: string;
    }> = [];
    const pddCheck = validatePDDCookies(cookies);
    const requiredCookies = ['PDDAccessToken', 'pdd_user_id'];
    
    requiredCookies.forEach(cookieName => {
      const cookie = cookies.find(c => c.name === cookieName);
      
      if (!cookie) {
        items.push({
          name: cookieName,
          status: 'error' as const,
          message: '缺少必需的Cookie',
          details: '此Cookie对于爬虫功能是必需的'
        });
      } else if (pddCheck.expired.includes(cookieName)) {
        items.push({
          name: cookieName,
          status: 'error' as const,
          message: 'Cookie已过期',
          details: formatExpiryTime(cookie.expires)
        });
      } else {
        items.push({
          name: cookieName,
          status: 'success' as const,
          message: 'Cookie有效',
          details: formatExpiryTime(cookie.expires)
        });
      }
    });
    
    // 检查其他重要Cookie
    const otherImportantCookies = ['pdd_vds', 'PDDAccessToken', 'api_uid'];
    otherImportantCookies.forEach(cookieName => {
      const cookie = cookies.find(c => c.name === cookieName);
      if (cookie && !requiredCookies.includes(cookieName)) {
        items.push({
          name: cookieName,
          status: 'success' as const,
          message: '检测到重要Cookie',
          details: '此Cookie有助于提高爬虫稳定性'
        });
      }
    });
    
    return {
      category: '拼多多专用验证',
      items
    };
  };

  // 安全性验证
  const validateCookieSecurity = (cookies: Cookie[]): ValidationDetail => {
    const items = [];
    
    let secureCount = 0;
    let httpOnlyCount = 0;
    let sameSiteCount = 0;
    
    cookies.forEach(cookie => {
      if (cookie.secure) secureCount++;
      if (cookie.httpOnly) httpOnlyCount++;
      if (cookie.sameSite && cookie.sameSite !== 'None') sameSiteCount++;
    });
    
    // Secure标志检查
    if (secureCount > 0) {
      items.push({
        name: 'Secure标志',
        status: 'success' as const,
        message: `${secureCount} 个Cookie启用了Secure`,
        details: '这些Cookie只能通过HTTPS传输'
      });
    } else {
      items.push({
        name: 'Secure标志',
        status: 'warning' as const,
        message: '没有Cookie启用Secure标志',
        details: '建议在生产环境中启用Secure标志'
      });
    }
    
    // HttpOnly标志检查
    if (httpOnlyCount > 0) {
      items.push({
        name: 'HttpOnly标志',
        status: 'success' as const,
        message: `${httpOnlyCount} 个Cookie启用了HttpOnly`,
        details: '防止JavaScript访问，提高安全性'
      });
    }
    
    // SameSite策略检查
    if (sameSiteCount > 0) {
      items.push({
        name: 'SameSite策略',
        status: 'success' as const,
        message: `${sameSiteCount} 个Cookie设置了SameSite`,
        details: '有助于防止CSRF攻击'
      });
    }
    
    return {
      category: '安全性验证',
      items
    };
  };

  // 过期时间验证
  const validateCookieExpiry = (cookies: Cookie[]): ValidationDetail => {
    const items = [];
    const currentTime = Math.floor(Date.now() / 1000);
    
    let expiredCount = 0;
    let expiringSoonCount = 0;
    let sessionCount = 0;
    let longTermCount = 0;
    
    cookies.forEach(cookie => {
      if (!cookie.expires) {
        sessionCount++;
      } else if (cookie.expires <= currentTime) {
        expiredCount++;
      } else {
        const timeLeft = cookie.expires - currentTime;
        if (timeLeft < 24 * 60 * 60) { // 24小时内过期
          expiringSoonCount++;
        } else if (timeLeft > 365 * 24 * 60 * 60) { // 超过1年
          longTermCount++;
        }
      }
    });
    
    // 过期Cookie检查
    if (expiredCount > 0) {
      items.push({
        name: '过期Cookie',
        status: 'error' as const,
        message: `${expiredCount} 个Cookie已过期`,
        details: '建议重新获取这些Cookie'
      });
    }
    
    // 即将过期Cookie检查
    if (expiringSoonCount > 0) {
      items.push({
        name: '即将过期Cookie',
        status: 'warning' as const,
        message: `${expiringSoonCount} 个Cookie将在24小时内过期`,
        details: '建议及时更新'
      });
    }
    
    // 会话Cookie检查
    if (sessionCount > 0) {
      items.push({
        name: '会话Cookie',
        status: 'success' as const,
        message: `${sessionCount} 个会话Cookie`,
        details: '浏览器关闭时自动删除'
      });
    }
    
    // 长期Cookie检查
    if (longTermCount > 0) {
      items.push({
        name: '长期Cookie',
        status: 'success' as const,
        message: `${longTermCount} 个长期有效Cookie`,
        details: '有效期超过1年'
      });
    }
    
    // 如果没有问题，添加成功项
    if (expiredCount === 0 && expiringSoonCount === 0 && cookies.length > 0) {
      items.push({
        name: '有效期状态',
        status: 'success' as const,
        message: '所有Cookie都在有效期内',
        details: '时间状态良好'
      });
    }
    
    return {
      category: '有效期验证',
      items
    };
  };

  // 获取整体状态消息
  const getOverallMessage = (status: string, pddCheck: { valid: boolean; missing: string[]; expired: string[] }) => {
    if (status === 'success' && pddCheck.valid) {
      return 'Cookie验证通过，爬虫可以正常使用';
    } else if (status === 'error' || !pddCheck.valid) {
      return 'Cookie验证失败，请检查并更新Cookie数据';
    } else {
      return 'Cookie基本可用，但建议优化部分配置';
    }
  };

  // 自动执行验证
  useEffect(() => {
    if (cookies.length > 0) {
      performValidation();
    } else {
      setValidationResults([]);
      setOverallStatus(null);
    }
  }, [cookies]);

  // 渲染验证结果
  const renderValidationResults = () => {
    if (validationResults.length === 0) {
      return null;
    }

    return (
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        {validationResults.map((category, index) => (
          <Card key={index} size="small" title={category.category}>
            <List
              size="small"
              dataSource={category.items}
              renderItem={(item) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={
                      item.status === 'success' ? (
                        <CheckCircleOutlined style={{ color: '#52c41a' }} />
                      ) : item.status === 'warning' ? (
                        <ExclamationCircleOutlined style={{ color: '#faad14' }} />
                      ) : (
                        <CloseCircleOutlined style={{ color: '#f5222d' }} />
                      )
                    }
                    title={
                      <Space>
                        <Text strong>{item.name}</Text>
                        <Text>{item.message}</Text>
                      </Space>
                    }
                    description={item.details}
                  />
                </List.Item>
              )}
            />
          </Card>
        ))}
      </Space>
    );
  };

  // 渲染整体状态
  const renderOverallStatus = () => {
    if (!overallStatus || cookies.length === 0) {
      return null;
    }

    const pddCheck = validatePDDCookies(cookies);
    const message = getOverallMessage(overallStatus, pddCheck);
    
    const alertType = overallStatus === 'success' && pddCheck.valid ? 'success' :
                     overallStatus === 'error' || !pddCheck.valid ? 'error' : 'warning';
    
    const icon = alertType === 'success' ? <CheckCircleOutlined /> :
                alertType === 'error' ? <CloseCircleOutlined /> : <ExclamationCircleOutlined />;

    return (
      <Alert
        message="验证结果"
        description={message}
        type={alertType}
        showIcon
        icon={icon}
        style={{ marginBottom: 16 }}
      />
    );
  };

  // 计算验证进度
  const getValidationProgress = () => {
    if (validationResults.length === 0) return 0;
    
    const totalItems = validationResults.reduce((sum, category) => sum + category.items.length, 0);
    const successItems = validationResults.reduce((sum, category) => 
      sum + category.items.filter(item => item.status === 'success').length, 0
    );
    
    return totalItems > 0 ? Math.round((successItems / totalItems) * 100) : 0;
  };

  return (
    <Card 
      title="Cookie 验证"
      extra={
        <Button 
          icon={<ReloadOutlined />}
          onClick={performValidation}
          loading={validating || loading}
          disabled={cookies.length === 0}
        >
          重新验证
        </Button>
      }
    >
      {cookies.length === 0 ? (
        <Alert
          message="暂无Cookie数据"
          description="请先导入Cookie数据再进行验证。"
          type="info"
          showIcon
          icon={<InfoCircleOutlined />}
        />
      ) : (
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          {/* 整体状态 */}
          {renderOverallStatus()}
          
          {/* 验证进度 */}
          {validationResults.length > 0 && (
            <div>
              <Row gutter={16} style={{ marginBottom: 16 }}>
                <Col span={12}>
                  <Progress 
                    percent={getValidationProgress()} 
                    status={overallStatus === 'error' ? 'exception' : 'normal'}
                    strokeColor={
                      overallStatus === 'success' ? '#52c41a' :
                      overallStatus === 'warning' ? '#faad14' : '#f5222d'
                    }
                  />
                </Col>
                <Col span={12}>
                  <Text type="secondary">
                    验证进度: {getValidationProgress()}% 通过
                  </Text>
                </Col>
              </Row>
            </div>
          )}
          
          {/* 详细验证结果 */}
          {validating ? (
            <div style={{ textAlign: 'center', padding: '20px 0' }}>
              <SecurityScanOutlined style={{ fontSize: '24px', color: '#1890ff' }} />
              <br />
              <Text>正在验证Cookie...</Text>
            </div>
          ) : (
            renderValidationResults()
          )}
        </Space>
      )}
    </Card>
  );
};

export default CookieValidator;