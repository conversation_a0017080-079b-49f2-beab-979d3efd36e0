/**
 * 数据统计组件 - 展示价格分布、品牌统计、销量分析等
 */

import React, { useMemo } from 'react';
import { Card, Row, Col, Statistic, Progress, Tag, List, Skeleton } from 'antd';
import { ShoppingCartOutlined, TagOutlined, DollarOutlined, TrophyOutlined, GiftOutlined, BarChartOutlined } from '@ant-design/icons';
import type { DataStatsProps } from './types';
import { generateDataStats, formatPrice, formatSales } from './utils';

const DataStats: React.FC<DataStatsProps> = ({
  data = [],
  stats: providedStats,
  loading = false
}) => {
  // 计算统计数据
  const stats = useMemo(() => {
    if (providedStats) return providedStats;
    return generateDataStats(data);
  }, [data, providedStats]);

  if (loading) {
    return (
      <div>
        <Row gutter={[16, 16]}>
          {[1, 2, 3, 4].map(i => (
            <Col span={6} key={i}>
              <Card>
                <Skeleton active paragraph={{ rows: 2 }} />
              </Card>
            </Col>
          ))}
        </Row>
      </div>
    );
  }

  // 基础统计卡片
  const BasicStatsCards = () => (
    <Row gutter={[16, 16]}>
      <Col xs={24} sm={12} md={6}>
        <Card>
          <Statistic
            title="商品总数"
            value={stats.totalProducts}
            prefix={<ShoppingCartOutlined />}
            suffix="个"
          />
        </Card>
      </Col>
      
      <Col xs={24} sm={12} md={6}>
        <Card>
          <Statistic
            title="搜索关键词"
            value={stats.keywordStats.length}
            prefix={<TagOutlined />}
            suffix="个"
          />
        </Card>
      </Col>
      
      <Col xs={24} sm={12} md={6}>
        <Card>
          <Statistic
            title="平均价格"
            value={stats.keywordStats.length > 0 
              ? (stats.keywordStats.reduce((sum: number, item: any) => sum + item.avgPrice, 0) / stats.keywordStats.length).toFixed(2)
              : 0
            }
            prefix={<DollarOutlined />}
            suffix="元"
          />
        </Card>
      </Col>
      
      <Col xs={24} sm={12} md={6}>
        <Card>
          <Statistic
            title="补贴商品"
            value={stats.subsidyStats.totalWithSubsidy}
            prefix={<GiftOutlined />}
            suffix={`个 (${stats.subsidyStats.subsidyPercentage.toFixed(1)}%)`}
          />
        </Card>
      </Col>
    </Row>
  );

  // 价格分布图表
  const PriceDistribution = () => (
    <Card title="价格分布" extra={<BarChartOutlined />}>
      <div style={{ padding: '16px 0' }}>
        {stats.priceDistribution.map((item: any, index: number) => (
          <div key={index} style={{ marginBottom: 16 }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 4 }}>
              <span>{item.range}</span>
              <span>{item.count}个 ({item.percentage.toFixed(1)}%)</span>
            </div>
            <Progress
              percent={item.percentage}
              showInfo={false}
              strokeColor={
                index === 0 ? '#52c41a' :
                index === 1 ? '#1890ff' :
                index === 2 ? '#faad14' :
                index === 3 ? '#f5222d' : '#722ed1'
              }
            />
          </div>
        ))}
      </div>
    </Card>
  );

  // 品牌分布
  const BrandDistribution = () => (
    <Card title="品牌分布" extra={<TagOutlined />}>
      <List
        size="small"
        dataSource={stats.brandDistribution.slice(0, 10)}
        renderItem={(item: any, index: number) => (
          <List.Item>
            <div style={{ display: 'flex', justifyContent: 'space-between', width: '100%', alignItems: 'center' }}>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <Tag color={index < 3 ? 'gold' : 'blue'} style={{ marginRight: 8 }}>
                  {index + 1}
                </Tag>
                <span>{item.brand}</span>
              </div>
              <div>
                <span style={{ marginRight: 8, fontWeight: 'bold' }}>{item.count}</span>
                <span style={{ color: '#666', fontSize: '12px' }}>
                  ({item.percentage.toFixed(1)}%)
                </span>
              </div>
            </div>
          </List.Item>
        )}
      />
    </Card>
  );

  // 关键词统计
  const KeywordStats = () => (
    <Card title="关键词统计" extra={<TagOutlined />}>
      <List
        size="small"
        dataSource={stats.keywordStats.sort((a: any, b: any) => b.count - a.count)}
        renderItem={(item: any, _index: number) => (
          <List.Item>
            <div style={{ display: 'flex', justifyContent: 'space-between', width: '100%', alignItems: 'center' }}>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <Tag color="blue" style={{ marginRight: 8 }}>
                  {item.keyword}
                </Tag>
              </div>
              <div style={{ textAlign: 'right' }}>
                <div>{item.count}个商品</div>
                <div style={{ fontSize: '12px', color: '#666' }}>
                  均价: {formatPrice(item.avgPrice)}
                </div>
                <div style={{ fontSize: '12px', color: '#666' }}>
                  价格区间: {formatPrice(item.priceRange[0])} - {formatPrice(item.priceRange[1])}
                </div>
              </div>
            </div>
          </List.Item>
        )}
      />
    </Card>
  );

  // 补贴统计详情
  const SubsidyStats = () => (
    <Card title="补贴统计详情" extra={<GiftOutlined />}>
      <Row gutter={[16, 16]}>
        <Col span={12}>
          <Statistic
            title="百亿补贴商品"
            value={stats.subsidyStats.totalWithSubsidy}
            suffix="个"
            valueStyle={{ color: '#f5222d' }}
          />
          <div style={{ marginTop: 8 }}>
            <Progress
              percent={stats.subsidyStats.subsidyPercentage}
              strokeColor="#f5222d"
              format={percent => `${percent?.toFixed(1)}%`}
            />
          </div>
        </Col>
        
        <Col span={12}>
          <Statistic
            title="政府补贴商品"
            value={stats.subsidyStats.govSubsidyCount}
            suffix="个"
            valueStyle={{ color: '#1890ff' }}
          />
          <div style={{ marginTop: 8 }}>
            <Progress
              percent={stats.subsidyStats.govSubsidyPercentage}
              strokeColor="#1890ff"
              format={percent => `${percent?.toFixed(1)}%`}
            />
          </div>
        </Col>
      </Row>
    </Card>
  );

  // 销量TOP10
  const TopProducts = () => (
    <Card title="销量TOP10" extra={<TrophyOutlined />}>
      <List
        size="small"
        dataSource={stats.salesStats.topProducts.slice(0, 10)}
        renderItem={(item, index) => (
          <List.Item>
            <div style={{ display: 'flex', justifyContent: 'space-between', width: '100%', alignItems: 'center' }}>
              <div style={{ display: 'flex', alignItems: 'center', flex: 1 }}>
                <Tag color={index < 3 ? 'gold' : 'blue'} style={{ marginRight: 8 }}>
                  {index + 1}
                </Tag>
                <div style={{ flex: 1 }}>
                  <div style={{ 
                    fontWeight: 'bold', 
                    marginBottom: 4,
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                    maxWidth: 200
                  }}>
                    {item.goods_name}
                  </div>
                  <div style={{ fontSize: '12px', color: '#666' }}>
                    {formatPrice(item.price)} | {item.brand_name || '未知品牌'}
                  </div>
                </div>
              </div>
              <div style={{ textAlign: 'right' }}>
                <div style={{ fontWeight: 'bold', color: '#f5222d' }}>
                  {formatSales(item.sales)}
                </div>
                {item.subsidy_info && (
                  <Tag color="red">补贴</Tag>
                )}
              </div>
            </div>
          </List.Item>
        )}
      />
    </Card>
  );

  // 销量统计
  const SalesOverview = () => (
    <Card title="销量统计" extra={<BarChartOutlined />}>
      <Row gutter={[16, 16]}>
        <Col span={8}>
          <Statistic
            title="总销量"
            value={stats.salesStats.totalSales}
            formatter={(value) => formatSales(Number(value))}
            valueStyle={{ color: '#52c41a' }}
          />
        </Col>
        <Col span={8}>
          <Statistic
            title="平均销量"
            value={stats.salesStats.avgSales}
            formatter={(value) => formatSales(Number(value))}
            valueStyle={{ color: '#1890ff' }}
          />
        </Col>
        <Col span={8}>
          <Statistic
            title="热销商品"
            value={stats.salesStats.topProducts.length}
            suffix="个"
            valueStyle={{ color: '#faad14' }}
          />
        </Col>
      </Row>
    </Card>
  );

  return (
    <div style={{ padding: '0 0 24px 0' }}>
      {/* 基础统计 */}
      <div style={{ marginBottom: 24 }}>
        <BasicStatsCards />
      </div>

      {/* 详细统计 */}
      <Row gutter={[16, 16]}>
        {/* 左侧列 */}
        <Col xs={24} lg={12}>
          <div style={{ marginBottom: 16 }}>
            <PriceDistribution />
          </div>
          <div style={{ marginBottom: 16 }}>
            <SubsidyStats />
          </div>
          <SalesOverview />
        </Col>

        {/* 右侧列 */}
        <Col xs={24} lg={12}>
          <div style={{ marginBottom: 16 }}>
            <KeywordStats />
          </div>
          <div style={{ marginBottom: 16 }}>
            <BrandDistribution />
          </div>
          <TopProducts />
        </Col>
      </Row>
    </div>
  );
};

export default DataStats;