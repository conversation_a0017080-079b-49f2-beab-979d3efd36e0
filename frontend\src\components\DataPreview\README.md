# DataPreview 数据预览组件

完整的数据预览和导出解决方案，支持实时数据展示、统计分析和多格式导出。

## 功能特性

### 🔍 数据表格 (DataTable)
- **虚拟滚动**: 支持大数据量展示
- **列配置**: 显示/隐藏列，列宽调整
- **排序筛选**: 支持多字段排序和过滤
- **商品图片预览**: 内置图片预览功能
- **链接跳转**: 点击链接直接跳转到商品页面
- **补贴高亮**: 特别突出显示"补贴详情"字段

### 📊 数据统计 (DataStats)
- **基础统计**: 商品总数、关键词数量、平均价格
- **价格分布**: 直观的价格区间分布图表
- **品牌分析**: 品牌分布TOP10统计
- **销量分析**: 销量TOP10和销量统计
- **补贴统计**: 百亿补贴和政府补贴详细统计

### 📥 导出管理 (ExportManager)
- **多格式支持**: Excel (.xlsx) 和 CSV (.csv) 格式
- **自定义字段**: 可选择导出的字段
- **进度显示**: 实时显示导出进度
- **文件命名**: 智能文件命名规则
- **格式一致性**: 确保与后端数据格式100%一致

### 📋 商品详情 (ProductDetail)
- **完整信息**: 显示所有32个字段的详细信息
- **图片预览**: 高清商品图片展示
- **复制功能**: 一键复制商品信息
- **补贴标识**: 清晰显示各种补贴类型

## 组件架构

```
DataPreview/
├── index.tsx           # 主组件入口
├── DataTable.tsx       # 数据表格组件
├── DataStats.tsx       # 数据统计组件
├── ExportManager.tsx   # 导出管理组件
├── ProductDetail.tsx   # 商品详情组件
├── types.ts           # 类型定义
├── utils.ts           # 工具函数
└── README.md          # 文档说明
```

## 使用方法

### 基础使用

```tsx
import DataPreview from './components/DataPreview';

const App = () => {
  const [data, setData] = useState<Product[]>([]);
  const [loading, setLoading] = useState(false);

  const handleExport = async (request: ExportRequest) => {
    // 调用后端导出接口
    const response = await api.export(request);
    return response;
  };

  return (
    <DataPreview
      data={data}
      loading={loading}
      onExport={handleExport}
      onRefresh={() => fetchData()}
    />
  );
};
```

### 高级配置

```tsx
<DataPreview
  data={products}
  loading={false}
  onExport={handleExport}
  exportProgress={exportProgress}
  showTabs={true}
  defaultActiveTab="table"
  height={800}
  onRefresh={refreshData}
/>
```

### 单独使用组件

```tsx
// 只使用数据表格
import { DataTable } from './components/DataPreview';

<DataTable
  data={products}
  onRowClick={handleRowClick}
  filters={filters}
  onFiltersChange={setFilters}
/>

// 只使用统计组件
import { DataStats } from './components/DataPreview';

<DataStats
  data={products}
  loading={false}
/>

// 只使用导出功能
import { ExportManager } from './components/DataPreview';

<ExportManager
  data={products}
  onExport={handleExport}
  exportProgress={progress}
/>
```

## API 集成

### 数据获取接口

```typescript
// GET /api/crawl/{task_id}/preview
interface PreviewResponse {
  success: boolean;
  data: {
    products: Product[];
    total: number;
    stats: DataPreviewStats;
  };
}
```

### 导出接口

```typescript
// POST /api/export/{task_id}
interface ExportRequest {
  format: 'excel' | 'csv';
  filename?: string;
  includeImages?: boolean;
  customFields?: string[];
}

// POST /api/export/{task_id}/csv
// GET /api/export/{task_id}/download
```

## 数据格式

### Product 类型 (32个字段)

```typescript
interface Product {
  // 基础信息
  goods_id: string;           // 商品ID
  goods_name: string;         // 商品名称
  keyword: string;            // 搜索关键词
  
  // 价格信息
  price: number;              // 拼团价
  original_price?: number;    // 原价
  coupon_price?: number;      // 券后价
  market_price?: number;      // 市场价
  price_type_name?: string;   // 价格类型
  
  // 销量与评价
  sales: number | string;     // 销量
  comment_count?: number;     // 评论数
  rating?: number;            // 评分
  sales_tip?: string;         // 销量描述
  
  // 品牌与分类
  brand_name?: string;        // 品牌名称
  category?: string;          // 商品分类
  
  // 补贴信息 (重点字段)
  subsidy_info?: string;      // 补贴详情
  
  // 活动信息
  activity_type_name?: string;    // 活动类型
  merchant_type_name?: string;    // 商家类型
  
  // 标签信息
  marketing_tags?: string;    // 营销标签
  tags?: string;             // 商品标签
  special_text?: string;     // 特殊信息
  
  // 图片与链接
  image_url: string;         // 商品图片
  hd_thumb_url?: string;     // 高清图片
  goods_url: string;         // 商品链接
  
  // 时间戳
  created_time?: string;     // 采集时间
}
```

## 特殊功能说明

### 补贴信息处理

- **subsidy_info** 字段是核心关注点
- 自动识别"百亿补贴"、"国补"、"政府补贴"等关键词
- 在表格和统计中特别标注和统计
- 导出时确保数据准确性

### 导出格式一致性

- 字段映射与后端 `column_mapping` 完全一致
- 支持中文字段名显示
- Excel格式包含格式化和条件格式
- CSV格式使用UTF-8-BOM编码确保中文正常显示

### 性能优化

- 虚拟滚动支持大数据量
- 分页加载减少内存占用
- 图片懒加载和缓存
- 防抖搜索和过滤

## 样式定制

组件使用Ant Design设计系统，支持主题定制：

```less
// 自定义样式
.data-preview {
  .table-row-light {
    background-color: #fafafa;
  }
  
  .table-row-dark {
    background-color: #ffffff;
  }
  
  .subsidy-highlight {
    color: #f5222d;
    font-weight: bold;
  }
}
```

## 注意事项

1. **数据格式**: 确保传入数据符合Product接口定义
2. **导出权限**: 确保用户有相应的导出权限
3. **文件大小**: 大量数据导出时注意文件大小限制
4. **浏览器兼容**: 使用现代浏览器以获得最佳体验
5. **补贴字段**: 特别关注subsidy_info字段的准确性和完整性

## 更新日志

### v1.0.0
- 🎉 初始版本发布
- ✅ 完整的32字段数据支持
- ✅ Excel和CSV导出功能
- ✅ 实时数据统计和可视化
- ✅ 响应式设计和移动端适配
- ✅ 补贴信息特殊处理和高亮显示