import type { KeywordHistory, ConfigPreset, SearchConfig, KeywordTemplate } from './types';

// 多种分隔符的正则表达式
const SEPARATORS_REGEX = /[,，;；\s]+/;

/**
 * 智能解析关键词
 * 支持多种分隔符：中文逗号、英文逗号、空格、分号、换行符
 */
export const parseKeywords = (input: string): string[] => {
  if (!input || typeof input !== 'string') {
    return [];
  }

  return input
    .split(SEPARATORS_REGEX)
    .map(keyword => keyword.trim())
    .filter(keyword => keyword.length > 0)
    .filter((keyword, index, array) => array.indexOf(keyword) === index); // 去重
};

/**
 * 验证关键词
 */
export const validateKeyword = (keyword: string): { valid: boolean; message?: string } => {
  if (!keyword || keyword.trim().length === 0) {
    return { valid: false, message: '关键词不能为空' };
  }

  if (keyword.length > 50) {
    return { valid: false, message: '关键词长度不能超过50个字符' };
  }

  // 检查特殊字符
  const invalidChars = /[<>\"'&]/;
  if (invalidChars.test(keyword)) {
    return { valid: false, message: '关键词包含无效字符' };
  }

  return { valid: true };
};

/**
 * 关键词历史记录管理
 */
export const keywordHistoryManager = {
  STORAGE_KEY: 'pdd_keyword_history',
  MAX_HISTORY: 100,

  // 获取历史记录
  getHistory(): KeywordHistory[] {
    try {
      const history = localStorage.getItem(this.STORAGE_KEY);
      return history ? JSON.parse(history) : [];
    } catch {
      return [];
    }
  },

  // 添加到历史记录
  addToHistory(keywords: string[]): void {
    try {
      const history = this.getHistory();
      const now = new Date().toISOString();

      keywords.forEach(keyword => {
        const existingIndex = history.findIndex(item => item.keyword === keyword);
        
        if (existingIndex >= 0) {
          // 更新现有记录
          history[existingIndex].usedCount += 1;
          history[existingIndex].lastUsed = now;
        } else {
          // 添加新记录
          history.push({
            keyword,
            usedCount: 1,
            lastUsed: now
          });
        }
      });

      // 按使用次数和最后使用时间排序
      history.sort((a, b) => {
        if (a.usedCount !== b.usedCount) {
          return b.usedCount - a.usedCount;
        }
        return new Date(b.lastUsed).getTime() - new Date(a.lastUsed).getTime();
      });

      // 限制历史记录数量
      const trimmedHistory = history.slice(0, this.MAX_HISTORY);
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(trimmedHistory));
    } catch (error) {
      console.error('Failed to save keyword history:', error);
    }
  },

  // 清除历史记录
  clearHistory(): void {
    try {
      localStorage.removeItem(this.STORAGE_KEY);
    } catch (error) {
      console.error('Failed to clear keyword history:', error);
    }
  },

  // 获取热门关键词
  getPopularKeywords(limit: number = 10): string[] {
    const history = this.getHistory();
    return history.slice(0, limit).map(item => item.keyword);
  }
};

/**
 * 配置预设管理
 */
export const configPresetManager = {
  STORAGE_KEY: 'pdd_config_presets',
  MAX_PRESETS: 20,

  // 获取预设列表
  getPresets(): ConfigPreset[] {
    try {
      const presets = localStorage.getItem(this.STORAGE_KEY);
      return presets ? JSON.parse(presets) : [];
    } catch {
      return [];
    }
  },

  // 保存预设
  savePreset(name: string, config: SearchConfig): ConfigPreset {
    try {
      const presets = this.getPresets();
      const now = new Date().toISOString();
      
      const preset: ConfigPreset = {
        id: `preset_${Date.now()}`,
        name,
        config: { ...config },
        createdAt: now,
        updatedAt: now
      };

      presets.unshift(preset);
      
      // 限制预设数量
      const trimmedPresets = presets.slice(0, this.MAX_PRESETS);
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(trimmedPresets));
      
      return preset;
    } catch (error) {
      console.error('Failed to save preset:', error);
      throw error;
    }
  },

  // 删除预设
  deletePreset(id: string): void {
    try {
      const presets = this.getPresets();
      const filteredPresets = presets.filter(preset => preset.id !== id);
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(filteredPresets));
    } catch (error) {
      console.error('Failed to delete preset:', error);
    }
  },

  // 应用预设
  applyPreset(id: string): SearchConfig | null {
    try {
      const presets = this.getPresets();
      const preset = presets.find(p => p.id === id);
      return preset ? { ...preset.config } : null;
    } catch (error) {
      console.error('Failed to apply preset:', error);
      return null;
    }
  }
};

/**
 * 关键词模板管理
 */
export const keywordTemplateManager = {
  // 预定义模板
  getDefaultTemplates(): KeywordTemplate[] {
    return [
      {
        id: 'electronics',
        name: '数码电子',
        category: '数码',
        keywords: ['手机', '耳机', '充电器', '数据线', '手机壳', '移动电源'],
        description: '常用数码电子产品关键词'
      },
      {
        id: 'fashion',
        name: '服装配饰',
        category: '时尚',
        keywords: ['T恤', '连衣裙', '牛仔裤', '运动鞋', '包包', '手表'],
        description: '时尚服装配饰关键词'
      },
      {
        id: 'home',
        name: '家居用品',
        category: '家居',
        keywords: ['毛巾', '床单', '枕头', '收纳盒', '台灯', '花瓶'],
        description: '日常家居用品关键词'
      },
      {
        id: 'beauty',
        name: '美妆护肤',
        category: '美妆',
        keywords: ['口红', '面膜', '洗面奶', '眼霜', '防晒霜', '粉底液'],
        description: '美妆护肤产品关键词'
      }
    ];
  }
};

/**
 * 预估搜索结果和时间
 */
export const estimateSearchMetrics = (keywords: string[], config: SearchConfig) => {
  // 基于关键词数量和配置参数的简单估算
  const avgResultsPerKeyword = 1000;
  const avgTimePerPage = 2; // 秒
  const maxResultsPerKeyword = config.targetCount || 100;
  
  const estimatedResults = Math.min(
    keywords.length * avgResultsPerKeyword,
    keywords.length * maxResultsPerKeyword
  );
  
  const estimatedPages = Math.ceil(estimatedResults / 50); // 假设每页50个结果
  const estimatedTime = Math.ceil((estimatedPages * avgTimePerPage) / 60); // 转换为分钟
  
  return {
    estimatedResults,
    estimatedTime: Math.max(1, estimatedTime) // 至少1分钟
  };
};

/**
 * 导出关键词
 */
export const exportKeywords = (keywords: string[], format: 'txt' | 'csv' | 'json' = 'txt') => {
  let content: string;
  let filename: string;
  let mimeType: string;

  switch (format) {
    case 'csv':
      content = keywords.map(keyword => `"${keyword}"`).join(',');
      filename = `keywords_${Date.now()}.csv`;
      mimeType = 'text/csv';
      break;
    case 'json':
      content = JSON.stringify(keywords, null, 2);
      filename = `keywords_${Date.now()}.json`;
      mimeType = 'application/json';
      break;
    default:
      content = keywords.join('\n');
      filename = `keywords_${Date.now()}.txt`;
      mimeType = 'text/plain';
  }

  const blob = new Blob([content], { type: mimeType });
  const url = URL.createObjectURL(blob);
  
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  URL.revokeObjectURL(url);
};

/**
 * 导入关键词
 */
export const importKeywords = (file: File): Promise<string[]> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    
    reader.onload = (e) => {
      try {
        const content = e.target?.result as string;
        let keywords: string[] = [];

        if (file.type === 'application/json') {
          // JSON 格式
          const parsed = JSON.parse(content);
          keywords = Array.isArray(parsed) ? parsed : [];
        } else if (file.type === 'text/csv' || file.name.endsWith('.csv')) {
          // CSV 格式
          keywords = content.split(',').map(k => k.replace(/"/g, '').trim());
        } else {
          // 文本格式
          keywords = parseKeywords(content);
        }

        // 验证和过滤关键词
        const validKeywords = keywords
          .filter(keyword => validateKeyword(keyword).valid)
          .slice(0, 1000); // 限制最大数量

        resolve(validKeywords);
      } catch (error) {
        reject(new Error('文件格式不正确或内容无效'));
      }
    };

    reader.onerror = () => {
      reject(new Error('文件读取失败'));
    };

    reader.readAsText(file);
  });
};