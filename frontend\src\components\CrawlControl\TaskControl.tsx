import React from 'react';
import {
  But<PERSON>,
  Space,
  Dropdown,
  Modal,
  message,
  Tooltip,
  Popconfirm
} from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
  ReloadOutlined,
  MoreOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import type { MenuProps } from 'antd';
import type { TaskInfo, TaskControlActions } from './types';
import { canPerformAction, formatTaskStatus } from './utils';

interface TaskControlProps {
  task?: TaskInfo;
  loading?: {
    starting?: boolean;
    stopping?: boolean;
    pausing?: boolean;
  };
  disabled?: boolean;
  actions: TaskControlActions;
  onStart?: () => void;
  className?: string;
}

const TaskControl: React.FC<TaskControlProps> = ({
  task,
  loading = {},
  disabled = false,
  actions,
  onStart,
  className
}) => {

  // 处理启动任务
  const handleStart = async () => {
    if (onStart) {
      onStart();
    } else {
      message.info('请先配置搜索参数');
    }
  };

  // 处理暂停任务
  const handlePause = async () => {
    if (!task?.id) return;
    
    try {
      await actions.pause(task.id);
      message.success('任务已暂停');
    } catch (error) {
      message.error('暂停任务失败: ' + (error as Error).message);
    }
  };

  // 处理恢复任务
  const handleResume = async () => {
    if (!task?.id) return;
    
    try {
      await actions.resume(task.id);
      message.success('任务已恢复');
    } catch (error) {
      message.error('恢复任务失败: ' + (error as Error).message);
    }
  };

  // 处理停止任务
  const handleStop = async () => {
    if (!task?.id) return;
    
    try {
      await actions.stop(task.id);
      message.success('任务已停止');
    } catch (error) {
      message.error('停止任务失败: ' + (error as Error).message);
    }
  };

  // 处理取消任务
  const handleCancel = async () => {
    if (!task?.id) return;
    
    try {
      await actions.cancel(task.id);
      message.success('任务已取消');
    } catch (error) {
      message.error('取消任务失败: ' + (error as Error).message);
    }
  };

  // 处理重试任务
  const handleRetry = async () => {
    if (!task?.id) return;
    
    try {
      const newTaskId = await actions.retry(task.id);
      message.success(`任务已重新启动，新任务ID: ${newTaskId}`);
    } catch (error) {
      message.error('重试任务失败: ' + (error as Error).message);
    }
  };

  // 确认对话框
  const showConfirm = (action: string, onConfirm: () => void, title: string, content: string) => {
    Modal.confirm({
      title,
      content,
      icon: <ExclamationCircleOutlined />,
      okText: '确认',
      cancelText: '取消',
      onOk: onConfirm,
      okButtonProps: { danger: action === 'stop' || action === 'cancel' }
    });
  };

  // 更多操作菜单
  const moreMenuItems: MenuProps['items'] = [];

  if (task && canPerformAction(task, 'cancel')) {
    moreMenuItems.push({
      key: 'cancel',
      label: '取消任务',
      icon: <StopOutlined />,
      danger: true,
      onClick: () => showConfirm(
        'cancel',
        handleCancel,
        '确认取消任务？',
        '取消后任务将无法恢复，已收集的数据将保留。'
      )
    });
  }

  if (task && canPerformAction(task, 'retry')) {
    moreMenuItems.push({
      key: 'retry',
      label: '重新运行',
      icon: <ReloadOutlined />,
      onClick: () => showConfirm(
        'retry',
        handleRetry,
        '确认重新运行？',
        '将使用原配置重新启动任务。'
      )
    });
  }

  // 如果没有任务，显示启动按钮
  if (!task) {
    return (
      <Space className={className}>
        <Button
          type="primary"
          size="large"
          icon={<PlayCircleOutlined />}
          loading={loading.starting}
          disabled={disabled}
          onClick={handleStart}
        >
          开始爬取
        </Button>
      </Space>
    );
  }

  const { text: statusText, color: statusColor } = formatTaskStatus(task.status);

  return (
    <Space className={className}>
      {/* 主要操作按钮 */}
      {task.status === 'running' && (
        <>
          <Tooltip title="暂停当前任务">
            <Button
              icon={<PauseCircleOutlined />}
              loading={loading.pausing}
              onClick={handlePause}
            >
              暂停
            </Button>
          </Tooltip>
          
          <Popconfirm
            title="确认停止任务？"
            description="停止后任务无法恢复，已收集的数据将保留。"
            onConfirm={handleStop}
            okText="确认停止"
            cancelText="取消"
            okButtonProps={{ danger: true }}
          >
            <Button
              danger
              icon={<StopOutlined />}
              loading={loading.stopping}
            >
              停止
            </Button>
          </Popconfirm>
        </>
      )}

      {task.status === 'paused' && (
        <>
          <Button
            type="primary"
            icon={<PlayCircleOutlined />}
            loading={loading.starting}
            onClick={handleResume}
          >
            继续
          </Button>
          
          <Popconfirm
            title="确认停止任务？"
            description="停止后任务无法恢复，已收集的数据将保留。"
            onConfirm={handleStop}
            okText="确认停止"
            cancelText="取消"
            okButtonProps={{ danger: true }}
          >
            <Button
              danger
              icon={<StopOutlined />}
              loading={loading.stopping}
            >
              停止
            </Button>
          </Popconfirm>
        </>
      )}

      {task.status === 'pending' && (
        <Button
          danger
          icon={<StopOutlined />}
          onClick={handleCancel}
        >
          取消
        </Button>
      )}

      {(task.status === 'failed' || task.status === 'cancelled') && (
        <Button
          type="primary"
          icon={<ReloadOutlined />}
          onClick={handleRetry}
        >
          重新运行
        </Button>
      )}

      {task.status === 'completed' && (
        <Button
          icon={<ReloadOutlined />}
          onClick={handleRetry}
        >
          再次运行
        </Button>
      )}

      {/* 更多操作 */}
      {moreMenuItems.length > 0 && (
        <Dropdown
          menu={{ items: moreMenuItems }}
          trigger={['click']}
          placement="bottomRight"
        >
          <Button icon={<MoreOutlined />} />
        </Dropdown>
      )}

      {/* 状态显示 */}
      <div style={{ marginLeft: 8, color: '#666', fontSize: '14px' }}>
        状态: <span style={{ 
          color: statusColor === 'processing' ? '#1890ff' : 
                statusColor === 'success' ? '#52c41a' : 
                statusColor === 'error' ? '#ff4d4f' : 
                statusColor === 'warning' ? '#faad14' : '#666'
        }}>
          {statusText}
        </span>
      </div>
    </Space>
  );
};

export default TaskControl;