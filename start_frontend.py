#!/usr/bin/env python3
"""
拼多多爬虫前端启动脚本
自动启动前端开发服务器
"""

import os
import sys
import subprocess
import platform
import time
import webbrowser
from pathlib import Path

def check_node_installed():
    """检查Node.js是否已安装"""
    try:
        result = subprocess.run(['node', '--version'], capture_output=True, text=True)
        version = result.stdout.strip()
        print(f"✅ Node.js已安装: {version}")
        return True
    except FileNotFoundError:
        print("❌ 未检测到Node.js，请先安装Node.js")
        print("下载地址: https://nodejs.org/")
        return False

def check_npm_installed():
    """检查npm是否已安装"""
    try:
        # Windows系统可能需要使用npm.cmd
        npm_cmd = 'npm.cmd' if platform.system() == 'Windows' else 'npm'
        result = subprocess.run([npm_cmd, '--version'], capture_output=True, text=True, shell=True)
        if result.returncode == 0:
            version = result.stdout.strip()
            print(f"✅ npm已安装: {version}")
            return True
        else:
            # 尝试直接使用npm
            result = subprocess.run(['npm', '--version'], capture_output=True, text=True, shell=True)
            if result.returncode == 0:
                version = result.stdout.strip()
                print(f"✅ npm已安装: {version}")
                return True
            else:
                print("❌ 未检测到npm")
                return False
    except Exception as e:
        print(f"❌ npm检查失败: {e}")
        return False

def install_dependencies():
    """安装前端依赖"""
    frontend_dir = Path(__file__).parent / 'frontend'
    
    if not frontend_dir.exists():
        print(f"❌ 前端目录不存在: {frontend_dir}")
        return False
    
    # 检查是否已安装依赖
    node_modules = frontend_dir / 'node_modules'
    if node_modules.exists():
        print("✅ 依赖已安装")
        return True
    
    print("📦 正在安装前端依赖...")
    print("这可能需要几分钟时间，请耐心等待...")
    
    try:
        # 切换到前端目录
        os.chdir(frontend_dir)
        
        # 设置npm镜像（使用淘宝镜像加速）
        npm_cmd = 'npm.cmd' if platform.system() == 'Windows' else 'npm'
        subprocess.run([npm_cmd, 'config', 'set', 'registry', 'https://registry.npmmirror.com'], shell=True)
        
        # 安装依赖
        result = subprocess.run([npm_cmd, 'install'], check=True, shell=True)
        print("✅ 依赖安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖安装失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 发生错误: {e}")
        return False

def start_frontend_server():
    """启动前端开发服务器"""
    frontend_dir = Path(__file__).parent / 'frontend'
    
    if not frontend_dir.exists():
        print(f"❌ 前端目录不存在: {frontend_dir}")
        return False
    
    print("\n🚀 正在启动前端开发服务器...")
    print("=" * 50)
    
    try:
        # 切换到前端目录
        os.chdir(frontend_dir)
        
        # 显示启动信息
        print("📍 工作目录:", frontend_dir)
        print("🌐 前端地址: http://localhost:5173")
        print("🔌 API代理: http://localhost:8001/api")
        print("📡 WebSocket: ws://localhost:8001/ws")
        print("=" * 50)
        print("\n按 Ctrl+C 停止服务器\n")
        
        # 根据操作系统选择命令
        if platform.system() == 'Windows':
            # Windows系统
            cmd = 'npm run dev'
        else:
            # Linux/Mac系统
            cmd = 'npm run dev'
        
        # 启动开发服务器
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1,
            universal_newlines=True,
            shell=True
        )
        
        # 等待服务器启动
        time.sleep(3)
        
        # 自动打开浏览器
        print("\n🌐 正在打开浏览器...")
        webbrowser.open('http://localhost:5173')
        
        # 实时输出日志
        for line in process.stdout:
            print(line, end='')
        
        # 等待进程结束
        process.wait()
        
    except KeyboardInterrupt:
        print("\n\n👋 正在停止前端服务器...")
        if 'process' in locals():
            process.terminate()
            process.wait()
        print("✅ 前端服务器已停止")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 启动失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 发生错误: {e}")
        return False

def check_backend_status():
    """检查后端服务状态"""
    import socket
    
    def is_port_open(host, port):
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0
    
    if is_port_open('localhost', 8001):
        print("✅ 后端服务已运行在端口 8001")
        return True
    else:
        print("⚠️  后端服务未运行")
        print("提示: 请先运行 python start_backend.py 启动后端服务")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("🕷️  拼多多爬虫前端启动脚本")
    print("=" * 50)
    
    # 检查Node.js环境
    if not check_node_installed():
        sys.exit(1)
    
    if not check_npm_installed():
        sys.exit(1)
    
    # 检查后端状态
    check_backend_status()
    print()
    
    # 安装依赖
    if not install_dependencies():
        print("❌ 依赖安装失败，无法启动前端")
        sys.exit(1)
    
    # 启动前端服务器
    start_frontend_server()
    
    print("\n👋 感谢使用！")

if __name__ == "__main__":
    main()