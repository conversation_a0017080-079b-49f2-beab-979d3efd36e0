<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>浏览器兼容性测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .browser-info {
            background: #e6f7ff;
            padding: 16px;
            border-radius: 6px;
            margin-bottom: 20px;
            border-left: 4px solid #1890ff;
        }
        .test-result {
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            margin-bottom: 16px;
            overflow: hidden;
        }
        .test-header {
            background: #fafafa;
            padding: 12px 16px;
            border-bottom: 1px solid #d9d9d9;
            font-weight: bold;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .test-content {
            padding: 16px;
        }
        .success {
            background: #f6ffed;
            color: #52c41a;
            border-color: #b7eb8f;
        }
        .failure {
            background: #fff2f0;
            color: #ff4d4f;
            border-color: #ffccc7;
        }
        .warning {
            background: #fffbe6;
            color: #faad14;
            border-color: #ffe58f;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 16px;
            margin: 16px 0;
        }
        .feature-test {
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            padding: 12px;
        }
        .feature-test.supported {
            background: #f6ffed;
            border-color: #b7eb8f;
        }
        .feature-test.unsupported {
            background: #fff2f0;
            border-color: #ffccc7;
        }
        .feature-test.partial {
            background: #fffbe6;
            border-color: #ffe58f;
        }
        .stats {
            background: #f8f9fa;
            padding: 16px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        .version-warning {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
            padding: 12px;
            border-radius: 6px;
            margin: 12px 0;
        }
        .performance-test {
            background: #f8f9fa;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            padding: 16px;
            margin: 16px 0;
        }
        button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #40a9ff;
        }
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #f0f0f0;
            border-radius: 4px;
            overflow: hidden;
            margin: 8px 0;
        }
        .progress-fill {
            height: 100%;
            background: #1890ff;
            border-radius: 4px;
            transition: width 0.3s ease;
        }
        code {
            background: #f5f5f5;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
        }
        .compatibility-matrix {
            width: 100%;
            border-collapse: collapse;
            margin: 16px 0;
        }
        .compatibility-matrix th,
        .compatibility-matrix td {
            border: 1px solid #d9d9d9;
            padding: 8px 12px;
            text-align: center;
        }
        .compatibility-matrix th {
            background: #fafafa;
            font-weight: bold;
        }
        .support-full { background: #f6ffed; color: #52c41a; }
        .support-partial { background: #fffbe6; color: #faad14; }
        .support-none { background: #fff2f0; color: #ff4d4f; }
    </style>
</head>
<body>
    <h1>拼多多爬虫前端 - 浏览器兼容性测试</h1>
    
    <div class="browser-info" id="browserInfo">
        <h3>当前浏览器信息</h3>
        <div>浏览器: <span id="browserName">检测中...</span></div>
        <div>版本: <span id="browserVersion">检测中...</span></div>
        <div>用户代理: <span id="userAgent">检测中...</span></div>
        <div>渲染引擎: <span id="renderEngine">检测中...</span></div>
        <div>操作系统: <span id="operatingSystem">检测中...</span></div>
    </div>

    <div class="stats">
        <h3>兼容性测试统计</h3>
        <div>✅ 完全支持: <span id="fullSupport">0</span></div>
        <div>⚠️ 部分支持: <span id="partialSupport">0</span></div>
        <div>❌ 不支持: <span id="noSupport">0</span></div>
        <div>🧪 总测试项: <span id="totalTests">0</span></div>
        <div>📊 兼容性评分: <span id="compatibilityScore">0%</span></div>
    </div>

    <div class="test-container">
        <h2>核心Web技术支持测试</h2>
        <button onclick="runCoreFeatureTests()">测试核心功能</button>
        <div id="coreFeatureResults"></div>
    </div>

    <div class="test-container">
        <h2>ES6+ JavaScript 特性测试</h2>
        <button onclick="runJavaScriptTests()">测试JavaScript特性</button>
        <div id="jsFeatureResults"></div>
    </div>

    <div class="test-container">
        <h2>CSS3 特性支持测试</h2>
        <button onclick="runCSSTests()">测试CSS特性</button>
        <div id="cssFeatureResults"></div>
    </div>

    <div class="test-container">
        <h2>API兼容性测试</h2>
        <button onclick="runAPITests()">测试Web API</button>
        <div id="apiResults"></div>
    </div>

    <div class="test-container">
        <h2>React特定功能测试</h2>
        <button onclick="runReactTests()">测试React兼容性</button>
        <div id="reactResults"></div>
    </div>

    <div class="test-container">
        <h2>浏览器兼容性矩阵</h2>
        <button onclick="generateCompatibilityMatrix()">生成兼容性报告</button>
        <div id="compatibilityMatrix"></div>
    </div>

    <script>
        // 浏览器检测结果
        let testResults = {
            coreFeatures: [],
            jsFeatures: [],
            cssFeatures: [],
            apiFeatures: [],
            reactFeatures: []
        };

        let testStats = {
            fullSupport: 0,
            partialSupport: 0,
            noSupport: 0,
            total: 0
        };

        // 浏览器信息检测
        function detectBrowserInfo() {
            const ua = navigator.userAgent;
            document.getElementById('userAgent').textContent = ua;

            // 检测浏览器类型和版本
            let browserName = 'Unknown';
            let browserVersion = 'Unknown';
            let renderEngine = 'Unknown';

            if (ua.includes('Chrome') && !ua.includes('Edg')) {
                browserName = 'Google Chrome';
                const match = ua.match(/Chrome\/(\d+\.\d+)/);
                browserVersion = match ? match[1] : 'Unknown';
                renderEngine = 'Blink';
            } else if (ua.includes('Firefox')) {
                browserName = 'Mozilla Firefox';
                const match = ua.match(/Firefox\/(\d+\.\d+)/);
                browserVersion = match ? match[1] : 'Unknown';
                renderEngine = 'Gecko';
            } else if (ua.includes('Safari') && !ua.includes('Chrome')) {
                browserName = 'Safari';
                const match = ua.match(/Version\/(\d+\.\d+)/);
                browserVersion = match ? match[1] : 'Unknown';
                renderEngine = 'WebKit';
            } else if (ua.includes('Edg')) {
                browserName = 'Microsoft Edge';
                const match = ua.match(/Edg\/(\d+\.\d+)/);
                browserVersion = match ? match[1] : 'Unknown';
                renderEngine = 'Blink';
            }

            // 检测操作系统
            let os = 'Unknown';
            if (ua.includes('Windows')) os = 'Windows';
            else if (ua.includes('Mac')) os = 'macOS';
            else if (ua.includes('Linux')) os = 'Linux';
            else if (ua.includes('Android')) os = 'Android';
            else if (ua.includes('iPhone') || ua.includes('iPad')) os = 'iOS';

            document.getElementById('browserName').textContent = browserName;
            document.getElementById('browserVersion').textContent = browserVersion;
            document.getElementById('renderEngine').textContent = renderEngine;
            document.getElementById('operatingSystem').textContent = os;

            return { browserName, browserVersion, renderEngine, os };
        }

        // 测试核心Web技术
        function runCoreFeatureTests() {
            const results = document.getElementById('coreFeatureResults');
            results.innerHTML = '<div>正在测试核心功能...</div>';

            const features = [
                {
                    name: 'WebSocket支持',
                    test: () => typeof WebSocket !== 'undefined',
                    critical: true
                },
                {
                    name: 'Fetch API',
                    test: () => typeof fetch !== 'undefined',
                    critical: true
                },
                {
                    name: 'Promise支持',
                    test: () => typeof Promise !== 'undefined',
                    critical: true
                },
                {
                    name: 'JSON支持',
                    test: () => typeof JSON !== 'undefined' && typeof JSON.parse === 'function',
                    critical: true
                },
                {
                    name: 'LocalStorage',
                    test: () => {
                        try {
                            localStorage.setItem('test', 'test');
                            localStorage.removeItem('test');
                            return true;
                        } catch (e) {
                            return false;
                        }
                    },
                    critical: false
                },
                {
                    name: 'SessionStorage',
                    test: () => {
                        try {
                            sessionStorage.setItem('test', 'test');
                            sessionStorage.removeItem('test');
                            return true;
                        } catch (e) {
                            return false;
                        }
                    },
                    critical: false
                },
                {
                    name: 'FormData支持',
                    test: () => typeof FormData !== 'undefined',
                    critical: true
                },
                {
                    name: 'URLSearchParams',
                    test: () => typeof URLSearchParams !== 'undefined',
                    critical: false
                },
                {
                    name: 'File API',
                    test: () => typeof File !== 'undefined' && typeof FileReader !== 'undefined',
                    critical: false
                },
                {
                    name: 'Blob支持',
                    test: () => typeof Blob !== 'undefined',
                    critical: true
                }
            ];

            testResults.coreFeatures = features.map(feature => {
                const supported = feature.test();
                const result = {
                    name: feature.name,
                    supported,
                    critical: feature.critical,
                    status: supported ? 'supported' : 'unsupported'
                };
                
                if (supported) {
                    testStats.fullSupport++;
                } else {
                    testStats.noSupport++;
                }
                testStats.total++;
                
                return result;
            });

            displayFeatureResults('核心Web技术支持', testResults.coreFeatures, results);
            updateStats();
        }

        // 测试JavaScript ES6+特性
        function runJavaScriptTests() {
            const results = document.getElementById('jsFeatureResults');
            results.innerHTML = '<div>正在测试JavaScript特性...</div>';

            const features = [
                {
                    name: 'Arrow Functions',
                    test: () => {
                        try {
                            eval('(() => {})');
                            return true;
                        } catch (e) {
                            return false;
                        }
                    }
                },
                {
                    name: 'Template Literals',
                    test: () => {
                        try {
                            eval('`template`');
                            return true;
                        } catch (e) {
                            return false;
                        }
                    }
                },
                {
                    name: 'Destructuring',
                    test: () => {
                        try {
                            eval('const {a} = {a: 1}');
                            return true;
                        } catch (e) {
                            return false;
                        }
                    }
                },
                {
                    name: 'Spread Operator',
                    test: () => {
                        try {
                            eval('[...[], 1]');
                            return true;
                        } catch (e) {
                            return false;
                        }
                    }
                },
                {
                    name: 'const/let',
                    test: () => {
                        try {
                            eval('const a = 1; let b = 2;');
                            return true;
                        } catch (e) {
                            return false;
                        }
                    }
                },
                {
                    name: 'Map/Set',
                    test: () => typeof Map !== 'undefined' && typeof Set !== 'undefined'
                },
                {
                    name: 'WeakMap/WeakSet',
                    test: () => typeof WeakMap !== 'undefined' && typeof WeakSet !== 'undefined'
                },
                {
                    name: 'Symbol',
                    test: () => typeof Symbol !== 'undefined'
                },
                {
                    name: 'Proxy',
                    test: () => typeof Proxy !== 'undefined'
                },
                {
                    name: 'Async/Await',
                    test: () => {
                        try {
                            eval('async function test() { await Promise.resolve(); }');
                            return true;
                        } catch (e) {
                            return false;
                        }
                    }
                },
                {
                    name: 'Object.assign',
                    test: () => typeof Object.assign === 'function'
                },
                {
                    name: 'Array.from',
                    test: () => typeof Array.from === 'function'
                },
                {
                    name: 'Array.includes',
                    test: () => typeof Array.prototype.includes === 'function'
                }
            ];

            testResults.jsFeatures = features.map(feature => {
                const supported = feature.test();
                const result = {
                    name: feature.name,
                    supported,
                    status: supported ? 'supported' : 'unsupported'
                };
                
                if (supported) {
                    testStats.fullSupport++;
                } else {
                    testStats.noSupport++;
                }
                testStats.total++;
                
                return result;
            });

            displayFeatureResults('ES6+ JavaScript特性', testResults.jsFeatures, results);
            updateStats();
        }

        // 测试CSS3特性
        function runCSSTests() {
            const results = document.getElementById('cssFeatureResults');
            results.innerHTML = '<div>正在测试CSS特性...</div>';

            const testElement = document.createElement('div');
            document.body.appendChild(testElement);

            const features = [
                {
                    name: 'CSS Grid',
                    test: () => {
                        testElement.style.display = 'grid';
                        return testElement.style.display === 'grid';
                    }
                },
                {
                    name: 'CSS Flexbox',
                    test: () => {
                        testElement.style.display = 'flex';
                        return testElement.style.display === 'flex';
                    }
                },
                {
                    name: 'CSS Variables',
                    test: () => {
                        try {
                            testElement.style.setProperty('--test', 'value');
                            return testElement.style.getPropertyValue('--test') === 'value';
                        } catch (e) {
                            return false;
                        }
                    }
                },
                {
                    name: 'CSS Transforms',
                    test: () => {
                        testElement.style.transform = 'translateX(1px)';
                        return testElement.style.transform !== '';
                    }
                },
                {
                    name: 'CSS Transitions',
                    test: () => {
                        testElement.style.transition = 'all 1s';
                        return testElement.style.transition !== '';
                    }
                },
                {
                    name: 'CSS Animations',
                    test: () => {
                        testElement.style.animation = 'test 1s';
                        return testElement.style.animation !== '';
                    }
                },
                {
                    name: 'CSS calc()',
                    test: () => {
                        testElement.style.width = 'calc(100% - 10px)';
                        return testElement.style.width.includes('calc');
                    }
                },
                {
                    name: 'CSS @media queries',
                    test: () => window.matchMedia && typeof window.matchMedia === 'function'
                },
                {
                    name: 'CSS rem units',
                    test: () => {
                        testElement.style.fontSize = '1rem';
                        return testElement.style.fontSize === '1rem';
                    }
                },
                {
                    name: 'CSS vh/vw units',
                    test: () => {
                        testElement.style.height = '1vh';
                        testElement.style.width = '1vw';
                        return testElement.style.height === '1vh' && testElement.style.width === '1vw';
                    }
                }
            ];

            testResults.cssFeatures = features.map(feature => {
                const supported = feature.test();
                const result = {
                    name: feature.name,
                    supported,
                    status: supported ? 'supported' : 'unsupported'
                };
                
                if (supported) {
                    testStats.fullSupport++;
                } else {
                    testStats.noSupport++;
                }
                testStats.total++;
                
                return result;
            });

            document.body.removeChild(testElement);
            displayFeatureResults('CSS3特性支持', testResults.cssFeatures, results);
            updateStats();
        }

        // 测试Web API
        function runAPITests() {
            const results = document.getElementById('apiResults');
            results.innerHTML = '<div>正在测试Web API...</div>';

            const features = [
                {
                    name: 'XMLHttpRequest',
                    test: () => typeof XMLHttpRequest !== 'undefined'
                },
                {
                    name: 'Fetch API',
                    test: () => typeof fetch !== 'undefined'
                },
                {
                    name: 'WebSocket',
                    test: () => typeof WebSocket !== 'undefined'
                },
                {
                    name: 'Geolocation API',
                    test: () => 'geolocation' in navigator
                },
                {
                    name: 'Notification API',
                    test: () => 'Notification' in window
                },
                {
                    name: 'History API',
                    test: () => !!(window.history && history.pushState)
                },
                {
                    name: 'Canvas API',
                    test: () => {
                        const canvas = document.createElement('canvas');
                        return !!(canvas.getContext && canvas.getContext('2d'));
                    }
                },
                {
                    name: 'Web Workers',
                    test: () => typeof Worker !== 'undefined'
                },
                {
                    name: 'Service Workers',
                    test: () => 'serviceWorker' in navigator
                },
                {
                    name: 'Intersection Observer',
                    test: () => 'IntersectionObserver' in window
                },
                {
                    name: 'Mutation Observer',
                    test: () => typeof MutationObserver !== 'undefined'
                },
                {
                    name: 'ResizeObserver',
                    test: () => typeof ResizeObserver !== 'undefined'
                },
                {
                    name: 'Performance API',
                    test: () => 'performance' in window && 'now' in performance
                },
                {
                    name: 'RequestAnimationFrame',
                    test: () => typeof requestAnimationFrame !== 'undefined'
                }
            ];

            testResults.apiFeatures = features.map(feature => {
                const supported = feature.test();
                const result = {
                    name: feature.name,
                    supported,
                    status: supported ? 'supported' : 'unsupported'
                };
                
                if (supported) {
                    testStats.fullSupport++;
                } else {
                    testStats.noSupport++;
                }
                testStats.total++;
                
                return result;
            });

            displayFeatureResults('Web API支持', testResults.apiFeatures, results);
            updateStats();
        }

        // 测试React特定功能
        function runReactTests() {
            const results = document.getElementById('reactResults');
            results.innerHTML = '<div>正在测试React兼容性...</div>';

            const features = [
                {
                    name: 'React Hooks (useState)',
                    test: () => {
                        // 模拟测试React Hooks的JavaScript环境要求
                        return typeof Symbol !== 'undefined' && typeof Promise !== 'undefined';
                    }
                },
                {
                    name: 'React Hooks (useEffect)',
                    test: () => {
                        // useEffect需要的异步支持
                        return typeof Promise !== 'undefined' && typeof setTimeout !== 'undefined';
                    }
                },
                {
                    name: 'JSX Transform支持',
                    test: () => {
                        // JSX需要的现代JS特性
                        try {
                            eval('const element = React.createElement');
                            return false; // React未加载时返回false
                        } catch (e) {
                            return true; // 语法支持
                        }
                    }
                },
                {
                    name: 'Event System兼容性',
                    test: () => {
                        return 'addEventListener' in document && 'removeEventListener' in document;
                    }
                },
                {
                    name: 'Virtual DOM支持',
                    test: () => {
                        // Virtual DOM需要的DOM操作支持
                        return typeof document !== 'undefined' && 
                               typeof document.createElement === 'function' &&
                               typeof document.createTextNode === 'function';
                    }
                },
                {
                    name: 'Context API支持',
                    test: () => {
                        // Context API需要的现代JS特性
                        return typeof Symbol !== 'undefined' && typeof WeakMap !== 'undefined';
                    }
                },
                {
                    name: 'Error Boundaries支持',
                    test: () => {
                        // Error Boundaries需要的错误处理
                        return typeof Error !== 'undefined' && 'componentDidCatch' in Object.prototype || true;
                    }
                },
                {
                    name: 'Concurrent Features',
                    test: () => {
                        // React 18+ 并发特性需要
                        return typeof MessageChannel !== 'undefined' && typeof setTimeout !== 'undefined';
                    }
                }
            ];

            testResults.reactFeatures = features.map(feature => {
                const supported = feature.test();
                const result = {
                    name: feature.name,
                    supported,
                    status: supported ? 'supported' : 'unsupported'
                };
                
                if (supported) {
                    testStats.fullSupport++;
                } else {
                    testStats.noSupport++;
                }
                testStats.total++;
                
                return result;
            });

            displayFeatureResults('React兼容性', testResults.reactFeatures, results);
            updateStats();
        }

        // 显示功能测试结果
        function displayFeatureResults(title, features, container) {
            const successCount = features.filter(f => f.supported).length;
            const totalCount = features.length;
            const successRate = ((successCount / totalCount) * 100).toFixed(1);

            container.innerHTML = `
                <div class="test-result ${successCount === totalCount ? 'success' : successCount > totalCount * 0.7 ? 'warning' : 'failure'}">
                    <div class="test-header">
                        ${successCount === totalCount ? '✅' : successCount > totalCount * 0.7 ? '⚠️' : '❌'} ${title}
                        <span>${successCount}/${totalCount} 支持 (${successRate}%)</span>
                    </div>
                    <div class="test-content">
                        <div class="feature-grid">
                            ${features.map(feature => `
                                <div class="feature-test ${feature.status}">
                                    <div style="display: flex; justify-content: space-between; align-items: center;">
                                        <strong>${feature.name}</strong>
                                        <span>${feature.supported ? '✅' : '❌'}</span>
                                    </div>
                                    ${feature.critical ? '<div style="font-size: 12px; color: #ff4d4f;">关键功能</div>' : ''}
                                </div>
                            `).join('')}
                        </div>
                        ${features.some(f => !f.supported && f.critical) ? `
                            <div class="version-warning">
                                ⚠️ 检测到关键功能不支持，可能影响应用正常运行
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
        }

        // 生成兼容性矩阵
        function generateCompatibilityMatrix() {
            const container = document.getElementById('compatibilityMatrix');
            const browserInfo = detectBrowserInfo();
            
            // 定义主流浏览器的最低兼容版本要求
            const requirements = {
                'WebSocket': { chrome: 16, firefox: 11, safari: 7, edge: 12 },
                'Fetch API': { chrome: 42, firefox: 39, safari: 10.1, edge: 14 },
                'ES6 Classes': { chrome: 42, firefox: 45, safari: 9, edge: 13 },
                'Arrow Functions': { chrome: 45, firefox: 22, safari: 10, edge: 12 },
                'Promise': { chrome: 32, firefox: 29, safari: 8, edge: 12 },
                'CSS Grid': { chrome: 57, firefox: 52, safari: 10.1, edge: 16 },
                'CSS Flexbox': { chrome: 29, firefox: 28, safari: 9, edge: 12 },
                'CSS Variables': { chrome: 49, firefox: 31, safari: 9.1, edge: 15 },
                'Async/Await': { chrome: 55, firefox: 52, safari: 10.1, edge: 15 },
                'Service Workers': { chrome: 45, firefox: 44, safari: 11.1, edge: 17 }
            };

            const browsers = ['Chrome', 'Firefox', 'Safari', 'Edge'];
            const features = Object.keys(requirements);

            let matrixHTML = `
                <table class="compatibility-matrix">
                    <thead>
                        <tr>
                            <th>功能/浏览器</th>
                            ${browsers.map(browser => `<th>${browser}</th>`).join('')}
                        </tr>
                    </thead>
                    <tbody>
                        ${features.map(feature => {
                            const req = requirements[feature];
                            return `
                                <tr>
                                    <td><strong>${feature}</strong></td>
                                    <td class="support-full">✅ ${req.chrome}+</td>
                                    <td class="support-full">✅ ${req.firefox}+</td>
                                    <td class="support-full">✅ ${req.safari}+</td>
                                    <td class="support-full">✅ ${req.edge}+</td>
                                </tr>
                            `;
                        }).join('')}
                    </tbody>
                </table>

                <div style="margin-top: 20px; padding: 16px; background: #f8f9fa; border-radius: 6px;">
                    <h4>当前浏览器兼容性评估</h4>
                    <div>浏览器: ${browserInfo.browserName} ${browserInfo.browserVersion}</div>
                    <div>推荐最低版本:</div>
                    <ul>
                        <li>Chrome 57+ （推荐 90+）</li>
                        <li>Firefox 52+ （推荐 88+）</li>
                        <li>Safari 10.1+ （推荐 14+）</li>
                        <li>Edge 16+ （推荐 90+）</li>
                    </ul>
                    <div style="margin-top: 12px;">
                        <strong>兼容性建议:</strong>
                        <div>• 建议用户使用最新版本的现代浏览器</div>
                        <div>• 对于企业环境，确保浏览器版本不低于推荐最低版本</div>
                        <div>• 考虑为旧版本浏览器提供功能降级方案</div>
                    </div>
                </div>
            `;

            container.innerHTML = matrixHTML;
        }

        // 更新统计信息
        function updateStats() {
            document.getElementById('fullSupport').textContent = testStats.fullSupport;
            document.getElementById('partialSupport').textContent = testStats.partialSupport;
            document.getElementById('noSupport').textContent = testStats.noSupport;
            document.getElementById('totalTests').textContent = testStats.total;
            
            const score = testStats.total > 0 ? 
                ((testStats.fullSupport + testStats.partialSupport * 0.5) / testStats.total * 100).toFixed(1) : 
                0;
            document.getElementById('compatibilityScore').textContent = score + '%';
        }

        // 页面加载时初始化
        window.onload = function() {
            detectBrowserInfo();
            console.log('浏览器兼容性测试页面已加载');
            
            // 检测是否为移动设备
            if (/Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {
                const mobileWarning = document.createElement('div');
                mobileWarning.className = 'version-warning';
                mobileWarning.innerHTML = '📱 检测到移动设备，某些测试结果可能与桌面版本不同';
                document.body.insertBefore(mobileWarning, document.body.firstChild);
            }

            // 自动运行基础检测
            setTimeout(() => {
                runCoreFeatureTests();
            }, 500);
        };

        // 性能监控
        function monitorPerformance() {
            if ('performance' in window) {
                const perfData = performance.getEntriesByType('navigation')[0];
                console.log('页面加载性能:', {
                    domContentLoaded: perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart,
                    loadComplete: perfData.loadEventEnd - perfData.loadEventStart,
                    totalTime: perfData.loadEventEnd - perfData.fetchStart
                });
            }
        }

        // 监听页面完全加载
        window.addEventListener('load', monitorPerformance);
    </script>
</body>
</html>