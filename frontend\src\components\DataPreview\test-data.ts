/**
 * 测试数据 - 用于验证DataPreview组件功能
 */
import type { Product } from '../../types';

export const mockProducts: Product[] = [
  {
    goods_id: '1001',
    goods_name: '【百亿补贴】Apple iPhone 15 Pro Max 256GB 钛原色',
    keyword: '手机',
    price: 8699,
    original_price: 9999,
    coupon_price: 8499,
    market_price: 9999,
    price_type_name: '拼团价',
    sales: '50000+',
    comment_count: 15678,
    rating: 4.9,
    sales_tip: '月销5万+',
    brand_name: 'Apple',
    category: '手机通讯',
    subsidy_info: '百亿补贴',
    activity_type_name: '百亿补贴',
    merchant_type_name: '品牌官方旗舰店',
    marketing_tags: '百亿补贴,官方正品,顺丰包邮',
    tags: '5G手机,钛合金,A17Pro芯片',
    special_text: '官方正品保证',
    image_url: 'https://img.pddpic.com/mms-material-img/2023-09-13/example1.jpg',
    hd_thumb_url: 'https://img.pddpic.com/mms-material-img/2023-09-13/example1_hd.jpg',
    goods_url: 'https://mobile.yangkeduo.com/goods.html?goods_id=1001',
    created_time: '2024-08-01T10:30:00Z'
  },
  {
    goods_id: '1002',
    goods_name: '小米14 Ultra 16GB+1TB 钛原色 徕卡专业影像',
    keyword: '手机',
    price: 6299,
    original_price: 6999,
    coupon_price: 6199,
    market_price: 6999,
    price_type_name: '拼团价',
    sales: '23000+',
    comment_count: 8945,
    rating: 4.7,
    sales_tip: '月销2.3万+',
    brand_name: '小米',
    category: '手机通讯',
    subsidy_info: '优惠券可用',
    activity_type_name: '限时优惠',
    merchant_type_name: '小米官方旗舰店',
    marketing_tags: '徕卡影像,骁龙8Gen3,120W快充',
    tags: '5G手机,大电池,专业拍照',
    special_text: '送充电器+保护壳',
    image_url: 'https://img.pddpic.com/mms-material-img/2023-09-13/example2.jpg',
    hd_thumb_url: 'https://img.pddpic.com/mms-material-img/2023-09-13/example2_hd.jpg',
    goods_url: 'https://mobile.yangkeduo.com/goods.html?goods_id=1002',
    created_time: '2024-08-01T10:35:00Z'
  },
  {
    goods_id: '1003',
    goods_name: '【国补商品】海尔冰箱 风冷无霜 十字对开门 508升',
    keyword: '冰箱',
    price: 2199,
    original_price: 2899,
    coupon_price: 2099,
    market_price: 2899,
    price_type_name: '拼团价',
    sales: '8900+',
    comment_count: 3456,
    rating: 4.6,
    sales_tip: '月销8900+',
    brand_name: '海尔',
    category: '家用电器',
    subsidy_info: '国补,以旧换新',
    activity_type_name: '以旧换新',
    merchant_type_name: '海尔官方旗舰店',
    marketing_tags: '国补商品,以旧换新,风冷无霜',
    tags: '十字对开门,节能环保,智能变频',
    special_text: '国家补贴500元',
    image_url: 'https://img.pddpic.com/mms-material-img/2023-09-13/example3.jpg',
    hd_thumb_url: 'https://img.pddpic.com/mms-material-img/2023-09-13/example3_hd.jpg',
    goods_url: 'https://mobile.yangkeduo.com/goods.html?goods_id=1003',
    created_time: '2024-08-01T10:40:00Z'
  },
  {
    goods_id: '1004',
    goods_name: '格力空调 新能效一级 变频冷暖 挂机1.5匹',
    keyword: '空调',
    price: 1899,
    original_price: 2299,
    coupon_price: 1799,
    market_price: 2299,
    price_type_name: '拼团价',
    sales: '15600+',
    comment_count: 5678,
    rating: 4.8,
    sales_tip: '月销1.56万+',
    brand_name: '格力',
    category: '家用电器',
    subsidy_info: '',
    activity_type_name: '限时促销',
    merchant_type_name: '格力官方旗舰店',
    marketing_tags: '新能效一级,变频技术,静音设计',
    tags: '冷暖空调,节能省电,智能控制',
    special_text: '免费安装',
    image_url: 'https://img.pddpic.com/mms-material-img/2023-09-13/example4.jpg',
    hd_thumb_url: 'https://img.pddpic.com/mms-material-img/2023-09-13/example4_hd.jpg',
    goods_url: 'https://mobile.yangkeduo.com/goods.html?goods_id=1004',
    created_time: '2024-08-01T10:45:00Z'
  },
  {
    goods_id: '1005',
    goods_name: '【百亿补贴】美的洗衣机 10公斤 变频滚筒 除菌洗',
    keyword: '洗衣机',
    price: 1299,
    original_price: 1599,
    coupon_price: 1199,
    market_price: 1599,
    price_type_name: '拼团价',
    sales: '34500+',
    comment_count: 12345,
    rating: 4.5,
    sales_tip: '月销3.45万+',
    brand_name: '美的',
    category: '家用电器',
    subsidy_info: '百亿补贴',
    activity_type_name: '百亿补贴',
    merchant_type_name: '美的官方旗舰店',
    marketing_tags: '百亿补贴,除菌洗,变频节能',
    tags: '10公斤容量,滚筒洗衣机,智能投放',
    special_text: '送洗衣液+安装',
    image_url: 'https://img.pddpic.com/mms-material-img/2023-09-13/example5.jpg',
    hd_thumb_url: 'https://img.pddpic.com/mms-material-img/2023-09-13/example5_hd.jpg',
    goods_url: 'https://mobile.yangkeduo.com/goods.html?goods_id=1005',
    created_time: '2024-08-01T10:50:00Z'
  },
  {
    goods_id: '1006',
    goods_name: '华为 HUAWEI Mate 60 Pro 12GB+256GB 雅川青',
    keyword: '手机',
    price: 5999,
    original_price: 6499,
    coupon_price: 5899,
    market_price: 6499,
    price_type_name: '拼团价',
    sales: '18700+',
    comment_count: 7890,
    rating: 4.9,
    sales_tip: '月销1.87万+',
    brand_name: '华为',
    category: '手机通讯',
    subsidy_info: '满减活动',
    activity_type_name: '满减优惠',
    merchant_type_name: '华为官方旗舰店',
    marketing_tags: '麒麟9000S,卫星通话,昆仑玻璃',
    tags: '5G手机,卫星通信,专业拍照',
    special_text: '赠送华为FreeBuds',
    image_url: 'https://img.pddpic.com/mms-material-img/2023-09-13/example6.jpg',
    hd_thumb_url: 'https://img.pddpic.com/mms-material-img/2023-09-13/example6_hd.jpg',
    goods_url: 'https://mobile.yangkeduo.com/goods.html?goods_id=1006',
    created_time: '2024-08-01T10:55:00Z'
  },
  {
    goods_id: '1007',
    goods_name: '【政府补贴】海信电视 75英寸 4K超高清 智慧屏',
    keyword: '电视',
    price: 3299,
    original_price: 3999,
    coupon_price: 3199,
    market_price: 3999,
    price_type_name: '拼团价',
    sales: '6780+',
    comment_count: 2345,
    rating: 4.4,
    sales_tip: '月销6780+',
    brand_name: '海信',
    category: '家用电器',
    subsidy_info: '政府补贴,以旧换新最高补贴800元',
    activity_type_name: '以旧换新',
    merchant_type_name: '海信官方旗舰店',
    marketing_tags: '政府补贴,4K超清,智慧屏',
    tags: '75英寸,4K分辨率,智能电视',
    special_text: '政府补贴最高800元',
    image_url: 'https://img.pddpic.com/mms-material-img/2023-09-13/example7.jpg',
    hd_thumb_url: 'https://img.pddpic.com/mms-material-img/2023-09-13/example7_hd.jpg',
    goods_url: 'https://mobile.yangkeduo.com/goods.html?goods_id=1007',
    created_time: '2024-08-01T11:00:00Z'
  },
  {
    goods_id: '1008',
    goods_name: 'OPPO Reno11 Pro 12GB+256GB 月光宝石',
    keyword: '手机',
    price: 3299,
    original_price: 3699,
    coupon_price: 3199,
    market_price: 3699,
    price_type_name: '拼团价',
    sales: '12400+',
    comment_count: 4567,
    rating: 4.6,
    sales_tip: '月销1.24万+',
    brand_name: 'OPPO',
    category: '手机通讯',
    subsidy_info: '',
    activity_type_name: '新品首发',
    merchant_type_name: 'OPPO官方旗舰店',
    marketing_tags: '人像拍照,67W快充,ColorOS14',
    tags: '5G手机,人像摄影,快充技术',
    special_text: '送无线耳机',
    image_url: 'https://img.pddpic.com/mms-material-img/2023-09-13/example8.jpg',
    hd_thumb_url: 'https://img.pddpic.com/mms-material-img/2023-09-13/example8_hd.jpg',
    goods_url: 'https://mobile.yangkeduo.com/goods.html?goods_id=1008',
    created_time: '2024-08-01T11:05:00Z'
  }
];

// 生成更多测试数据的函数
export const generateMockProducts = (count: number = 50): Product[] => {
  const brands = ['Apple', '华为', '小米', 'OPPO', 'vivo', '三星', '荣耀', '一加'];
  const categories = ['手机通讯', '家用电器', '数码配件', '电脑办公'];
  const keywords = ['手机', '电脑', '电视', '冰箱', '洗衣机', '空调', '耳机', '平板'];
  const subsidyTypes = ['', '百亿补贴', '政府补贴', '国补', '优惠券可用', '满减活动'];
  
  const products: Product[] = [];
  
  for (let i = 0; i < count; i++) {
    const id = `${2000 + i}`;
    const brand = brands[Math.floor(Math.random() * brands.length)];
    const category = categories[Math.floor(Math.random() * categories.length)];
    const keyword = keywords[Math.floor(Math.random() * keywords.length)];
    const subsidyInfo = subsidyTypes[Math.floor(Math.random() * subsidyTypes.length)];
    
    const basePrice = Math.floor(Math.random() * 8000) + 500;
    const discount = Math.random() * 0.3 + 0.1; // 10%-40% discount
    const price = Math.floor(basePrice * (1 - discount));
    
    products.push({
      goods_id: id,
      goods_name: `${brand} ${keyword} 智能产品 ${id}型号`,
      keyword: keyword,
      price: price,
      original_price: basePrice,
      coupon_price: price - Math.floor(Math.random() * 200),
      market_price: basePrice,
      price_type_name: '拼团价',
      sales: Math.floor(Math.random() * 50000) + 100,
      comment_count: Math.floor(Math.random() * 10000) + 50,
      rating: Math.round((Math.random() * 1.5 + 3.5) * 10) / 10, // 3.5-5.0
      sales_tip: `月销${Math.floor(Math.random() * 10000) + 100}+`,
      brand_name: brand,
      category: category,
      subsidy_info: subsidyInfo,
      activity_type_name: subsidyInfo ? (subsidyInfo.includes('百亿') ? '百亿补贴' : '优惠活动') : '限时促销',
      merchant_type_name: `${brand}官方旗舰店`,
      marketing_tags: `${subsidyInfo ? subsidyInfo + ',' : ''}正品保证,快速发货`,
      tags: `${category},智能设备,高品质`,
      special_text: Math.random() > 0.5 ? '送配件礼包' : '',
      image_url: `https://img.pddpic.com/mms-material-img/2023-09-13/example${i % 10}.jpg`,
      hd_thumb_url: `https://img.pddpic.com/mms-material-img/2023-09-13/example${i % 10}_hd.jpg`,
      goods_url: `https://mobile.yangkeduo.com/goods.html?goods_id=${id}`,
      created_time: new Date(Date.now() - Math.random() * 86400000 * 7).toISOString() // 最近7天内
    });
  }
  
  return products;
};