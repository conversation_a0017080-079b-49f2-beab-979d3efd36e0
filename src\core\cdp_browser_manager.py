"""
CDP浏览器管理器 - 基于MediaCrawler实现
负责启动和管理通过CDP连接的浏览器
"""
import os
import socket
import asyncio
import httpx
import time
import json
from datetime import datetime, timedelta
from typing import Optional, Dict, List, Callable
from playwright.async_api import Playwright, <PERSON><PERSON>er, BrowserContext, Page
from loguru import logger

from .browser_launcher import BrowserLauncher
from ..utils.helpers import load_config


class CDPBrowserManager:
    """
    CDP浏览器管理器，负责启动和管理通过CDP连接的浏览器
    支持健康检查、崩溃检测和自动恢复
    """

    def __init__(self, config_path: str = "config/settings.yaml"):
        """初始化CDP浏览器管理器"""
        self.config = load_config(config_path)
        self.cdp_config = self.config.get("cdp", {})
        
        self.launcher = BrowserLauncher()
        self.browser: Optional[Browser] = None
        self.browser_context: Optional[BrowserContext] = None
        self.debug_port: Optional[int] = None
        
        # 初始化Cookie管理器
        from .cookie_manager import CookieManager
        self.cookie_manager = CookieManager()
        
        # 健康检查和恢复机制相关属性
        self.health_check_enabled = self.cdp_config.get("health_check", {}).get("enabled", True)
        self.health_check_interval = self.cdp_config.get("health_check", {}).get("interval", 30)  # 30秒
        self.health_check_timeout = self.cdp_config.get("health_check", {}).get("timeout", 10)   # 10秒
        self.max_recovery_attempts = self.cdp_config.get("recovery", {}).get("max_attempts", 3)
        self.recovery_delay = self.cdp_config.get("recovery", {}).get("delay", 5)  # 5秒
        
        # 状态监控
        self.last_health_check = None
        self.health_check_task: Optional[asyncio.Task] = None
        self.is_recovering = False
        self.recovery_attempts = 0
        self.crash_count = 0
        self.last_crash_time = None
        
        # 恢复统计
        self.stats = {
            "start_time": datetime.now(),
            "total_crashes": 0,
            "successful_recoveries": 0,
            "failed_recoveries": 0,
            "health_checks_performed": 0,
            "health_check_failures": 0
        }
        
        # 恢复回调
        self.on_crash_detected: Optional[Callable] = None
        self.on_recovery_success: Optional[Callable] = None
        self.on_recovery_failed: Optional[Callable] = None
        
        # 会话状态持久化
        self.session_data = {}
        self.session_backup_enabled = self.cdp_config.get("session_backup", {}).get("enabled", True)
        self.session_backup_interval = self.cdp_config.get("session_backup", {}).get("interval", 60)  # 60秒
        self.session_backup_task: Optional[asyncio.Task] = None
        
        # 故障转移策略
        self.failover_strategy = self.cdp_config.get("failover", {}).get("strategy", "restart")  # restart, fallback_to_standard
        self.fallback_to_standard = self.cdp_config.get("failover", {}).get("fallback_to_standard", True)
        self.graceful_degradation = self.cdp_config.get("failover", {}).get("graceful_degradation", True)
        
        logger.info(f"CDP浏览器管理器初始化完成（健康检查: {self.health_check_enabled}，会话备份: {self.session_backup_enabled}）")

    async def launch_and_connect(
        self,
        playwright: Playwright,
        playwright_proxy: Optional[Dict] = None,
        user_agent: Optional[str] = None,
        headless: bool = False,
    ) -> BrowserContext:
        """
        启动浏览器并通过CDP连接
        
        Args:
            playwright: Playwright实例
            playwright_proxy: 代理配置（CDP模式下需要在浏览器启动前配置）
            user_agent: 用户代理（可选）
            headless: 是否无头模式
            
        Returns:
            BrowserContext: 浏览器上下文
        """
        try:
            # 1. 检测浏览器路径
            browser_path = await self._get_browser_path()

            # 2. 获取可用端口
            self.debug_port = self.launcher.find_available_port(
                self.cdp_config.get("debug_port", 9222)
            )

            # 3. 启动浏览器
            await self._launch_browser(browser_path, headless)

            # 4. 通过CDP连接
            await self._connect_via_cdp(playwright)

            # 5. 创建浏览器上下文
            browser_context = await self._create_browser_context(
                playwright_proxy, user_agent
            )

            self.browser_context = browser_context
            
            # 6. 加载保存的Cookie
            if self.cdp_config.get("save_login_state", True):
                await self.cookie_manager.load_cookies(browser_context, "pdd")
                logger.info("已尝试加载保存的Cookie")
            
            # 7. 启动健康检查
            if self.health_check_enabled:
                await self._start_health_check()
            
            # 8. 启动会话备份
            if self.session_backup_enabled:
                await self._start_session_backup()
            
            # 9. 尝试恢复会话状态
            await self._restore_session_state()
            
            return browser_context

        except Exception as e:
            logger.error(f"CDP浏览器启动失败: {e}")
            # 如果是CDP连接失败，提供更详细的错误信息
            if "CDP" in str(e) or "WebSocket" in str(e):
                logger.error("CDP连接失败可能的原因：")
                logger.error("1. 浏览器未正确启动")
                logger.error("2. 调试端口被占用")
                logger.error("3. 防火墙阻止了连接")
                logger.error("4. 浏览器版本不兼容")
                logger.error(f"\n请尝试：")
                logger.error(f"- 检查端口 {self.debug_port} 是否被占用")
                logger.error("- 关闭所有Chrome/Edge浏览器实例后重试")
                logger.error("或者在config/settings.yaml中禁用CDP模式")
            await self.cleanup()
            raise

    async def _get_browser_path(self) -> str:
        """获取浏览器路径"""
        # 优先使用用户自定义路径
        custom_path = self.cdp_config.get("custom_browser_path")
        if custom_path and os.path.isfile(custom_path):
            logger.info(f"使用自定义浏览器路径: {custom_path}")
            return custom_path

        # 自动检测浏览器路径
        browser_paths = self.launcher.detect_browser_paths()

        if not browser_paths:
            # 生成详细的错误信息和解决方案
            error_msg = self._generate_browser_not_found_error()
            raise RuntimeError(error_msg)

        browser_path = browser_paths[0]  # 使用第一个找到的浏览器
        browser_name, browser_version = self.launcher.get_browser_info(browser_path)

        logger.info(f"检测到浏览器: {browser_name} ({browser_version})")
        logger.info(f"浏览器路径: {browser_path}")

        return browser_path

    async def _test_cdp_connection(self, debug_port: int) -> bool:
        """测试CDP连接是否可用"""
        try:
            # 简单的socket连接测试
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.settimeout(5)
                result = s.connect_ex(("localhost", debug_port))
                if result == 0:
                    logger.info(f"CDP端口 {debug_port} 可访问")
                    return True
                else:
                    logger.warning(f"CDP端口 {debug_port} 不可访问")
                    return False
        except Exception as e:
            logger.warning(f"CDP连接测试失败: {e}")
            return False

    async def _launch_browser(self, browser_path: str, headless: bool):
        """启动浏览器进程"""
        # 设置用户数据目录（如果启用了保存登录状态）
        user_data_dir = None
        if self.cdp_config.get("save_login_state", True):
            user_data_dir = os.path.join(
                os.getcwd(),
                "browser_data",
                f"cdp_{self.cdp_config.get('user_data_dir', 'pdd_user_data_dir')}",
            )
            os.makedirs(user_data_dir, exist_ok=True)
            logger.info(f"用户数据目录: {user_data_dir}")

        # 启动浏览器
        self.launcher.browser_process = await self.launcher.launch_browser(
            browser_path=browser_path,
            debug_port=self.debug_port,
            headless=headless,
            user_data_dir=user_data_dir,
        )

        # 等待浏览器准备就绪
        launch_timeout = self.cdp_config.get("launch_timeout", 30)
        if not await self.launcher.wait_for_browser_ready(
            self.debug_port, launch_timeout
        ):
            raise RuntimeError(f"浏览器在 {launch_timeout} 秒内未能启动")

        # 额外等待一秒让CDP服务完全启动
        await asyncio.sleep(1)

        # 测试CDP连接
        if not await self._test_cdp_connection(self.debug_port):
            logger.warning("CDP连接测试失败，但将继续尝试连接")

    async def _get_browser_websocket_url(self, debug_port: int) -> str:
        """获取浏览器的WebSocket连接URL"""
        max_retries = 3
        retry_delay = 1
        
        for attempt in range(max_retries):
            try:
                async with httpx.AsyncClient() as client:
                    response = await client.get(
                        f"http://localhost:{debug_port}/json/version", 
                        timeout=10
                    )
                    if response.status_code == 200:
                        data = response.json()
                        ws_url = data.get("webSocketDebuggerUrl")
                        if ws_url:
                            logger.info(f"获取到浏览器WebSocket URL: {ws_url}")
                            return ws_url
                        else:
                            # 如果没有webSocketDebuggerUrl，尝试构造一个
                            browser_id = data.get("Browser", "").split("/")[1] if "Browser" in data else None
                            if browser_id:
                                ws_url = f"ws://localhost:{debug_port}/devtools/browser/{browser_id}"
                                logger.info(f"构造WebSocket URL: {ws_url}")
                                return ws_url
                            else:
                                raise RuntimeError("未找到webSocketDebuggerUrl或无法构造")
                    else:
                        raise RuntimeError(f"HTTP {response.status_code}: {response.text}")
                        
            except httpx.ConnectError as e:
                logger.warning(f"连接到调试端口失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(retry_delay)
                    retry_delay *= 2
                else:
                    raise RuntimeError(f"无法连接到调试端口 {debug_port}，请确保浏览器已启动")
            except Exception as e:
                logger.error(f"获取WebSocket URL失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(retry_delay)
                else:
                    raise

    async def _connect_via_cdp(self, playwright: Playwright):
        """通过CDP连接到浏览器（带重试机制）"""
        max_retries = 3
        retry_delay = 2
        
        for attempt in range(max_retries):
            try:
                # 获取正确的WebSocket URL
                ws_url = await self._get_browser_websocket_url(self.debug_port)
                logger.info(f"正在通过CDP连接到浏览器: {ws_url} (尝试 {attempt + 1}/{max_retries})")

                # 使用Playwright的connectOverCDP方法连接
                self.browser = await playwright.chromium.connect_over_cdp(ws_url)

                if self.browser.is_connected():
                    logger.info("成功连接到浏览器")
                    logger.info(f"浏览器上下文数量: {len(self.browser.contexts)}")
                    return  # 成功连接，退出重试循环
                else:
                    raise RuntimeError("CDP连接失败")

            except Exception as e:
                logger.error(f"CDP连接失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                
                if attempt < max_retries - 1:
                    logger.info(f"等待 {retry_delay} 秒后重试...")
                    await asyncio.sleep(retry_delay)
                    retry_delay *= 2  # 指数退避
                else:
                    logger.error("CDP连接已达最大重试次数，放弃连接")
                    raise

    async def _create_browser_context(
        self, playwright_proxy: Optional[Dict] = None, user_agent: Optional[str] = None
    ) -> BrowserContext:
        """创建或获取浏览器上下文"""
        if not self.browser:
            raise RuntimeError("浏览器未连接")

        # 获取现有上下文或创建新的上下文
        contexts = self.browser.contexts

        if contexts:
            # 使用现有的第一个上下文（保持登录状态）
            browser_context = contexts[0]
            logger.info("使用现有的浏览器上下文")
        else:
            # 创建新的上下文
            context_options = {
                "viewport": {"width": 1920, "height": 1080},
                "accept_downloads": True,
            }

            # 设置用户代理
            if user_agent:
                context_options["user_agent"] = user_agent
                logger.info(f"设置用户代理: {user_agent}")

            # 注意：CDP模式下代理设置可能不生效，因为浏览器已经启动
            if playwright_proxy:
                logger.warning(
                    "警告: CDP模式下代理设置可能不生效，"
                    "建议在浏览器启动前配置系统代理或浏览器代理扩展"
                )

            browser_context = await self.browser.new_context(**context_options)
            logger.info("创建新的浏览器上下文")

        return browser_context

    async def add_stealth_script(self, script_path: str = "libs/stealth.min.js"):
        """添加反检测脚本"""
        if self.browser_context and os.path.exists(script_path):
            try:
                await self.browser_context.add_init_script(path=script_path)
                logger.info(f"已添加反检测脚本: {script_path}")
            except Exception as e:
                logger.warning(f"添加反检测脚本失败: {e}")

    async def add_cookies(self, cookies: list):
        """添加Cookie"""
        if self.browser_context:
            try:
                await self.browser_context.add_cookies(cookies)
                logger.info(f"已添加 {len(cookies)} 个Cookie")
            except Exception as e:
                logger.warning(f"添加Cookie失败: {e}")

    async def get_cookies(self) -> list:
        """获取当前Cookie"""
        if self.browser_context:
            try:
                cookies = await self.browser_context.cookies()
                return cookies
            except Exception as e:
                logger.warning(f"获取Cookie失败: {e}")
                return []
        return []

    async def cleanup(self):
        """清理资源"""
        try:
            # 保存Cookie（如果配置了保存登录状态）
            if self.browser_context and self.cdp_config.get("save_login_state", True):
                try:
                    await self.cookie_manager.save_cookies(self.browser_context, "pdd")
                    logger.info("已保存Cookie")
                except Exception as e:
                    logger.warning(f"保存Cookie失败: {e}")
            
            # 关闭浏览器上下文
            if self.browser_context:
                await self.browser_context.close()
                self.browser_context = None
                logger.info("浏览器上下文已关闭")

            # 断开浏览器连接
            if self.browser:
                await self.browser.close()
                self.browser = None
                logger.info("浏览器连接已断开")

            # 关闭浏览器进程（如果配置为自动关闭）
            if self.cdp_config.get("auto_close_browser", True):
                await self.launcher.cleanup()
            else:
                logger.info("浏览器进程保持运行（auto_close_browser=False）")

        except Exception as e:
            logger.error(f"清理资源时出错: {e}")
        finally:
            # 停止健康检查任务
            if self.health_check_task and not self.health_check_task.done():
                self.health_check_task.cancel()
                try:
                    await self.health_check_task
                except asyncio.CancelledError:
                    pass
                logger.info("健康检查任务已停止")
            
            # 停止会话备份任务
            if self.session_backup_task and not self.session_backup_task.done():
                self.session_backup_task.cancel()
                try:
                    await self.session_backup_task
                except asyncio.CancelledError:
                    pass
                logger.info("会话备份任务已停止")

    def is_connected(self) -> bool:
        """检查是否已连接到浏览器"""
        return self.browser is not None and self.browser.is_connected()

    async def _start_health_check(self):
        """启动健康检查任务"""
        if self.health_check_task and not self.health_check_task.done():
            return
        
        self.health_check_task = asyncio.create_task(self._health_check_loop())
        logger.info(f"健康检查已启动，检查间隔: {self.health_check_interval}秒")

    async def _health_check_loop(self):
        """健康检查循环"""
        while True:
            try:
                await asyncio.sleep(self.health_check_interval)
                
                if self.is_recovering:
                    continue
                
                # 执行健康检查
                is_healthy = await self._perform_health_check()
                self.last_health_check = datetime.now()
                self.stats["health_checks_performed"] += 1
                
                if not is_healthy:
                    logger.warning("浏览器健康检查失败，开始恢复流程")
                    self.stats["health_check_failures"] += 1
                    await self._handle_browser_crash()
                    
            except asyncio.CancelledError:
                logger.info("健康检查任务已取消")
                break
            except Exception as e:
                logger.error(f"健康检查循环出错: {e}")
                await asyncio.sleep(5)  # 错误后短暂等待

    async def _perform_health_check(self) -> bool:
        """执行健康检查"""
        try:
            # 1. 检查浏览器连接
            if not self.is_connected():
                logger.debug("健康检查失败: 浏览器未连接")
                return False
            
            # 2. 检查CDP端口连接
            if not await self._test_cdp_connection(self.debug_port):
                logger.debug("健康检查失败: CDP端口不可访问")
                return False
            
            # 3. 检查浏览器上下文
            if not self.browser_context:
                logger.debug("健康检查失败: 浏览器上下文为空")
                return False
            
            # 4. 尝试获取浏览器版本（轻量级操作）
            try:
                _ = self.browser.version
            except Exception as e:
                logger.debug(f"健康检查失败: 无法获取浏览器版本 - {e}")
                return False
            
            # 5. 检查页面是否响应（如果有活动页面）
            try:
                pages = self.browser_context.pages
                if pages:
                    # 尝试在第一个页面上执行简单的JavaScript
                    page = pages[0]
                    result = await asyncio.wait_for(
                        page.evaluate("() => true"),
                        timeout=self.health_check_timeout
                    )
                    if not result:
                        logger.debug("健康检查失败: 页面JavaScript执行失败")
                        return False
            except asyncio.TimeoutError:
                logger.debug("健康检查失败: 页面响应超时")
                return False
            except Exception as e:
                logger.debug(f"健康检查失败: 页面检查出错 - {e}")
                return False
            
            logger.debug("浏览器健康检查通过")
            return True
            
        except Exception as e:
            logger.debug(f"健康检查出错: {e}")
            return False

    async def _handle_browser_crash(self):
        """处理浏览器崩溃"""
        if self.is_recovering:
            return
        
        self.is_recovering = True
        self.crash_count += 1
        self.last_crash_time = datetime.now()
        self.stats["total_crashes"] += 1
        
        logger.error(f"检测到浏览器崩溃（第{self.crash_count}次），开始恢复流程")
        
        # 触发崩溃回调
        if self.on_crash_detected:
            try:
                await self.on_crash_detected()
            except Exception as e:
                logger.error(f"崩溃回调执行失败: {e}")
        
        # 记录崩溃日志
        await self._log_crash_info()
        
        # 尝试恢复
        recovery_success = await self._attempt_recovery()
        
        if recovery_success:
            logger.info("浏览器恢复成功")
            self.stats["successful_recoveries"] += 1
            self.recovery_attempts = 0
            
            # 触发恢复成功回调
            if self.on_recovery_success:
                try:
                    await self.on_recovery_success()
                except Exception as e:
                    logger.error(f"恢复成功回调执行失败: {e}")
        else:
            logger.error("浏览器恢复失败")
            self.stats["failed_recoveries"] += 1
            
            # 触发恢复失败回调
            if self.on_recovery_failed:
                try:
                    await self.on_recovery_failed()
                except Exception as e:
                    logger.error(f"恢复失败回调执行失败: {e}")
        
        self.is_recovering = False

    async def _attempt_recovery(self) -> bool:
        """尝试恢复浏览器"""
        for attempt in range(1, self.max_recovery_attempts + 1):
            logger.info(f"开始第{attempt}次恢复尝试")
            self.recovery_attempts = attempt
            
            try:
                # 1. 清理当前连接
                await self._cleanup_connections()
                
                # 2. 等待恢复延迟
                if attempt > 1:
                    delay = self.recovery_delay * attempt  # 指数退避
                    logger.info(f"等待{delay}秒后重试")
                    await asyncio.sleep(delay)
                
                # 3. 尝试重新启动浏览器
                from playwright.async_api import async_playwright
                playwright = await async_playwright().start()
                
                try:
                    browser_context = await self.launch_and_connect(
                        playwright=playwright,
                        playwright_proxy=None,
                        user_agent=None,
                        headless=self.cdp_config.get("headless", False)
                    )
                except Exception as recovery_error:
                    logger.warning(f"CDP恢复失败，尝试故障转移: {recovery_error}")
                    # 尝试故障转移策略
                    browser_context = await self._apply_failover_strategy()
                    if not browser_context:
                        raise recovery_error
                
                # 4. 验证恢复成功
                if await self._perform_health_check():
                    logger.info(f"第{attempt}次恢复尝试成功")
                    return True
                else:
                    logger.warning(f"第{attempt}次恢复尝试失败：健康检查未通过")
                    
            except Exception as e:
                logger.error(f"第{attempt}次恢复尝试出错: {e}")
            
        logger.error(f"所有{self.max_recovery_attempts}次恢复尝试均失败")
        return False

    async def _cleanup_connections(self):
        """清理现有连接"""
        try:
            # 停止健康检查
            if self.health_check_task and not self.health_check_task.done():
                self.health_check_task.cancel()
                try:
                    await self.health_check_task
                except asyncio.CancelledError:
                    pass
            
            # 清理浏览器连接
            if self.browser_context:
                try:
                    await self.browser_context.close()
                except:
                    pass
                self.browser_context = None
            
            if self.browser:
                try:
                    await self.browser.close()
                except:
                    pass
                self.browser = None
            
            # 终止浏览器进程
            if self.launcher.browser_process:
                try:
                    await self.launcher.cleanup()
                except:
                    pass
            
            logger.debug("连接清理完成")
            
        except Exception as e:
            logger.error(f"清理连接时出错: {e}")

    async def _log_crash_info(self):
        """记录崩溃信息"""
        try:
            crash_info = {
                "timestamp": datetime.now().isoformat(),
                "crash_count": self.crash_count,
                "debug_port": self.debug_port,
                "browser_version": getattr(self.browser, 'version', 'unknown') if self.browser else 'unknown',
                "context_count": len(self.browser.contexts) if self.browser else 0,
                "stats": self.stats.copy()
            }
            
            # 保存到日志文件
            logs_dir = "logs"
            os.makedirs(logs_dir, exist_ok=True)
            
            crash_log_path = os.path.join(logs_dir, "browser_crashes.json")
            
            # 读取现有崩溃日志
            crashes = []
            if os.path.exists(crash_log_path):
                try:
                    with open(crash_log_path, 'r', encoding='utf-8') as f:
                        crashes = json.load(f)
                except:
                    crashes = []
            
            # 添加新的崩溃记录
            crashes.append(crash_info)
            
            # 保持最近100条记录
            crashes = crashes[-100:]
            
            # 保存更新的日志
            with open(crash_log_path, 'w', encoding='utf-8') as f:
                json.dump(crashes, f, indent=2, ensure_ascii=False)
            
            logger.info(f"崩溃信息已记录到 {crash_log_path}")
            
        except Exception as e:
            logger.error(f"记录崩溃信息失败: {e}")

    def get_recovery_stats(self) -> Dict:
        """获取恢复统计信息"""
        uptime = datetime.now() - self.stats["start_time"]
        
        return {
            **self.stats,
            "uptime_seconds": int(uptime.total_seconds()),
            "uptime_formatted": str(uptime).split('.')[0],  # 去掉微秒
            "current_crash_count": self.crash_count,
            "is_recovering": self.is_recovering,
            "recovery_attempts": self.recovery_attempts,
            "last_health_check": self.last_health_check.isoformat() if self.last_health_check else None,
            "last_crash_time": self.last_crash_time.isoformat() if self.last_crash_time else None,
            "health_check_enabled": self.health_check_enabled,
            "health_check_interval": self.health_check_interval
        }

    def set_crash_callback(self, callback: Callable):
        """设置崩溃检测回调"""
        self.on_crash_detected = callback

    def set_recovery_callbacks(self, success_callback: Callable = None, failed_callback: Callable = None):
        """设置恢复回调"""
        if success_callback:
            self.on_recovery_success = success_callback
        if failed_callback:
            self.on_recovery_failed = failed_callback

    async def force_health_check(self) -> bool:
        """手动执行健康检查"""
        logger.info("执行手动健康检查")
        is_healthy = await self._perform_health_check()
        self.last_health_check = datetime.now()
        self.stats["health_checks_performed"] += 1
        
        if not is_healthy:
            self.stats["health_check_failures"] += 1
        
        return is_healthy

    async def trigger_recovery(self) -> bool:
        """手动触发恢复流程"""
        logger.info("手动触发浏览器恢复")
        return await self._attempt_recovery()

    async def _start_session_backup(self):
        """启动会话备份任务"""
        if self.session_backup_task and not self.session_backup_task.done():
            return
        
        self.session_backup_task = asyncio.create_task(self._session_backup_loop())
        logger.info(f"会话备份已启动，备份间隔: {self.session_backup_interval}秒")

    async def _session_backup_loop(self):
        """会话备份循环"""
        while True:
            try:
                await asyncio.sleep(self.session_backup_interval)
                
                if self.is_recovering or not self.browser_context:
                    continue
                
                # 执行会话备份
                await self._backup_session_state()
                
            except asyncio.CancelledError:
                logger.info("会话备份任务已取消")
                break
            except Exception as e:
                logger.error(f"会话备份循环出错: {e}")
                await asyncio.sleep(10)  # 错误后等待

    async def _backup_session_state(self):
        """备份会话状态"""
        try:
            if not self.browser_context:
                return
            
            # 收集会话数据
            session_data = {
                "timestamp": datetime.now().isoformat(),
                "debug_port": self.debug_port,
                "cookies": await self.get_cookies(),
                "local_storage": {},
                "session_storage": {},
                "current_url": None,
                "page_count": len(self.browser_context.pages) if self.browser_context else 0
            }
            
            # 获取当前页面URL
            if self.browser_context.pages:
                try:
                    current_page = self.browser_context.pages[0]
                    session_data["current_url"] = current_page.url
                    
                    # 获取localStorage和sessionStorage
                    try:
                        local_storage = await current_page.evaluate(
                            "() => Object.fromEntries(Object.entries(localStorage))"
                        )
                        session_storage = await current_page.evaluate(
                            "() => Object.fromEntries(Object.entries(sessionStorage))"
                        )
                        session_data["local_storage"] = local_storage
                        session_data["session_storage"] = session_storage
                    except Exception as e:
                        logger.debug(f"获取存储数据失败: {e}")
                        
                except Exception as e:
                    logger.debug(f"获取页面信息失败: {e}")
            
            # 保存到文件
            backup_dir = "browser_data/session_backup"
            os.makedirs(backup_dir, exist_ok=True)
            
            backup_file = os.path.join(backup_dir, "cdp_session_backup.json")
            
            with open(backup_file, 'w', encoding='utf-8') as f:
                json.dump(session_data, f, indent=2, ensure_ascii=False)
            
            self.session_data = session_data
            logger.debug("会话状态备份完成")
            
        except Exception as e:
            logger.error(f"备份会话状态失败: {e}")

    async def _restore_session_state(self):
        """恢复会话状态"""
        try:
            backup_file = "browser_data/session_backup/cdp_session_backup.json"
            
            if not os.path.exists(backup_file):
                logger.debug("未找到会话备份文件")
                return
            
            with open(backup_file, 'r', encoding='utf-8') as f:
                session_data = json.load(f)
            
            logger.info("正在恢复会话状态")
            
            # 检查备份时间（避免使用过旧的备份）
            backup_time = datetime.fromisoformat(session_data["timestamp"])
            time_diff = datetime.now() - backup_time
            
            if time_diff.total_seconds() > 3600:  # 超过1小时的备份不使用
                logger.warning(f"会话备份过旧（{time_diff}），跳过恢复")
                return
            
            # 恢复 Cookie（已在 cookie_manager 中实现）
            if session_data.get("cookies"):
                try:
                    await self.browser_context.add_cookies(session_data["cookies"])
                    logger.info("已恢复 Cookie 数据")
                except Exception as e:
                    logger.warning(f"恢复 Cookie 失败: {e}")
            
            # 记录恢复的会话数据
            self.session_data = session_data
            
            logger.info("会话状态恢复完成")
            
        except Exception as e:
            logger.error(f"恢复会话状态失败: {e}")

    async def _apply_failover_strategy(self) -> Optional[BrowserContext]:
        """应用故障转移策略"""
        logger.info(f"应用故障转移策略: {self.failover_strategy}")
        
        if self.failover_strategy == "restart":
            # 重启策略：尝试重新启动CDP浏览器
            return await self._attempt_recovery()
            
        elif self.failover_strategy == "fallback_to_standard" and self.fallback_to_standard:
            # 降级策略：切换到标准模式
            logger.warning("正在切换到标准浏览器模式")
            
            try:
                # 启动标准浏览器
                from playwright.async_api import async_playwright
                playwright = await async_playwright().start()
                
                browser = await playwright.chromium.launch(
                    headless=self.cdp_config.get("headless", False),
                    args=[
                        "--disable-blink-features=AutomationControlled",
                        "--no-sandbox",
                        "--disable-dev-shm-usage"
                    ]
                )
                
                context = await browser.new_context(
                    viewport={"width": 1920, "height": 1080},
                    user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
                )
                
                # 尝试恢复会话状态
                if self.session_data and self.session_data.get("cookies"):
                    try:
                        await context.add_cookies(self.session_data["cookies"])
                        logger.info("已在标准模式中恢复 Cookie")
                    except Exception as e:
                        logger.warning(f"在标准模式中恢复 Cookie 失败: {e}")
                
                self.browser = browser
                self.browser_context = context
                
                logger.info("已成功切换到标准浏览器模式")
                return context
                
            except Exception as e:
                logger.error(f"切换到标准模式失败: {e}")
                return None
        
        elif self.graceful_degradation:
            # 优雅降级：保持最小功能
            logger.warning("启用优雅降级模式")
            # 这里可以实现一些基础功能的保持
            return None
        
        return None

    def get_session_data(self) -> Dict:
        """获取当前会话数据"""
        return self.session_data.copy() if self.session_data else {}

    async def backup_session_now(self):
        """立即执行会话备份"""
        logger.info("手动执行会话备份")
        await self._backup_session_state()

    def enable_graceful_degradation(self, enable: bool = True):
        """启用/禁用优雅降级"""
        self.graceful_degradation = enable
        logger.info(f"优雅降级: {'enabled' if enable else 'disabled'}")

    async def get_browser_info(self) -> Dict[str, any]:
        """获取浏览器信息"""
        if not self.browser:
            return {}

        try:
            version = self.browser.version
            contexts_count = len(self.browser.contexts)

            return {
                "version": version,
                "contexts_count": contexts_count,
                "debug_port": self.debug_port,
                "is_connected": self.is_connected(),
            }
        except Exception as e:
            logger.warning(f"获取浏览器信息失败: {e}")
            return {}

    def _generate_browser_not_found_error(self) -> str:
        """生成浏览器未找到的错误信息"""
        """
        生成详细的浏览器未找到错误信息
        """
        import platform
        system = platform.system()

        error_msg = "❌ 未找到可用的浏览器！\n\n"

        if system == "Windows":
            error_msg += "🔍 已搜索以下位置：\n"
            error_msg += "  • C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe\n"
            error_msg += "  • C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe\n"
            error_msg += "  • %USERPROFILE%\\AppData\\Local\\Google\\Chrome\\Application\\chrome.exe\n"
            error_msg += "  • C:\\Program Files\\Microsoft\\Edge\\Application\\msedge.exe\n"
            error_msg += "  • 便携版和自定义安装路径\n\n"

            error_msg += "💡 解决方案：\n"
            error_msg += "  1. 安装Chrome浏览器：https://www.google.com/chrome/\n"
            error_msg += "  2. 安装Edge浏览器：https://www.microsoft.com/edge\n"
            error_msg += "  3. 如果已安装但在非标准位置，请在config/settings.yaml中设置：\n"
            error_msg += "     cdp:\n"
            error_msg += "       custom_browser_path: \"D:\\Your\\Chrome\\Path\\chrome.exe\"\n\n"

        elif system == "Darwin":  # macOS
            error_msg += "🔍 已搜索以下位置：\n"
            error_msg += "  • /Applications/Google Chrome.app\n"
            error_msg += "  • /Applications/Microsoft Edge.app\n"
            error_msg += "  • ~/Applications/Google Chrome.app\n\n"

            error_msg += "💡 解决方案：\n"
            error_msg += "  1. 安装Chrome：brew install --cask google-chrome\n"
            error_msg += "  2. 安装Edge：brew install --cask microsoft-edge\n"
            error_msg += "  3. 或从官网下载安装\n\n"

        else:  # Linux
            error_msg += "🔍 已搜索以下位置：\n"
            error_msg += "  • /usr/bin/google-chrome\n"
            error_msg += "  • /usr/bin/chromium\n"
            error_msg += "  • /snap/bin/chromium\n"
            error_msg += "  • /usr/bin/microsoft-edge\n\n"

            error_msg += "💡 解决方案：\n"
            error_msg += "  1. Ubuntu/Debian: sudo apt install google-chrome-stable\n"
            error_msg += "  2. CentOS/RHEL: sudo yum install google-chrome-stable\n"
            error_msg += "  3. 或安装Chromium: sudo apt install chromium-browser\n\n"

        error_msg += "🔧 高级配置：\n"
        error_msg += "  如果浏览器安装在自定义位置，请在配置文件中指定路径：\n"
        error_msg += "  config/settings.yaml -> cdp -> custom_browser_path\n\n"

        error_msg += "📞 需要帮助？请检查：\n"
        error_msg += "  • 浏览器是否正确安装\n"
        error_msg += "  • 浏览器可执行文件是否有执行权限\n"
        error_msg += "  • 防火墙或安全软件是否阻止了浏览器启动"

        return error_msg