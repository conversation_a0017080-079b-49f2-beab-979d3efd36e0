import type { TaskInfo, TaskHistory, QuickStartConfig, StepValidation } from './types';
import type { SearchConfig } from '@/types';
import type { CookieValidationResult } from '@/types';

/**
 * 验证 Cookie 状态
 */
export const validateCookieStep = async (): Promise<StepValidation> => {
  try {
    const { apiClient } = await import('@/services/api');
    const response = await apiClient.getCookieStatus();
    
    if (!response.success) {
      throw new Error(response.message || 'API请求失败');
    }
    
    const result = response.data;
    
    if (result && result.valid) {
      return {
        valid: true,
        message: 'Cookie 验证成功，可以开始爬取'
      };
    } else {
      return {
        valid: false,
        message: result?.exists ? 'Cookie 无效或已过期' : '未找到 Cookie 数据',
        details: result?.exists ? ['请检查 Cookie 的有效期和格式'] : ['请先导入有效的 Cookie']
      };
    }
  } catch (error) {
    return {
      valid: false,
      message: '无法连接到服务器',
      details: [(error as Error).message]
    };
  }
};

/**
 * 验证搜索配置
 */
export const validateConfigStep = (config: SearchConfig): StepValidation => {
  const errors: string[] = [];

  if (!config.keyword?.trim()) {
    errors.push('请输入搜索关键词');
  }

  if (config.pageSize && (config.pageSize < 10 || config.pageSize > 100)) {
    errors.push('每页数量应在 10-100 之间');
  }

  if (config.maxPages && (config.maxPages < 1 || config.maxPages > 100)) {
    errors.push('最大页数应在 1-100 之间');
  }

  if (config.minPrice && config.maxPrice && config.minPrice > config.maxPrice) {
    errors.push('最低价格不能大于最高价格');
  }

  if (errors.length > 0) {
    return {
      valid: false,
      message: '配置验证失败',
      details: errors
    };
  }

  return {
    valid: true,
    message: '配置验证成功'
  };
};

/**
 * 生成任务名称
 */
export const generateTaskName = (config: SearchConfig): string => {
  const timestamp = new Date().toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
  
  return `${config.keyword} - ${timestamp}`;
};

/**
 * 格式化任务状态
 */
export const formatTaskStatus = (status: TaskInfo['status']): { text: string; color: string } => {
  const statusMap = {
    pending: { text: '等待中', color: 'default' },
    running: { text: '运行中', color: 'processing' },
    paused: { text: '已暂停', color: 'warning' },
    completed: { text: '已完成', color: 'success' },
    failed: { text: '失败', color: 'error' },
    cancelled: { text: '已取消', color: 'default' }
  };
  
  return statusMap[status] || { text: '未知', color: 'default' };
};

/**
 * 计算任务进度
 */
export const calculateTaskProgress = (task: TaskInfo): number => {
  if (task.progress.totalPages === 0) return 0;
  return Math.round((task.progress.currentPage / task.progress.totalPages) * 100);
};

/**
 * 格式化持续时间
 */
export const formatDuration = (startTime: string, endTime?: string): string => {
  const start = new Date(startTime);
  const end = endTime ? new Date(endTime) : new Date();
  const duration = Math.floor((end.getTime() - start.getTime()) / 1000);

  if (duration < 60) {
    return `${duration}秒`;
  } else if (duration < 3600) {
    return `${Math.floor(duration / 60)}分${duration % 60}秒`;
  } else {
    const hours = Math.floor(duration / 3600);
    const minutes = Math.floor((duration % 3600) / 60);
    return `${hours}时${minutes}分`;
  }
};

/**
 * 估算剩余时间
 */
export const estimateRemainingTime = (task: TaskInfo): string => {
  if (!task.startedAt || task.progress.currentPage === 0) {
    return '估算中...';
  }

  const elapsedSeconds = Math.floor((Date.now() - new Date(task.startedAt).getTime()) / 1000);
  const remainingPages = task.progress.totalPages - task.progress.currentPage;
  const secondsPerPage = elapsedSeconds / task.progress.currentPage;
  const remainingSeconds = Math.floor(remainingPages * secondsPerPage);

  if (remainingSeconds < 60) {
    return `约 ${remainingSeconds} 秒`;
  } else if (remainingSeconds < 3600) {
    return `约 ${Math.floor(remainingSeconds / 60)} 分钟`;
  } else {
    const hours = Math.floor(remainingSeconds / 3600);
    const minutes = Math.floor((remainingSeconds % 3600) / 60);
    return `约 ${hours} 小时 ${minutes} 分钟`;
  }
};

/**
 * 验证任务是否可以执行操作
 */
export const canPerformAction = (
  task: TaskInfo, 
  action: 'pause' | 'resume' | 'stop' | 'cancel' | 'retry'
): boolean => {
  switch (action) {
    case 'pause':
      return task.status === 'running';
    case 'resume':
      return task.status === 'paused';
    case 'stop':
      return task.status === 'running' || task.status === 'paused';
    case 'cancel':
      return task.status === 'pending' || task.status === 'running' || task.status === 'paused';
    case 'retry':
      return task.status === 'failed' || task.status === 'cancelled';
    default:
      return false;
  }
};

/**
 * 获取配置的显示摘要
 */
export const getConfigSummary = (config: SearchConfig): string[] => {
  const summary: string[] = [
    `关键词: ${config.keyword}`
  ];

  if (config.category) {
    summary.push(`分类: ${config.category}`);
  }

  if (config.minPrice || config.maxPrice) {
    const priceRange = [
      config.minPrice ? `¥${config.minPrice}` : '不限',
      config.maxPrice ? `¥${config.maxPrice}` : '不限'
    ].join(' - ');
    summary.push(`价格: ${priceRange}`);
  }

  if (config.sortType) {
    const sortMap: Record<string, string> = {
      sales: '销量优先',
      price_asc: '价格从低到高',
      price_desc: '价格从高到低',
      rating: '评分优先',
      latest: '最新发布'
    };
    summary.push(`排序: ${sortMap[config.sortType] || config.sortType}`);
  }

  summary.push(`每页: ${config.pageSize || 20} 个`);
  summary.push(`最大: ${config.maxPages || 10} 页`);

  return summary;
};

/**
 * 本地存储操作
 */
export const storage = {
  // 获取任务列表
  getTasks: (): TaskInfo[] => {
    try {
      const data = localStorage.getItem('pdd-tasks');
      return data ? JSON.parse(data) : [];
    } catch {
      return [];
    }
  },

  // 保存任务列表
  saveTasks: (tasks: TaskInfo[]) => {
    try {
      localStorage.setItem('pdd-tasks', JSON.stringify(tasks));
    } catch (error) {
      console.error('保存任务失败:', error);
    }
  },

  // 获取快速配置
  getQuickConfigs: (): QuickStartConfig[] => {
    try {
      const data = localStorage.getItem('pdd-quick-configs');
      return data ? JSON.parse(data) : [];
    } catch {
      return [];
    }
  },

  // 保存快速配置
  saveQuickConfig: (config: QuickStartConfig) => {
    try {
      const configs = storage.getQuickConfigs();
      const existingIndex = configs.findIndex(c => c.id === config.id);
      
      if (existingIndex >= 0) {
        configs[existingIndex] = { ...config, lastUsed: new Date().toISOString() };
      } else {
        configs.push({ ...config, lastUsed: new Date().toISOString() });
      }
      
      // 只保留最近的 10 个配置
      const sortedConfigs = configs
        .sort((a, b) => new Date(b.lastUsed).getTime() - new Date(a.lastUsed).getTime())
        .slice(0, 10);
      
      localStorage.setItem('pdd-quick-configs', JSON.stringify(sortedConfigs));
    } catch (error) {
      console.error('保存快速配置失败:', error);
    }
  },

  // 删除快速配置
  removeQuickConfig: (id: string) => {
    try {
      const configs = storage.getQuickConfigs().filter(c => c.id !== id);
      localStorage.setItem('pdd-quick-configs', JSON.stringify(configs));
    } catch (error) {
      console.error('删除快速配置失败:', error);
    }
  },

  // 获取任务历史
  getTaskHistory: (): TaskHistory[] => {
    try {
      const data = localStorage.getItem('pdd-task-history');
      return data ? JSON.parse(data) : [];
    } catch {
      return [];
    }
  },

  // 保存任务历史
  saveTaskHistory: (task: TaskHistory) => {
    try {
      const history = storage.getTaskHistory();
      const existingIndex = history.findIndex(t => t.id === task.id);
      
      if (existingIndex >= 0) {
        history[existingIndex] = task;
      } else {
        history.unshift(task);
      }
      
      // 只保留最近的 50 个任务
      const trimmedHistory = history.slice(0, 50);
      localStorage.setItem('pdd-task-history', JSON.stringify(trimmedHistory));
    } catch (error) {
      console.error('保存任务历史失败:', error);
    }
  }
};