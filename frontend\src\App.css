#root {
  margin: 0;
  padding: 0;
  height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #d1d1d1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #b1b1b1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ant-layout-sider {
    position: fixed !important;
    height: 100vh;
    z-index: 999;
  }
  
  .ant-layout-content {
    margin-left: 0 !important;
  }
}

/* 表格响应式 */
@media (max-width: 576px) {
  .ant-table-wrapper {
    overflow-x: auto;
  }
}

/* 卡片样式优化 */
.ant-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

.ant-card-head {
  border-bottom: 1px solid #f0f0f0;
}

/* 进度条样式 */
.ant-progress-line {
  margin: 8px 0;
}

/* 统计数字样式 */
.ant-statistic-content {
  font-weight: 600;
}

/* 标签样式 */
.ant-tag {
  border-radius: 4px;
}

/* 按钮组样式 */
.ant-btn-group .ant-btn {
  margin-right: 0;
}

/* 表单样式优化 */
.ant-form-item-label > label {
  font-weight: 500;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .ant-layout-header {
    padding: 0 16px;
  }
  
  .ant-layout-content {
    padding: 16px;
  }
  
  .ant-card {
    margin-bottom: 16px;
  }
  
  .ant-col {
    margin-bottom: 16px;
  }
}
