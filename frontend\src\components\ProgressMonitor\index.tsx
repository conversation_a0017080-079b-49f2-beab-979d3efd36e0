import React, { useEffect, useState } from 'react';
import { Row, Col, Card, Space, Alert, Spin } from 'antd';
import { useWebSocket } from '@/hooks/useWebSocket';
import { useAppStore } from '@/stores/appStore';
import ProgressBar, { EnhancedProgressBar } from './ProgressBar';
import KeywordProgress from './KeywordProgress';
import RealtimeStats from './RealtimeStats';
import ConnectionStatus from './ConnectionStatus';
import { calculateEstimatedTime, calculateSuccessRate } from './utils';
import type { ProgressMonitorProps, RealtimeStats as RealtimeStatsType } from './types';

const ProgressMonitor: React.FC<ProgressMonitorProps> = ({
  taskId,
  showAdvanced = true,
  refreshInterval = 1000,
  className,
}) => {
  const { crawlStatus, error } = useAppStore();
  const { 
    isConnected, 
    connectionState, 
    stats, 
    reconnect 
  } = useWebSocket(taskId);

  const [realtimeStats, setRealtimeStats] = useState<RealtimeStatsType>({
    speed: 0,
    successRate: 0,
    errorCount: 0,
    totalProcessed: 0,
    estimatedTimeRemaining: 0,
  });

  // 更新实时统计数据
  useEffect(() => {
    const updateStats = () => {
      if (!crawlStatus) return;

      const { progress } = crawlStatus;
      const totalProcessed = progress.collectedCount + progress.failedCount;
      const successRate = calculateSuccessRate(progress.collectedCount, totalProcessed);
      
      // 计算速度 (简单估算，基于处理数量和时间)
      let speed = 0;
      if (crawlStatus.startTime) {
        const startTime = new Date(crawlStatus.startTime).getTime();
        const currentTime = Date.now();
        const elapsedMinutes = (currentTime - startTime) / (1000 * 60);
        if (elapsedMinutes > 0) {
          speed = totalProcessed / elapsedMinutes;
        }
      }

      // 计算预计剩余时间
      const estimatedTimeRemaining = calculateEstimatedTime(
        progress.currentPage,
        progress.totalPages,
        speed
      );

      setRealtimeStats({
        speed,
        successRate,
        errorCount: progress.failedCount,
        totalProcessed,
        estimatedTimeRemaining,
      });
    };

    // 立即更新一次
    updateStats();

    // 设置定时更新
    const interval = setInterval(updateStats, refreshInterval);
    return () => clearInterval(interval);
  }, [crawlStatus, refreshInterval]);

  // 如果没有爬虫状态数据，显示空状态
  if (!crawlStatus) {
    return (
      <div className={className}>
        <Card>
          <div style={{ textAlign: 'center', padding: '40px 0' }}>
            <Spin size="large" />
            <div style={{ marginTop: '16px' }}>
              等待爬虫状态数据...
            </div>
          </div>
        </Card>
      </div>
    );
  }

  const { progress } = crawlStatus;

  // 模拟关键词数据 (实际应该从后端获取)
  const keywordData = {
    currentKeyword: crawlStatus.currentKeyword,
    completedKeywords: [], // 实际应该从后端获取
    failedKeywords: [], // 实际应该从后端获取
    totalKeywords: 1, // 实际应该从后端获取
  };

  return (
    <div className={className}>
      <Space direction="vertical" style={{ width: '100%' }} size="large">
        {/* 错误提示 */}
        {error && (
          <Alert
            message="系统错误"
            description={error}
            type="error"
            closable
            showIcon
          />
        )}

        {/* 第一行：整体进度和连接状态 */}
        <Row gutter={[16, 16]}>
          <Col xs={24} md={16}>
            <Card title="整体进度" size="small">
              <Space direction="vertical" style={{ width: '100%' }}>
                {/* 页面进度 */}
                <EnhancedProgressBar
                  label="页面进度"
                  current={progress.currentPage}
                  total={progress.totalPages}
                  type="line"
                  status={crawlStatus.isRunning ? 'active' : 'normal'}
                  estimatedTimeRemaining={realtimeStats.estimatedTimeRemaining}
                  speed={realtimeStats.speed}
                  showDetails={true}
                />

                {/* 环形进度图 */}
                <div style={{ textAlign: 'center', marginTop: '16px' }}>
                  <ProgressBar
                    current={progress.collectedCount}
                    total={progress.collectedCount + (progress.totalPages - progress.currentPage) * 20}
                    type="circle"
                    status={crawlStatus.isRunning ? 'active' : 'success'}
                    size="default"
                  />
                </div>
              </Space>
            </Card>
          </Col>

          <Col xs={24} md={8}>
            <ConnectionStatus
              isConnected={isConnected}
              connectionState={connectionState}
              latency={stats.averageLatency}
              reconnectAttempts={stats.reconnectAttempts}
              lastHeartbeat={stats.lastHeartbeat}
              messagesReceived={stats.messagesReceived}
              onReconnect={reconnect}
            />
          </Col>
        </Row>

        {/* 第二行：关键词进度和实时统计 */}
        <Row gutter={[16, 16]}>
          <Col xs={24} md={12}>
            <KeywordProgress
              currentKeyword={keywordData.currentKeyword}
              completedKeywords={keywordData.completedKeywords}
              failedKeywords={keywordData.failedKeywords}
              totalKeywords={keywordData.totalKeywords}
              animationDuration={500}
            />
          </Col>

          <Col xs={24} md={12}>
            <RealtimeStats
              speed={realtimeStats.speed}
              successRate={realtimeStats.successRate}
              errorCount={realtimeStats.errorCount}
              totalProcessed={realtimeStats.totalProcessed}
              estimatedTimeRemaining={realtimeStats.estimatedTimeRemaining}
              showChart={showAdvanced}
            />
          </Col>
        </Row>

        {/* 第三行：高级信息（可选） */}
        {showAdvanced && (
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <Card title="详细信息" size="small">
                <Row gutter={16}>
                  <Col span={6}>
                    <div>
                      <div style={{ color: '#666', fontSize: '12px' }}>开始时间</div>
                      <div style={{ fontWeight: 'bold' }}>
                        {crawlStatus.startTime 
                          ? new Date(crawlStatus.startTime).toLocaleString()
                          : '未开始'
                        }
                      </div>
                    </div>
                  </Col>
                  <Col span={6}>
                    <div>
                      <div style={{ color: '#666', fontSize: '12px' }}>运行状态</div>
                      <div style={{ fontWeight: 'bold', color: crawlStatus.isRunning ? '#52c41a' : '#666' }}>
                        {crawlStatus.isRunning ? '运行中' : '已停止'}
                      </div>
                    </div>
                  </Col>
                  <Col span={6}>
                    <div>
                      <div style={{ color: '#666', fontSize: '12px' }}>成功率</div>
                      <div style={{ fontWeight: 'bold', color: '#1890ff' }}>
                        {realtimeStats.successRate.toFixed(1)}%
                      </div>
                    </div>
                  </Col>
                  <Col span={6}>
                    <div>
                      <div style={{ color: '#666', fontSize: '12px' }}>WebSocket消息</div>
                      <div style={{ fontWeight: 'bold', color: '#52c41a' }}>
                        {stats.messagesReceived} 条
                      </div>
                    </div>
                  </Col>
                </Row>
              </Card>
            </Col>
          </Row>
        )}
      </Space>
    </div>
  );
};

export default ProgressMonitor;