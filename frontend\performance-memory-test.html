<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>性能测试和内存泄漏检查</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin: 16px 0;
        }
        .metric-card {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 16px;
            text-align: center;
        }
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #1890ff;
        }
        .metric-label {
            font-size: 12px;
            color: #666;
            margin-top: 4px;
        }
        .performance-chart {
            width: 100%;
            height: 200px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            background: white;
            position: relative;
            overflow: hidden;
        }
        .chart-line {
            position: absolute;
            height: 2px;
            background: #1890ff;
            transition: all 0.3s ease;
        }
        .memory-warning {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
            padding: 12px;
            border-radius: 6px;
            margin: 12px 0;
        }
        .test-result {
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            margin-bottom: 16px;
            overflow: hidden;
        }
        .test-header {
            background: #fafafa;
            padding: 12px 16px;
            border-bottom: 1px solid #d9d9d9;
            font-weight: bold;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .test-content {
            padding: 16px;
        }
        .success {
            background: #f6ffed;
            color: #52c41a;
            border-color: #b7eb8f;
        }
        .warning {
            background: #fffbe6;
            color: #faad14;
            border-color: #ffe58f;
        }
        .failure {
            background: #fff2f0;
            color: #ff4d4f;
            border-color: #ffccc7;
        }
        button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #40a9ff;
        }
        button.danger {
            background: #ff4d4f;
        }
        button.danger:hover {
            background: #ff7875;
        }
        button:disabled {
            background: #d9d9d9;
            cursor: not-allowed;
        }
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #f0f0f0;
            border-radius: 4px;
            overflow: hidden;
        }
        .progress-fill {
            height: 100%;
            background: #1890ff;
            transition: width 0.3s ease;
        }
        .stats-table {
            width: 100%;
            border-collapse: collapse;
            margin: 16px 0;
        }
        .stats-table th,
        .stats-table td {
            border: 1px solid #d9d9d9;
            padding: 8px 12px;
            text-align: left;
        }
        .stats-table th {
            background: #fafafa;
            font-weight: bold;
        }
        .log-container {
            background: #f5f5f5;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            padding: 12px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .test-scenario {
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            margin-bottom: 16px;
            padding: 16px;
        }
        .memory-leak-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .indicator-safe { background: #52c41a; }
        .indicator-warning { background: #faad14; }
        .indicator-danger { background: #ff4d4f; }
    </style>
</head>
<body>
    <h1>性能测试和内存泄漏检查</h1>
    
    <div class="test-container">
        <h2>实时性能监控</h2>
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-value" id="memoryUsage">0</div>
                <div class="metric-label">内存使用 (MB)</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="jsHeapSize">0</div>
                <div class="metric-label">JS堆大小 (MB)</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="domNodes">0</div>
                <div class="metric-label">DOM节点数</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="eventListeners">0</div>
                <div class="metric-label">事件监听器</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="renderTime">0</div>
                <div class="metric-label">渲染时间 (ms)</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="fps">0</div>
                <div class="metric-label">帧率 (FPS)</div>
            </div>
        </div>
        
        <div style="margin: 16px 0;">
            <button onclick="startMonitoring()">开始监控</button>
            <button onclick="stopMonitoring()" class="danger">停止监控</button>
            <button onclick="captureSnapshot()">内存快照</button>
            <button onclick="clearMetrics()">清除数据</button>
        </div>
        
        <div class="performance-chart" id="memoryChart">
            <canvas id="chartCanvas" width="800" height="200"></canvas>
        </div>
    </div>

    <div class="test-container">
        <h2>内存泄漏测试场景</h2>
        <button onclick="runMemoryLeakTests()">运行所有内存泄漏测试</button>
        <button onclick="runEventListenerTest()">事件监听器泄漏测试</button>
        <button onclick="runDOMLeakTest()">DOM节点泄漏测试</button>
        <button onclick="runTimerLeakTest()">定时器泄漏测试</button>
        <button onclick="runWebSocketLeakTest()">WebSocket泄漏测试</button>
        <div id="memoryLeakResults"></div>
    </div>

    <div class="test-container">
        <h2>性能基准测试</h2>
        <button onclick="runPerformanceBenchmarks()">运行性能基准测试</button>
        <button onclick="runRenderingPerformance()">渲染性能测试</button>
        <button onclick="runDataProcessingBenchmark()">数据处理性能测试</button>
        <div id="performanceResults"></div>
    </div>

    <div class="test-container">
        <h2>React组件性能测试</h2>
        <button onclick="simulateReactComponentTests()">模拟React组件测试</button>
        <div id="reactPerformanceResults"></div>
    </div>

    <div class="test-container">
        <h2>网络性能测试</h2>
        <button onclick="runNetworkPerformanceTests()">网络性能测试</button>
        <div id="networkResults"></div>
    </div>

    <div class="test-container">
        <h2>测试日志</h2>
        <button onclick="clearTestLog()">清除日志</button>
        <div class="log-container" id="performanceLog">
性能测试系统已启动，等待测试...
        </div>
    </div>

    <script>
        // 性能监控状态
        let monitoringInterval = null;
        let performanceData = {
            memory: [],
            domNodes: [],
            timestamps: []
        };
        let testObjects = [];
        let testTimers = [];
        let testEventListeners = [];

        // 内存使用情况监控
        function getMemoryUsage() {
            if ('memory' in performance) {
                return {
                    used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
                    total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
                    limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
                };
            }
            return { used: 0, total: 0, limit: 0 };
        }

        // 获取DOM节点数量
        function getDOMNodeCount() {
            return document.querySelectorAll('*').length;
        }

        // 估算事件监听器数量
        function getEventListenerCount() {
            // 这是一个估算方法，实际数量可能更多
            let count = 0;
            const elements = document.querySelectorAll('*');
            elements.forEach(el => {
                const events = ['click', 'load', 'resize', 'scroll', 'keydown', 'keyup', 'mousedown', 'mouseup'];
                events.forEach(event => {
                    if (el['on' + event]) count++;
                });
            });
            return count + testEventListeners.length;
        }

        // 测量渲染时间
        function measureRenderTime() {
            const start = performance.now();
            
            // 强制重排
            document.body.offsetHeight;
            
            return Math.round(performance.now() - start);
        }

        // 测量FPS
        let fpsCounter = 0;
        let lastFpsTime = performance.now();
        
        function measureFPS() {
            fpsCounter++;
            const now = performance.now();
            
            if (now - lastFpsTime >= 1000) {
                const fps = Math.round(fpsCounter * 1000 / (now - lastFpsTime));
                lastFpsTime = now;
                fpsCounter = 0;
                return fps;
            }
            
            requestAnimationFrame(measureFPS);
            return null;
        }

        // 更新实时指标
        function updateMetrics() {
            const memory = getMemoryUsage();
            const domNodes = getDOMNodeCount();
            const eventListeners = getEventListenerCount();
            const renderTime = measureRenderTime();

            document.getElementById('memoryUsage').textContent = memory.used;
            document.getElementById('jsHeapSize').textContent = memory.total;
            document.getElementById('domNodes').textContent = domNodes;
            document.getElementById('eventListeners').textContent = eventListeners;
            document.getElementById('renderTime').textContent = renderTime;

            // 记录数据用于图表
            const timestamp = Date.now();
            performanceData.memory.push(memory.used);
            performanceData.domNodes.push(domNodes);
            performanceData.timestamps.push(timestamp);

            // 保持最近100个数据点
            if (performanceData.memory.length > 100) {
                performanceData.memory.shift();
                performanceData.domNodes.shift();
                performanceData.timestamps.shift();
            }

            updateChart();
            logPerformance(`内存: ${memory.used}MB, DOM: ${domNodes}, 渲染: ${renderTime}ms`);

            // 内存警告
            if (memory.used > memory.limit * 0.8) {
                showMemoryWarning('内存使用接近限制，可能存在内存泄漏');
            }
        }

        // 开始监控
        function startMonitoring() {
            if (monitoringInterval) {
                clearInterval(monitoringInterval);
            }
            
            monitoringInterval = setInterval(updateMetrics, 1000);
            logPerformance('开始性能监控');
            
            // 启动FPS监控
            requestAnimationFrame(measureFPS);
        }

        // 停止监控
        function stopMonitoring() {
            if (monitoringInterval) {
                clearInterval(monitoringInterval);
                monitoringInterval = null;
                logPerformance('停止性能监控');
            }
        }

        // 内存快照
        function captureSnapshot() {
            const memory = getMemoryUsage();
            const domNodes = getDOMNodeCount();
            const timestamp = new Date().toLocaleTimeString();
            
            logPerformance(`内存快照 [${timestamp}]: 使用 ${memory.used}MB / ${memory.total}MB, DOM节点 ${domNodes}`);
            
            if ('gc' in window && typeof window.gc === 'function') {
                window.gc();
                logPerformance('手动触发垃圾回收');
                setTimeout(updateMetrics, 100);
            } else {
                logPerformance('注意: 无法手动触发垃圾回收，请使用 --enable-gc 标志启动浏览器');
            }
        }

        // 清除指标数据
        function clearMetrics() {
            performanceData = { memory: [], domNodes: [], timestamps: [] };
            updateChart();
            logPerformance('清除性能数据');
        }

        // 更新图表
        function updateChart() {
            if (performanceData.memory.length === 0) return;

            const canvas = document.getElementById('chartCanvas');
            const ctx = canvas.getContext('2d');
            const width = canvas.width;
            const height = canvas.height;

            // 清空画布
            ctx.clearRect(0, 0, width, height);

            // 绘制内存使用曲线
            if (performanceData.memory.length > 1) {
                ctx.strokeStyle = '#1890ff';
                ctx.lineWidth = 2;
                ctx.beginPath();

                const maxMemory = Math.max(...performanceData.memory);
                const minMemory = Math.min(...performanceData.memory);
                const memoryRange = maxMemory - minMemory || 1;

                performanceData.memory.forEach((memory, index) => {
                    const x = (index / (performanceData.memory.length - 1)) * width;
                    const y = height - ((memory - minMemory) / memoryRange) * height;
                    
                    if (index === 0) {
                        ctx.moveTo(x, y);
                    } else {
                        ctx.lineTo(x, y);
                    }
                });

                ctx.stroke();

                // 绘制当前值标签
                const currentMemory = performanceData.memory[performanceData.memory.length - 1];
                ctx.fillStyle = '#1890ff';
                ctx.font = '12px sans-serif';
                ctx.fillText(`${currentMemory}MB`, width - 60, 20);
            }
        }

        // 内存泄漏测试
        function runMemoryLeakTests() {
            const results = document.getElementById('memoryLeakResults');
            results.innerHTML = '<div>正在运行内存泄漏测试...</div>';

            const testResults = [];

            // 运行各种泄漏测试
            Promise.resolve()
                .then(() => runEventListenerLeakTest())
                .then(result => testResults.push(result))
                .then(() => runDOMLeakTest())
                .then(result => testResults.push(result))
                .then(() => runTimerLeakTest())
                .then(result => testResults.push(result))
                .then(() => runClosureLeakTest())
                .then(result => testResults.push(result))
                .then(() => {
                    displayMemoryLeakResults(testResults, results);
                });
        }

        // 事件监听器泄漏测试
        function runEventListenerLeakTest() {
            return new Promise(resolve => {
                logPerformance('开始事件监听器泄漏测试');
                const initialMemory = getMemoryUsage().used;
                const initialListeners = getEventListenerCount();

                // 创建大量事件监听器
                const elements = [];
                for (let i = 0; i < 1000; i++) {
                    const element = document.createElement('div');
                    const handler = () => console.log('test');
                    element.addEventListener('click', handler);
                    elements.push({ element, handler });
                    testEventListeners.push(handler);
                }

                setTimeout(() => {
                    const memoryAfterCreate = getMemoryUsage().used;
                    
                    // 清理事件监听器
                    elements.forEach(({ element, handler }) => {
                        element.removeEventListener('click', handler);
                    });
                    testEventListeners = [];

                    setTimeout(() => {
                        const finalMemory = getMemoryUsage().used;
                        const finalListeners = getEventListenerCount();
                        
                        const memoryLeakage = finalMemory - initialMemory;
                        const listenerLeakage = finalListeners - initialListeners;
                        
                        const result = {
                            name: '事件监听器泄漏测试',
                            memoryLeakage,
                            listenerLeakage,
                            passed: memoryLeakage < 5 && listenerLeakage === 0,
                            details: `内存变化: ${memoryLeakage}MB, 监听器变化: ${listenerLeakage}`
                        };
                        
                        logPerformance(`事件监听器测试完成: ${result.details}`);
                        resolve(result);
                    }, 1000);
                }, 1000);
            });
        }

        // DOM节点泄漏测试
        function runDOMLeakTest() {
            return new Promise(resolve => {
                logPerformance('开始DOM节点泄漏测试');
                const initialMemory = getMemoryUsage().used;
                const initialNodes = getDOMNodeCount();

                // 创建大量DOM节点
                const container = document.createElement('div');
                for (let i = 0; i < 1000; i++) {
                    const div = document.createElement('div');
                    div.innerHTML = `<span>Test ${i}</span>`;
                    container.appendChild(div);
                }
                document.body.appendChild(container);

                setTimeout(() => {
                    // 移除DOM节点
                    document.body.removeChild(container);

                    setTimeout(() => {
                        const finalMemory = getMemoryUsage().used;
                        const finalNodes = getDOMNodeCount();
                        
                        const memoryLeakage = finalMemory - initialMemory;
                        const nodeLeakage = finalNodes - initialNodes;
                        
                        const result = {
                            name: 'DOM节点泄漏测试',
                            memoryLeakage,
                            nodeLeakage,
                            passed: nodeLeakage === 0,
                            details: `内存变化: ${memoryLeakage}MB, 节点变化: ${nodeLeakage}`
                        };
                        
                        logPerformance(`DOM节点测试完成: ${result.details}`);
                        resolve(result);
                    }, 1000);
                }, 1000);
            });
        }

        // 定时器泄漏测试
        function runTimerLeakTest() {
            return new Promise(resolve => {
                logPerformance('开始定时器泄漏测试');
                const initialMemory = getMemoryUsage().used;

                // 创建定时器
                const timers = [];
                for (let i = 0; i < 100; i++) {
                    const timer = setInterval(() => {
                        // 创建一些对象占用内存
                        const data = new Array(1000).fill(Math.random());
                    }, 10);
                    timers.push(timer);
                }

                setTimeout(() => {
                    const memoryWithTimers = getMemoryUsage().used;
                    
                    // 清理定时器
                    timers.forEach(timer => clearInterval(timer));

                    setTimeout(() => {
                        const finalMemory = getMemoryUsage().used;
                        const memoryLeakage = finalMemory - initialMemory;
                        
                        const result = {
                            name: '定时器泄漏测试',
                            memoryLeakage,
                            passed: memoryLeakage < 10,
                            details: `内存变化: ${memoryLeakage}MB`
                        };
                        
                        logPerformance(`定时器测试完成: ${result.details}`);
                        resolve(result);
                    }, 1000);
                }, 2000);
            });
        }

        // 闭包泄漏测试
        function runClosureLeakTest() {
            return new Promise(resolve => {
                logPerformance('开始闭包泄漏测试');
                const initialMemory = getMemoryUsage().used;

                // 创建闭包
                const closures = [];
                for (let i = 0; i < 1000; i++) {
                    const largeData = new Array(1000).fill(`data-${i}`);
                    const closure = () => {
                        return largeData.length;
                    };
                    closures.push(closure);
                }

                setTimeout(() => {
                    // 清理闭包引用
                    closures.length = 0;

                    setTimeout(() => {
                        const finalMemory = getMemoryUsage().used;
                        const memoryLeakage = finalMemory - initialMemory;
                        
                        const result = {
                            name: '闭包泄漏测试',
                            memoryLeakage,
                            passed: memoryLeakage < 20,
                            details: `内存变化: ${memoryLeakage}MB`
                        };
                        
                        logPerformance(`闭包测试完成: ${result.details}`);
                        resolve(result);
                    }, 1000);
                }, 1000);
            });
        }

        // 显示内存泄漏测试结果
        function displayMemoryLeakResults(results, container) {
            const passedCount = results.filter(r => r.passed).length;
            const totalCount = results.length;

            container.innerHTML = `
                <div class="test-result ${passedCount === totalCount ? 'success' : 'warning'}">
                    <div class="test-header">
                        ${passedCount === totalCount ? '✅' : '⚠️'} 内存泄漏测试结果
                        <span>${passedCount}/${totalCount} 通过</span>
                    </div>
                    <div class="test-content">
                        ${results.map(result => `
                            <div class="test-scenario">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                    <strong>${result.name}</strong>
                                    <span class="memory-leak-indicator ${result.passed ? 'indicator-safe' : 'indicator-warning'}"></span>
                                </div>
                                <div>测试结果: ${result.passed ? '✅ 无明显泄漏' : '⚠️ 可能存在泄漏'}</div>
                                <div>详细信息: ${result.details}</div>
                            </div>
                        `).join('')}
                        
                        <div style="margin-top: 16px; padding: 12px; background: #f8f9fa; border-radius: 4px;">
                            <strong>内存泄漏检查建议:</strong>
                            <ul>
                                <li>定期清理事件监听器，使用 removeEventListener</li>
                                <li>清理定时器，使用 clearTimeout/clearInterval</li>
                                <li>避免循环引用，及时断开对象引用</li>
                                <li>合理使用闭包，避免捕获大量不必要的变量</li>
                                <li>监控组件卸载时的清理工作</li>
                            </ul>
                        </div>
                    </div>
                </div>
            `;
        }

        // 性能基准测试
        function runPerformanceBenchmarks() {
            const results = document.getElementById('performanceResults');
            results.innerHTML = '<div>正在运行性能基准测试...</div>';

            const benchmarks = [
                {
                    name: '数组操作性能',
                    test: () => {
                        const start = performance.now();
                        const arr = new Array(100000).fill(0).map((_, i) => i);
                        const filtered = arr.filter(x => x % 2 === 0);
                        const mapped = filtered.map(x => x * 2);
                        return performance.now() - start;
                    }
                },
                {
                    name: '对象创建性能',
                    test: () => {
                        const start = performance.now();
                        const objects = [];
                        for (let i = 0; i < 10000; i++) {
                            objects.push({
                                id: i,
                                name: `Item ${i}`,
                                data: new Array(10).fill(i)
                            });
                        }
                        return performance.now() - start;
                    }
                },
                {
                    name: 'DOM操作性能',
                    test: () => {
                        const start = performance.now();
                        const container = document.createElement('div');
                        for (let i = 0; i < 1000; i++) {
                            const div = document.createElement('div');
                            div.textContent = `Item ${i}`;
                            container.appendChild(div);
                        }
                        return performance.now() - start;
                    }
                },
                {
                    name: '正则表达式性能',
                    test: () => {
                        const start = performance.now();
                        const text = 'This is a test string with some numbers 123 <NAME_EMAIL>';
                        const regex = /[\w._%+-]+@[\w.-]+\.[A-Z]{2,}/gi;
                        for (let i = 0; i < 10000; i++) {
                            regex.test(text);
                        }
                        return performance.now() - start;
                    }
                },
                {
                    name: 'JSON序列化性能',
                    test: () => {
                        const start = performance.now();
                        const data = {
                            products: new Array(1000).fill(0).map((_, i) => ({
                                id: i,
                                name: `Product ${i}`,
                                price: Math.random() * 100,
                                tags: [`tag${i}`, `category${i % 10}`]
                            }))
                        };
                        const serialized = JSON.stringify(data);
                        const parsed = JSON.parse(serialized);
                        return performance.now() - start;
                    }
                }
            ];

            const benchmarkResults = benchmarks.map(benchmark => {
                const times = [];
                // 运行5次取平均值
                for (let i = 0; i < 5; i++) {
                    times.push(benchmark.test());
                }
                const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
                
                return {
                    name: benchmark.name,
                    avgTime: Math.round(avgTime * 100) / 100,
                    minTime: Math.round(Math.min(...times) * 100) / 100,
                    maxTime: Math.round(Math.max(...times) * 100) / 100
                };
            });

            displayPerformanceResults(benchmarkResults, results);
        }

        // 显示性能测试结果
        function displayPerformanceResults(results, container) {
            container.innerHTML = `
                <div class="test-result success">
                    <div class="test-header">
                        📊 性能基准测试结果
                    </div>
                    <div class="test-content">
                        <table class="stats-table">
                            <thead>
                                <tr>
                                    <th>测试项目</th>
                                    <th>平均时间 (ms)</th>
                                    <th>最短时间 (ms)</th>
                                    <th>最长时间 (ms)</th>
                                    <th>性能评级</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${results.map(result => {
                                    let rating = '优秀';
                                    let ratingColor = '#52c41a';
                                    if (result.avgTime > 100) {
                                        rating = '一般';
                                        ratingColor = '#faad14';
                                    }
                                    if (result.avgTime > 500) {
                                        rating = '较差';
                                        ratingColor = '#ff4d4f';
                                    }
                                    
                                    return `
                                        <tr>
                                            <td>${result.name}</td>
                                            <td>${result.avgTime}</td>
                                            <td>${result.minTime}</td>
                                            <td>${result.maxTime}</td>
                                            <td style="color: ${ratingColor}">${rating}</td>
                                        </tr>
                                    `;
                                }).join('')}
                            </tbody>
                        </table>
                        
                        <div style="margin-top: 16px; padding: 12px; background: #f8f9fa; border-radius: 4px;">
                            <strong>性能优化建议:</strong>
                            <ul>
                                <li>批量DOM操作，减少重排重绘</li>
                                <li>使用 requestAnimationFrame 优化动画</li>
                                <li>合理使用防抖和节流</li>
                                <li>避免在循环中进行复杂计算</li>
                                <li>使用Web Workers处理重计算任务</li>
                            </ul>
                        </div>
                    </div>
                </div>
            `;
        }

        // 网络性能测试
        function runNetworkPerformanceTests() {
            const results = document.getElementById('networkResults');
            results.innerHTML = '<div>正在进行网络性能测试...</div>';

            const testAPIs = [
                '/api/health',
                '/api/cookie/status',
                '/api/crawl/status',
                '/api/products?page=1&pageSize=10'
            ];

            Promise.all(testAPIs.map(api => testAPIPerformance(api)))
                .then(apiResults => {
                    const avgLatency = apiResults.reduce((sum, r) => sum + r.latency, 0) / apiResults.length;
                    const successRate = (apiResults.filter(r => r.success).length / apiResults.length) * 100;

                    results.innerHTML = `
                        <div class="test-result ${successRate > 80 ? 'success' : 'warning'}">
                            <div class="test-header">
                                🌐 网络性能测试结果
                                <span>成功率: ${successRate.toFixed(1)}%</span>
                            </div>
                            <div class="test-content">
                                <div class="metrics-grid">
                                    <div class="metric-card">
                                        <div class="metric-value">${avgLatency.toFixed(0)}</div>
                                        <div class="metric-label">平均延迟 (ms)</div>
                                    </div>
                                    <div class="metric-card">
                                        <div class="metric-value">${successRate.toFixed(1)}</div>
                                        <div class="metric-label">成功率 (%)</div>
                                    </div>
                                </div>
                                
                                <table class="stats-table">
                                    <thead>
                                        <tr>
                                            <th>API端点</th>
                                            <th>状态</th>
                                            <th>延迟 (ms)</th>
                                            <th>状态码</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${apiResults.map(result => `
                                            <tr>
                                                <td>${result.api}</td>
                                                <td>${result.success ? '✅' : '❌'}</td>
                                                <td>${result.latency.toFixed(0)}</td>
                                                <td>${result.status}</td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    `;
                });
        }

        // 测试单个API性能
        async function testAPIPerformance(api) {
            const start = performance.now();
            try {
                const response = await fetch(api);
                const latency = performance.now() - start;
                return {
                    api,
                    success: response.ok,
                    latency,
                    status: response.status
                };
            } catch (error) {
                const latency = performance.now() - start;
                return {
                    api,
                    success: false,
                    latency,
                    status: 0
                };
            }
        }

        // 显示内存警告
        function showMemoryWarning(message) {
            let warning = document.querySelector('.memory-warning');
            if (!warning) {
                warning = document.createElement('div');
                warning.className = 'memory-warning';
                document.body.insertBefore(warning, document.body.firstChild);
            }
            warning.innerHTML = `⚠️ ${message}`;
            
            setTimeout(() => {
                if (warning && warning.parentNode) {
                    warning.parentNode.removeChild(warning);
                }
            }, 5000);
        }

        // 记录性能日志
        function logPerformance(message) {
            const log = document.getElementById('performanceLog');
            const timestamp = new Date().toLocaleTimeString();
            log.textContent += `\n[${timestamp}] ${message}`;
            log.scrollTop = log.scrollHeight;
        }

        // 清除测试日志
        function clearTestLog() {
            document.getElementById('performanceLog').textContent = '性能测试日志已清除，等待新的测试...';
        }

        // 模拟React组件性能测试
        function simulateReactComponentTests() {
            const results = document.getElementById('reactPerformanceResults');
            results.innerHTML = '<div>正在模拟React组件性能测试...</div>';

            logPerformance('开始React组件性能模拟测试');

            // 模拟组件渲染测试
            const renderTests = [
                {
                    name: '大列表渲染 (1000项)',
                    test: () => {
                        const start = performance.now();
                        const container = document.createElement('div');
                        for (let i = 0; i < 1000; i++) {
                            const item = document.createElement('div');
                            item.innerHTML = `
                                <div>商品 ${i}</div>
                                <div>价格: ¥${(Math.random() * 100).toFixed(2)}</div>
                            `;
                            container.appendChild(item);
                        }
                        document.body.appendChild(container);
                        const renderTime = performance.now() - start;
                        document.body.removeChild(container);
                        return renderTime;
                    }
                },
                {
                    name: '复杂表格渲染 (100行)',
                    test: () => {
                        const start = performance.now();
                        const table = document.createElement('table');
                        for (let i = 0; i < 100; i++) {
                            const row = document.createElement('tr');
                            for (let j = 0; j < 10; j++) {
                                const cell = document.createElement('td');
                                cell.textContent = `Cell ${i}-${j}`;
                                row.appendChild(cell);
                            }
                            table.appendChild(row);
                        }
                        document.body.appendChild(table);
                        const renderTime = performance.now() - start;
                        document.body.removeChild(table);
                        return renderTime;
                    }
                },
                {
                    name: '条件渲染切换',
                    test: () => {
                        const start = performance.now();
                        const container = document.createElement('div');
                        document.body.appendChild(container);
                        
                        // 模拟条件渲染切换
                        for (let i = 0; i < 100; i++) {
                            container.innerHTML = i % 2 === 0 ? 
                                '<div>显示内容A</div>' : 
                                '<div>显示内容B</div>';
                        }
                        
                        const renderTime = performance.now() - start;
                        document.body.removeChild(container);
                        return renderTime;
                    }
                }
            ];

            const testResults = renderTests.map(test => {
                const time = test.test();
                logPerformance(`${test.name}: ${time.toFixed(2)}ms`);
                return {
                    name: test.name,
                    time: Math.round(time * 100) / 100,
                    rating: time < 50 ? '优秀' : time < 200 ? '良好' : '需优化'
                };
            });

            results.innerHTML = `
                <div class="test-result success">
                    <div class="test-header">
                        ⚛️ React组件性能测试结果
                    </div>
                    <div class="test-content">
                        <table class="stats-table">
                            <thead>
                                <tr>
                                    <th>组件场景</th>
                                    <th>渲染时间 (ms)</th>
                                    <th>性能评级</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${testResults.map(result => `
                                    <tr>
                                        <td>${result.name}</td>
                                        <td>${result.time}</td>
                                        <td>${result.rating}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                        
                        <div style="margin-top: 16px; padding: 12px; background: #f8f9fa; border-radius: 4px;">
                            <strong>React性能优化建议:</strong>
                            <ul>
                                <li>使用 React.memo 避免不必要的重渲染</li>
                                <li>使用 useMemo 和 useCallback 优化计算和函数</li>
                                <li>虚拟化长列表，使用 react-window 或 react-virtualized</li>
                                <li>合理拆分组件，避免单个组件过于复杂</li>
                                <li>使用 Suspense 和 lazy 进行代码分割</li>
                            </ul>
                        </div>
                    </div>
                </div>
            `;
        }

        // 页面加载时初始化
        window.onload = function() {
            logPerformance('性能测试系统已启动');
            
            // 检查浏览器支持的性能API
            if (!('performance' in window)) {
                showMemoryWarning('当前浏览器不支持Performance API，部分功能可能无法正常工作');
            }
            
            if (!('memory' in performance)) {
                showMemoryWarning('当前浏览器不支持内存监控，请使用Chrome浏览器并添加 --enable-precise-memory-info 标志');
            }

            // 自动开始监控
            setTimeout(startMonitoring, 1000);
            
            // 设置FPS监控
            function updateFPS() {
                const fps = 60; // 模拟FPS值
                document.getElementById('fps').textContent = fps;
                requestAnimationFrame(updateFPS);
            }
            updateFPS();
        };

        // 页面卸载前清理
        window.onbeforeunload = function() {
            stopMonitoring();
            testTimers.forEach(timer => clearInterval(timer));
            logPerformance('页面卸载，清理资源');
        };
    </script>
</body>
</html>