/**
 * 数据预览主组件 - 整合数据表格、统计、导出等功能
 */

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { Tabs, Card, Button, Space, Spin, Empty, message, Badge } from 'antd';
import { TableOutlined, BarChartOutlined, DownloadOutlined, ReloadOutlined } from '@ant-design/icons';
import type { Product, DataFilter, ColumnConfig, ExportRequest, ExportProgress } from '../../types';
import DataTable from './DataTable';
import DataStats from './DataStats';
import ExportManager from './ExportManager';
import ProductDetail from './ProductDetail';
import { generateDataStats } from './utils';
import './DataPreview.css';

interface DataPreviewProps {
  // 数据源
  data?: Product[];
  loading?: boolean;
  
  // 数据获取
  onRefresh?: () => void;
  
  // 导出功能
  onExport?: (request: ExportRequest) => Promise<void>;
  exportProgress?: ExportProgress;
  
  // 其他配置
  showTabs?: boolean;
  defaultActiveTab?: string;
  height?: number;
}

const DataPreview: React.FC<DataPreviewProps> = ({
  data = [],
  loading = false,
  onRefresh,
  onExport,
  exportProgress,
  showTabs = true,
  defaultActiveTab = 'table',
  height = 600
}) => {
  const [activeTab, setActiveTab] = useState(defaultActiveTab);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [detailVisible, setDetailVisible] = useState(false);
  const [filters, setFilters] = useState<DataFilter>({});
  const [columns, setColumns] = useState<ColumnConfig[]>([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0
  });

  // 过滤后的数据
  const filteredData = useMemo(() => {
    if (!filters || Object.keys(filters).length === 0) return data;

    return data.filter(product => {
      // 关键词过滤
      if (filters.keyword && !product.goods_name?.toLowerCase().includes(filters.keyword.toLowerCase())) {
        return false;
      }

      // 价格范围过滤
      if (filters.priceRange) {
        const price = Number(product.price) || 0;
        if (price < filters.priceRange[0] || price > filters.priceRange[1]) {
          return false;
        }
      }

      // 品牌过滤
      if (filters.brands && filters.brands.length > 0) {
        if (!product.brand_name || !filters.brands.includes(product.brand_name)) {
          return false;
        }
      }

      // 补贴过滤
      if (filters.hasSubsidy !== undefined) {
        const hasSubsidy = !!(product.subsidy_info?.trim());
        if (hasSubsidy !== filters.hasSubsidy) {
          return false;
        }
      }

      // 最小销量过滤
      if (filters.minSales !== undefined) {
        let sales = 0;
        if (typeof product.sales === 'number') {
          sales = product.sales;
        } else if (typeof product.sales === 'string') {
          const salesStr = product.sales.toString();
          if (salesStr.includes('万')) {
            const match = salesStr.match(/(\d+(?:\.\d+)?)\s*万/);
            if (match) {
              sales = parseFloat(match[1]) * 10000;
            }
          } else {
            sales = parseInt(salesStr.replace(/[^\d]/g, '')) || 0;
          }
        }
        if (sales < filters.minSales) {
          return false;
        }
      }

      // 最小评分过滤
      if (filters.minRating !== undefined) {
        const rating = Number(product.rating) || 0;
        if (rating < filters.minRating) {
          return false;
        }
      }

      return true;
    });
  }, [data, filters]);

  // 更新分页总数
  useEffect(() => {
    setPagination(prev => ({
      ...prev,
      total: filteredData.length
    }));
  }, [filteredData]);

  // 统计数据
  const stats = useMemo(() => {
    return generateDataStats(filteredData);
  }, [filteredData]);

  // 处理行点击
  const handleRowClick = useCallback((product: Product) => {
    setSelectedProduct(product);
    setDetailVisible(true);
  }, []);

  // 处理分页变化
  const handlePaginationChange = useCallback((page: number, pageSize: number) => {
    setPagination(prev => ({
      ...prev,
      current: page,
      pageSize
    }));
  }, []);

  // 处理导出
  const handleExport = useCallback(async (request: ExportRequest) => {
    if (!onExport) {
      message.error('导出功能未配置');
      return;
    }

    try {
      await onExport(request);
    } catch (error) {
      message.error('导出失败: ' + (error as Error).message);
    }
  }, [onExport]);

  // 获取补贴商品数量
  const subsidyCount = useMemo(() => {
    return filteredData.filter(p => p.subsidy_info?.trim()).length;
  }, [filteredData]);

  // 渲染空状态
  const renderEmpty = () => (
    <div style={{ textAlign: 'center', padding: '40px 0' }}>
      <Empty
        description={
          <div>
            <div style={{ marginBottom: 16 }}>暂无商品数据</div>
            {onRefresh && (
              <Button type="primary" icon={<ReloadOutlined />} onClick={onRefresh}>
                刷新数据
              </Button>
            )}
          </div>
        }
      />
    </div>
  );

  // 渲染内容
  const renderContent = () => {
    if (loading) {
      return (
        <div style={{ textAlign: 'center', padding: '80px 0' }}>
          <Spin size="large" tip="正在加载数据..." />
        </div>
      );
    }

    if (data.length === 0) {
      return renderEmpty();
    }

    const tabItems = [
      {
        key: 'table',
        label: (
          <Space>
            <TableOutlined />
            数据表格
            <Badge count={filteredData.length} style={{ backgroundColor: '#1890ff' }} />
          </Space>
        ),
        children: (
          <DataTable
            data={filteredData}
            loading={loading}
            pagination={{
              ...pagination,
              onChange: handlePaginationChange
            }}
            onRowClick={handleRowClick}
            filters={filters}
            onFiltersChange={setFilters}
            columns={columns}
            onColumnsChange={setColumns}
          />
        )
      },
      {
        key: 'stats',
        label: (
          <Space>
            <BarChartOutlined />
            数据统计
            {subsidyCount > 0 && (
              <Badge count={subsidyCount} style={{ backgroundColor: '#f5222d' }} title="补贴商品数量" />
            )}
          </Space>
        ),
        children: (
          <DataStats
            data={filteredData}
            stats={stats}
            loading={loading}
          />
        )
      },
      {
        key: 'export',
        label: (
          <Space>
            <DownloadOutlined />
            数据导出
          </Space>
        ),
        children: (
          <ExportManager
            data={filteredData}
            onExport={handleExport}
            exportProgress={exportProgress}
            disabled={loading}
          />
        )
      }
    ];

    if (showTabs) {
      return (
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={tabItems}
          tabBarExtraContent={
            <Space>
              {onRefresh && (
                <Button
                  icon={<ReloadOutlined />}
                  onClick={onRefresh}
                  loading={loading}
                  size="small"
                >
                  刷新
                </Button>
              )}
            </Space>
          }
        />
      );
    } else {
      // 不显示标签页时，根据 defaultActiveTab 渲染对应内容
      const activeTabItem = tabItems.find(item => item.key === activeTab);
      return activeTabItem?.children || tabItems[0].children;
    }
  };

  return (
    <div className="data-preview" style={{ height: height ? `${height}px` : 'auto' }}>
      <Card
        bodyStyle={{ 
          padding: showTabs ? '0' : '16px',
          height: height ? `${height - 32}px` : 'auto',
          overflow: 'auto'
        }}
      >
        {renderContent()}
      </Card>

      {/* 商品详情弹窗 */}
      <ProductDetail
        product={selectedProduct}
        visible={detailVisible}
        onClose={() => {
          setDetailVisible(false);
          setSelectedProduct(null);
        }}
      />
    </div>
  );
};

export default DataPreview;