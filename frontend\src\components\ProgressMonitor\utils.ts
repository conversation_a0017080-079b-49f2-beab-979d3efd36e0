// ProgressMonitor 工具函数
import type { ChartDataPoint } from './types';

/**
 * 格式化时间为可读字符串
 * @param seconds 秒数
 * @returns 格式化的时间字符串
 */
export const formatTime = (seconds: number): string => {
  if (seconds < 60) {
    return `${Math.round(seconds)}秒`;
  } else if (seconds < 3600) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.round(seconds % 60);
    return `${minutes}分${remainingSeconds}秒`;
  } else {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}小时${minutes}分钟`;
  }
};

/**
 * 格式化速度
 * @param speed 速度 (条/分钟)
 * @returns 格式化的速度字符串
 */
export const formatSpeed = (speed: number): string => {
  if (speed < 1) {
    return `${(speed * 60).toFixed(1)} 条/小时`;
  } else if (speed < 60) {
    return `${speed.toFixed(1)} 条/分钟`;
  } else {
    return `${(speed / 60).toFixed(1)} 条/秒`;
  }
};

/**
 * 格式化百分比
 * @param value 数值
 * @param total 总数
 * @returns 百分比字符串
 */
export const formatPercentage = (value: number, total: number): string => {
  if (total === 0) return '0%';
  return `${((value / total) * 100).toFixed(1)}%`;
};

/**
 * 计算成功率
 * @param success 成功数量
 * @param total 总数量
 * @returns 成功率百分比
 */
export const calculateSuccessRate = (success: number, total: number): number => {
  if (total === 0) return 0;
  return (success / total) * 100;
};

/**
 * 计算预计剩余时间
 * @param current 当前进度
 * @param total 总进度
 * @param speed 速度 (单位/分钟)
 * @returns 预计剩余时间 (秒)
 */
export const calculateEstimatedTime = (
  current: number,
  total: number,
  speed: number
): number => {
  if (speed <= 0 || current >= total) return 0;
  const remaining = total - current;
  return (remaining / speed) * 60; // 转换为秒
};

/**
 * 更新图表数据
 * @param chartData 当前图表数据
 * @param newValue 新数值
 * @param maxPoints 最大数据点数量
 * @returns 更新后的图表数据
 */
export const updateChartData = (
  chartData: ChartDataPoint[],
  newValue: number,
  maxPoints: number = 50
): ChartDataPoint[] => {
  const newPoint: ChartDataPoint = {
    timestamp: Date.now(),
    value: newValue,
  };

  const updatedData = [...chartData, newPoint];
  
  // 限制数据点数量
  if (updatedData.length > maxPoints) {
    return updatedData.slice(-maxPoints);
  }
  
  return updatedData;
};

/**
 * 获取连接状态颜色
 * @param connectionState 连接状态
 * @returns 状态颜色
 */
export const getConnectionStatusColor = (connectionState: string): string => {
  switch (connectionState) {
    case 'connected':
      return '#52c41a'; // 绿色
    case 'connecting':
      return '#1890ff'; // 蓝色
    case 'disconnected':
    case 'closed':
      return '#ff4d4f'; // 红色
    case 'closing':
      return '#faad14'; // 橙色
    default:
      return '#d9d9d9'; // 灰色
  }
};

/**
 * 获取连接状态文本
 * @param connectionState 连接状态
 * @returns 状态文本
 */
export const getConnectionStatusText = (connectionState: string): string => {
  switch (connectionState) {
    case 'connected':
      return '已连接';
    case 'connecting':
      return '连接中';
    case 'disconnected':
      return '未连接';
    case 'closed':
      return '已关闭';
    case 'closing':
      return '断开中';
    default:
      return '未知状态';
  }
};

/**
 * 格式化延迟时间
 * @param latency 延迟时间 (ms)
 * @returns 格式化的延迟字符串
 */
export const formatLatency = (latency: number): string => {
  if (latency < 100) {
    return `${latency}ms`;
  } else if (latency < 1000) {
    return `${latency}ms (良好)`;
  } else if (latency < 2000) {
    return `${latency}ms (一般)`;
  } else {
    return `${latency}ms (较慢)`;
  }
};

/**
 * 计算平均值
 * @param values 数值数组
 * @returns 平均值
 */
export const calculateAverage = (values: number[]): number => {
  if (values.length === 0) return 0;
  return values.reduce((sum, value) => sum + value, 0) / values.length;
};

/**
 * 节流函数
 * @param func 要节流的函数
 * @param limit 节流时间间隔 (ms)
 * @returns 节流后的函数
 */
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean;
  return function (this: any, ...args: Parameters<T>) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
};

/**
 * 防抖函数
 * @param func 要防抖的函数
 * @param delay 防抖延迟时间 (ms)
 * @returns 防抖后的函数
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  delay: number
): ((...args: Parameters<T>) => void) => {
  let timeoutId: NodeJS.Timeout;
  return function (this: any, ...args: Parameters<T>) {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func.apply(this, args), delay);
  };
};