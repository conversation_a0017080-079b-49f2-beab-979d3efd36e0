# SearchConfig 多关键词搜索配置组件

一个功能完整的多关键词搜索配置组件，支持智能关键词解析、参数设置、高级筛选和搜索统计等功能。

## 功能特性

### 🔍 多关键词输入
- **智能解析**：支持中文逗号、英文逗号、空格、分号、换行符等多种分隔符
- **自动去重**：自动去除重复关键词和空白关键词
- **标签化显示**：关键词以可编辑标签形式展示
- **批量输入**：支持批量输入和实时预览解析结果

### 📚 关键词管理
- **历史记录**：自动保存使用过的关键词，支持快速添加
- **关键词模板**：预设常用关键词模板（数码、时尚、家居、美妆等）
- **导入导出**：支持TXT、CSV、JSON格式的批量导入导出
- **智能推荐**：基于使用频率推荐热门关键词

### ⚙️ 参数设置
- **目标数量**：设置每个关键词的采集目标数量
- **排序方式**：销量优先、价格排序、评分优先、最新发布等
- **最大页数**：限制搜索深度，控制采集范围
- **无头模式**：提高采集速度的后台运行模式
- **筛选开关**：启用/禁用高级筛选功能

### 🔧 高级筛选
- **价格筛选**：设置价格范围，支持预设和自定义
- **质量筛选**：设置最低评分和最低销量要求
- **排除关键词**：过滤包含特定关键词的商品
- **智能建议**：根据筛选条件提供优化建议

### 📊 搜索统计
- **实时预估**：预估搜索结果数量和耗时
- **效率评分**：基于配置参数的效率评分
- **历史记录**：显示最近的搜索历史
- **配置分析**：提供配置优化建议

### 💾 预设管理
- **保存预设**：将当前配置保存为预设
- **加载预设**：快速加载已保存的配置
- **预设管理**：支持删除和管理多个预设

## 组件结构

```
src/components/SearchConfig/
├── index.tsx           # 主组件入口
├── KeywordInput.tsx    # 关键词输入组件
├── ParameterSettings.tsx # 参数设置组件
├── AdvancedFilters.tsx # 高级筛选组件
├── SearchStats.tsx     # 搜索统计组件
├── types.ts           # 类型定义
├── utils.ts           # 工具函数
└── README.md          # 文档说明
```

## 使用方法

### 基础使用

```tsx
import React from 'react';
import SearchConfig from '@/components/SearchConfig';

const App: React.FC = () => {
  const handleConfigChange = (config) => {
    console.log('配置更新:', config);
  };

  const handleStartSearch = (config) => {
    console.log('开始搜索:', config);
    // 启动爬虫逻辑
  };

  const handleStopSearch = () => {
    console.log('停止搜索');
    // 停止爬虫逻辑
  };

  return (
    <SearchConfig
      onConfigChange={handleConfigChange}
      onStartSearch={handleStartSearch}
      onStopSearch={handleStopSearch}
    />
  );
};
```

### 高级配置

```tsx
import React, { useState } from 'react';
import SearchConfig from '@/components/SearchConfig';
import { SearchConfig as SearchConfigType } from '@/components/SearchConfig/types';

const AdvancedApp: React.FC = () => {
  const [isSearching, setIsSearching] = useState(false);

  // 初始配置
  const initialConfig: Partial<SearchConfigType> = {
    keywords: ['手机', '耳机'],
    targetCount: 100,
    sortMethod: 'sales',
    headless: true,
    enableFilter: true,
    priceRange: [50, 500],
    minRating: 4
  };

  const handleStartSearch = async (config: SearchConfigType) => {
    setIsSearching(true);
    try {
      // 调用API启动搜索
      const response = await fetch('/api/search/start', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(config)
      });
      // 处理响应
    } catch (error) {
      console.error('搜索启动失败:', error);
    } finally {
      setIsSearching(false);
    }
  };

  return (
    <SearchConfig
      initialConfig={initialConfig}
      onStartSearch={handleStartSearch}
      isSearching={isSearching}
    />
  );
};
```

## API 参考

### SearchConfig Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| initialConfig | `Partial<SearchConfigType>` | `{}` | 初始配置 |
| onConfigChange | `(config: SearchConfigType) => void` | - | 配置变更回调 |
| onStartSearch | `(config: SearchConfigType) => void` | - | 开始搜索回调 |
| onStopSearch | `() => void` | - | 停止搜索回调 |
| isSearching | `boolean` | `false` | 是否正在搜索 |
| disabled | `boolean` | `false` | 是否禁用组件 |

### SearchConfigType 类型

```typescript
interface SearchConfig {
  keywords: string[];              // 关键词列表
  targetCount: number;             // 目标数量
  sortMethod: 'sales' | 'price_asc' | 'price_desc' | 'rating' | 'latest'; // 排序方式
  maxPages: number;                // 最大页数
  headless: boolean;               // 无头模式
  enableFilter: boolean;           // 启用筛选
  priceRange?: [number, number];   // 价格范围
  minRating?: number;              // 最低评分
  minSoldCount?: number;           // 最低销量
  excludeKeywords?: string[];      // 排除关键词
}
```

## 工具函数

### parseKeywords
解析关键词字符串，支持多种分隔符。

```typescript
import { parseKeywords } from '@/components/SearchConfig/utils';

const keywords = parseKeywords('手机,耳机;充电器 数据线');
// ['手机', '耳机', '充电器', '数据线']
```

### keywordHistoryManager
关键词历史记录管理。

```typescript
import { keywordHistoryManager } from '@/components/SearchConfig/utils';

// 添加到历史记录
keywordHistoryManager.addToHistory(['手机', '耳机']);

// 获取历史记录
const history = keywordHistoryManager.getHistory();

// 获取热门关键词
const popular = keywordHistoryManager.getPopularKeywords(10);
```

### configPresetManager
配置预设管理。

```typescript
import { configPresetManager } from '@/components/SearchConfig/utils';

// 保存预设
const preset = configPresetManager.savePreset('我的配置', config);

// 获取预设列表
const presets = configPresetManager.getPresets();

// 应用预设
const config = configPresetManager.applyPreset(presetId);
```

## 样式定制

组件基于 Ant Design 构建，支持通过 ConfigProvider 进行主题定制：

```tsx
import { ConfigProvider, theme } from 'antd';

<ConfigProvider
  theme={{
    algorithm: theme.defaultAlgorithm,
    token: {
      colorPrimary: '#1890ff',
      borderRadius: 6,
    },
  }}
>
  <SearchConfig />
</ConfigProvider>
```

## 性能优化

- 使用 `React.memo` 优化组件渲染
- 使用 `useCallback` 优化事件处理函数
- 使用 `useMemo` 优化计算密集型操作
- 支持虚拟滚动处理大量关键词
- 智能防抖处理用户输入

## 兼容性

- React 16.8+
- Ant Design 5.0+
- TypeScript 4.0+
- 支持现代浏览器（Chrome 70+, Firefox 70+, Safari 12+）

## 更新日志

### v1.0.0
- 初始版本发布
- 支持多关键词输入和智能解析
- 完整的参数设置和高级筛选功能
- 搜索统计和预设管理
- 完整的 TypeScript 类型支持