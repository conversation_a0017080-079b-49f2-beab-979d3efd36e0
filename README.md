# 🕷️ 拼多多商品爬虫系统

一个功能完整的拼多多商品数据爬取系统，包含Python后端爬虫和React前端管理界面。

## 🌟 主要特性

- **智能Cookie管理**：支持三种Cookie导入方式（JSON、字符串、文件）
- **多关键词爬取**：智能识别多种分隔符，批量爬取商品
- **实时进度监控**：WebSocket实时推送爬取进度
- **数据导出**：支持Excel和CSV格式导出，保留完整的补贴信息
- **断点续爬**：支持暂停、继续、停止等灵活控制
- **数据预览**：分组展示、搜索过滤、详情查看

## 🚀 快速开始

### 环境要求

- Python 3.8+
- Node.js 16+
- Chrome浏览器
- ChromeDriver（对应Chrome版本）

### 一键启动

```bash
# 同时启动前端和后端
python start_all.py
```

### 分别启动

```bash
# 启动后端API服务
python start_backend.py

# 启动前端开发服务器
python start_frontend.py
```

启动后访问：
- 前端界面：http://localhost:5173
- API文档：http://localhost:8001/docs
- WebSocket：ws://localhost:8001/ws

## 📖 使用指南

### 1. Cookie配置（必须）

首次使用需要配置拼多多Cookie：

1. 登录拼多多网页版
2. 打开开发者工具（F12）
3. 进入Application → Cookies
4. 复制所有Cookie
5. 在系统中导入Cookie（支持三种方式）

### 2. 设置爬取参数

- **关键词**：输入要搜索的商品关键词（支持多个）
  - 支持的分隔符：逗号、分号、竖线、空格、换行
  - 示例：`手机壳,耳机;数据线|充电器 鼠标垫`
  
- **目标数量**：每个关键词爬取的商品数量
- **排序方式**：默认排序、销量、价格等
- **最大页数**：限制爬取页数，避免时间过长

### 3. 开始爬取

点击"开始爬取"后，系统将：
1. 验证Cookie有效性
2. 按关键词依次爬取
3. 实时显示进度
4. 自动保存数据

### 4. 数据管理

- **实时预览**：爬取过程中即可查看数据
- **分组展示**：按关键词分组显示
- **搜索过滤**：支持商品名称搜索
- **详情查看**：点击查看完整商品信息
- **批量导出**：支持Excel和CSV格式

## 🏗️ 项目结构

```
pdd2/
├── backend/                 # 后端代码
│   ├── api_server.py       # FastAPI服务器
│   └── ...
├── frontend/               # 前端代码
│   ├── src/
│   │   ├── components/    # React组件
│   │   ├── services/      # API服务
│   │   ├── hooks/         # 自定义Hooks
│   │   └── types/         # TypeScript类型
│   └── ...
├── src/                    # 爬虫核心代码
│   ├── core/              # 核心模块
│   ├── data/              # 数据处理
│   └── utils/             # 工具函数
├── data/                   # 数据存储
├── logs/                   # 日志文件
├── browser_data/           # 浏览器数据
│   └── cookies/           # Cookie存储
├── config/                 # 配置文件
│   └── settings.yaml      # 系统配置
└── drivers/               # ChromeDriver

```

## ⚙️ 配置说明

配置文件：`config/settings.yaml`

```yaml
browser:
  headless: true          # 无头模式
  window_size: '1920,1080'
  user_agent: 'Mozilla/5.0...'

crawl:
  default_max_pages: 5    # 默认最大页数
  page_load_timeout: 30   # 页面加载超时
  scroll_pause_time: 2    # 滚动暂停时间

api:
  host: '0.0.0.0'
  port: 8001
  debug: true
```

## 🔧 技术栈

### 后端
- **框架**：FastAPI
- **爬虫**：Selenium + ChromeDriver
- **数据处理**：Pandas
- **WebSocket**：原生WebSocket
- **数据导出**：openpyxl、csv

### 前端
- **框架**：React 18 + TypeScript
- **构建工具**：Vite
- **UI组件**：Ant Design 5
- **状态管理**：Zustand
- **WebSocket**：react-use-websocket
- **HTTP客户端**：Axios

## 📊 数据字段说明

爬取的商品数据包含以下字段：

- **基础信息**：商品ID、名称、图片、链接
- **价格信息**：原价、现价、折扣
- **销量信息**：销量、评价数
- **店铺信息**：店铺名称、店铺类型
- **补贴信息**：补贴详情（重要字段）
- **其他信息**：标签、活动、创建时间等

## ⚠️ 注意事项

1. **Cookie有效期**：Cookie可能过期，需要定期更新
2. **爬取频率**：避免频率过高导致被限制
3. **ChromeDriver版本**：需要与Chrome浏览器版本匹配
4. **网络要求**：确保网络稳定，否则可能爬取失败
5. **数据存储**：定期清理data目录，避免占用过多空间

## 🐛 常见问题

### 1. ChromeDriver版本不匹配
下载对应版本：https://chromedriver.chromium.org/

### 2. Cookie无效
重新登录拼多多并导入新的Cookie

### 3. 端口被占用
修改`config/settings.yaml`中的端口配置

### 4. 爬取速度慢
- 减少`scroll_pause_time`
- 使用无头模式`headless: true`
- 适当增加`page_load_timeout`

## 📈 性能指标

- **启动时间**：< 5秒
- **页面加载**：< 3秒
- **爬取速度**：约10-20个商品/分钟
- **内存占用**：< 500MB
- **CPU占用**：< 30%

## 🔐 安全说明

本系统仅供学习研究使用，请遵守相关法律法规：
- 不要用于商业用途
- 控制爬取频率
- 尊重网站robots协议
- 保护用户隐私数据

## 📝 更新日志

### v1.0.0 (2024-01)
- ✅ 实现核心爬虫功能
- ✅ 添加React前端界面
- ✅ 支持WebSocket实时通信
- ✅ 实现数据导出功能
- ✅ 添加Cookie管理
- ✅ 支持多关键词爬取

## 🤝 贡献指南

欢迎提交Issue和Pull Request！

## 📄 许可证

MIT License - 仅供学习研究使用

---

**免责声明**：本项目仅供学习研究使用，使用者需自行承担相关风险和责任。