import React from 'react';
import {
  Form,
  InputNumber,
  Select,
  Switch,
  Card,
  Space,
  Tooltip,
  Typography,
  Row,
  Col,
  Divider
} from 'antd';
import {
  SettingOutlined,
  QuestionCircleOutlined,
  ThunderboltOutlined,
  FilterOutlined
} from '@ant-design/icons';
import type { ParameterSettingsProps, SortOption } from './types';

const { Text, Title } = Typography;
const { Option } = Select;

// 排序选项配置
const sortOptions: SortOption[] = [
  {
    value: 'default',
    label: '综合排序',
    description: '按拼多多默认综合排序，推荐使用'
  },
  {
    value: 'sales',
    label: '销量优先',
    description: '按销量从高到低排序，优先显示热销商品'
  },
  {
    value: 'price_asc',
    label: '价格升序',
    description: '按价格从低到高排序，优先显示低价商品'
  },
  {
    value: 'price_desc',
    label: '价格降序',
    description: '按价格从高到低排序，优先显示高价商品'
  },
  {
    value: 'rating',
    label: '评分优先',
    description: '按商品评分排序，优先显示高评分商品'
  }
];

const ParameterSettings: React.FC<ParameterSettingsProps> = ({
  config,
  onChange,
  disabled = false
}) => {
  const handleChange = (field: string, value: any) => {
    onChange({
      [field]: value
    });
  };

  return (
    <Card
      title={
        <Space>
          <SettingOutlined />
          <span>参数设置</span>
        </Space>
      }
      size="small"
    >
      <Form layout="vertical" size="small">
        <Row gutter={16}>
          {/* 目标数量 */}
          <Col xs={24} sm={12} md={8}>
            <Form.Item
              label={
                <Space>
                  <span>每个关键词目标数量</span>
                  <Tooltip title="每个关键词需要采集的商品数量，系统会根据目标数量自动翻页">
                    <QuestionCircleOutlined style={{ color: '#999' }} />
                  </Tooltip>
                </Space>
              }
            >
              <InputNumber
                value={config.targetCount}
                onChange={(value) => handleChange('targetCount', value || 50)}
                min={10}
                max={1000}
                step={10}
                style={{ width: '100%' }}
                placeholder="50"
                disabled={disabled}
                formatter={(value) => `${value}个/关键词`}
                parser={(value) => Number(value?.replace('个/关键词', '')) || 0}
              />
              <Text type="secondary" style={{ fontSize: '12px' }}>
                建议范围：10-500个
              </Text>
            </Form.Item>
          </Col>

          {/* 排序方式 */}
          <Col xs={24} sm={12} md={8}>
            <Form.Item
              label={
                <Space>
                  <span>排序方式</span>
                  <Tooltip title="选择商品的排序策略，影响采集到的商品类型">
                    <QuestionCircleOutlined style={{ color: '#999' }} />
                  </Tooltip>
                </Space>
              }
            >
              <Select
                value={config.sortMethod}
                onChange={(value) => handleChange('sortMethod', value)}
                style={{ width: '100%' }}
                placeholder="请选择排序方式"
                disabled={disabled}
                optionLabelProp="label"
              >
                {sortOptions.map(option => (
                  <Option key={option.value} value={option.value} label={option.label}>
                    <div>
                      <div style={{ fontWeight: 'bold' }}>{option.label}</div>
                      <div style={{ fontSize: '12px', color: '#999' }}>
                        {option.description}
                      </div>
                    </div>
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Divider style={{ margin: '16px 0' }} />

        {/* 高级选项 */}
        <Row gutter={16}>
          <Col xs={24} sm={12}>
            <Form.Item
              label={
                <Space>
                  <ThunderboltOutlined />
                  <span>无头模式</span>
                  <Tooltip title="启用无头模式可以提高采集速度，但可能影响某些网站的兼容性">
                    <QuestionCircleOutlined style={{ color: '#999' }} />
                  </Tooltip>
                </Space>
              }
            >
              <div>
                <Switch
                  checked={config.headless}
                  onChange={(checked) => handleChange('headless', checked)}
                  disabled={disabled}
                  checkedChildren="启用"
                  unCheckedChildren="禁用"
                />
                <div style={{ marginTop: 4 }}>
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    {config.headless ? '后台运行，速度更快' : '显示浏览器，便于调试'}
                  </Text>
                </div>
              </div>
            </Form.Item>
          </Col>

          <Col xs={24} sm={12}>
            <Form.Item
              label={
                <Space>
                  <FilterOutlined />
                  <span>商品筛选</span>
                  <Tooltip title="启用后可以根据价格、评分等条件筛选商品">
                    <QuestionCircleOutlined style={{ color: '#999' }} />
                  </Tooltip>
                </Space>
              }
            >
              <div>
                <Switch
                  checked={config.enableFilter}
                  onChange={(checked) => handleChange('enableFilter', checked)}
                  disabled={disabled}
                  checkedChildren="启用"
                  unCheckedChildren="禁用"
                />
                <div style={{ marginTop: 4 }}>
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    {config.enableFilter ? '可设置筛选条件' : '采集所有商品'}
                  </Text>
                </div>
              </div>
            </Form.Item>
          </Col>
        </Row>

        {/* 配置说明 */}
        <div style={{ marginTop: 16, padding: 12, backgroundColor: '#f6f8fa', borderRadius: 6 }}>
          <Title level={5} style={{ margin: 0, marginBottom: 8, color: '#666' }}>
            配置说明
          </Title>
          <div style={{ fontSize: '12px', color: '#666', lineHeight: 1.5 }}>
            <div>• <strong>目标数量</strong>：每个关键词采集的商品数量，影响总采集时间</div>
            <div>• <strong>最大页数</strong>：限制搜索深度，避免采集到过于冷门的商品</div>
            <div>• <strong>排序方式</strong>：影响采集到商品的质量和类型</div>
            <div>• <strong>无头模式</strong>：提高速度但可能影响某些网站的访问</div>
            <div>• <strong>商品筛选</strong>：启用后可在高级筛选中设置具体条件</div>
          </div>
        </div>

        {/* 性能建议 */}
        {(config.targetCount > 500 || config.maxPages > 20) && (
          <div style={{ 
            marginTop: 12, 
            padding: 12, 
            backgroundColor: '#fff2e8', 
            borderRadius: 6,
            border: '1px solid #ffd591'
          }}>
            <div style={{ fontSize: '12px', color: '#d4621b' }}>
              <strong>⚠️ 性能提醒：</strong>
              当前配置可能导致采集时间较长，建议：
              {config.targetCount > 500 && <div>• 目标数量控制在500以内</div>}
              {config.maxPages > 20 && <div>• 最大页数控制在20以内</div>}
            </div>
          </div>
        )}
      </Form>
    </Card>
  );
};

export default ParameterSettings;