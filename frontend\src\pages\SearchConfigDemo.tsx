import React, { useState } from 'react';
import { Layout, Typography, Card, message, Divider } from 'antd';
import SearchConfig from '../components/SearchConfig';
import type { SearchConfig as SearchConfigType } from '../components/SearchConfig/types';

const { Content } = Layout;
const { Title, Paragraph } = Typography;

const SearchConfigDemo: React.FC = () => {
  const [currentConfig, setCurrentConfig] = useState<SearchConfigType | null>(null);
  const [isSearching, setIsSearching] = useState(false);

  const handleConfigChange = (config: SearchConfigType) => {
    setCurrentConfig(config);
    console.log('配置更新:', config);
  };

  const handleStartSearch = (config: SearchConfigType) => {
    console.log('开始搜索，配置:', config);
    message.success(`开始搜索 ${config.keywords.length} 个关键词`);
    setIsSearching(true);
    
    // 模拟搜索过程
    setTimeout(() => {
      setIsSearching(false);
      message.success('搜索完成');
    }, 5000);
  };

  const handleStopSearch = () => {
    console.log('停止搜索');
    setIsSearching(false);
    message.info('搜索已停止');
  };

  return (
    <Layout style={{ minHeight: '100vh', background: '#f0f2f5' }}>
      <Content style={{ padding: '24px', maxWidth: 1400, margin: '0 auto', width: '100%' }}>
        <div style={{ marginBottom: 24 }}>
          <Title level={2}>搜索配置组件演示</Title>
          <Paragraph>
            这是一个完整的多关键词搜索配置组件，支持智能关键词解析、参数设置、高级筛选和搜索统计等功能。
          </Paragraph>
        </div>

        {/* 主要组件 */}
        <SearchConfig
          onConfigChange={handleConfigChange}
          onStartSearch={handleStartSearch}
          onStopSearch={handleStopSearch}
          isSearching={isSearching}
        />

        <Divider />

        {/* 配置预览 */}
        {currentConfig && (
          <Card title="当前配置预览" style={{ marginTop: 24 }}>
            <pre style={{ 
              background: '#f6f8fa', 
              padding: 16, 
              borderRadius: 6, 
              overflow: 'auto',
              fontSize: '12px'
            }}>
              {JSON.stringify(currentConfig, null, 2)}
            </pre>
          </Card>
        )}

        {/* 使用说明 */}
        <Card title="功能说明" style={{ marginTop: 24 }}>
          <div style={{ fontSize: '14px', lineHeight: 1.6 }}>
            <Title level={4}>主要功能</Title>
            <ul>
              <li><strong>多关键词输入</strong>：支持中文逗号、英文逗号、空格、分号、换行符等多种分隔符</li>
              <li><strong>智能解析</strong>：自动去重、去空格、格式化关键词</li>
              <li><strong>历史记录</strong>：自动保存使用过的关键词，支持快速添加</li>
              <li><strong>关键词模板</strong>：预设常用关键词模板，快速批量添加</li>
              <li><strong>批量导入导出</strong>：支持TXT、CSV、JSON格式的批量导入导出</li>
              <li><strong>参数设置</strong>：目标数量、排序方式、最大页数、无头模式等</li>
              <li><strong>高级筛选</strong>：价格范围、最低评分、最低销量、排除关键词</li>
              <li><strong>搜索统计</strong>：实时预估结果数量、耗时、效率评分</li>
              <li><strong>预设管理</strong>：保存和加载常用配置预设</li>
            </ul>

            <Title level={4}>使用方法</Title>
            <ol>
              <li>在关键词输入框中输入关键词，支持多种分隔符</li>
              <li>或使用批量输入、历史记录、模板等快速添加关键词</li>
              <li>在参数设置中配置目标数量、排序方式等基础参数</li>
              <li>启用高级筛选后可设置价格、评分等筛选条件</li>
              <li>查看右侧统计信息，确认配置效率</li>
              <li>可以保存当前配置为预设，方便下次使用</li>
              <li>点击"开始搜索"启动爬虫任务</li>
            </ol>

            <Title level={4}>技术特点</Title>
            <ul>
              <li><strong>TypeScript</strong>：完整的类型定义，确保类型安全</li>
              <li><strong>React Hooks</strong>：使用现代React Hooks进行状态管理</li>
              <li><strong>Ant Design</strong>：基于Ant Design 5.x构建，界面美观</li>
              <li><strong>本地存储</strong>：历史记录和预设使用localStorage持久化</li>
              <li><strong>性能优化</strong>：使用useCallback等优化性能</li>
              <li><strong>响应式设计</strong>：支持移动端和桌面端</li>
              <li><strong>无障碍访问</strong>：遵循WCAG规范，支持键盘导航</li>
            </ul>
          </div>
        </Card>
      </Content>
    </Layout>
  );
};

export default SearchConfigDemo;