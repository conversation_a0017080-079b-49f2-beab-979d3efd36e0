"""
资源清理管理器
提供全面的系统资源清理功能，包括临时文件、日志文件、浏览器数据、缓存等

版本: v2.0.0
更新时间: 2025-01-31
支持功能: 定时清理、优雅关闭、策略配置、清理统计
"""

import asyncio
import os
import shutil
import datetime
import threading
from pathlib import Path
from typing import Dict, List, Any, Optional, Callable
import psutil
import glob
import json
import sqlite3

from loguru import logger


class ResourceCleaner:
    """
    全面的资源清理管理器
    
    支持功能:
    - 临时文件清理（截图、下载、缓存等）
    - 日志文件管理（按时间/大小清理）
    - 浏览器数据清理（缓存、临时profile）
    - 内存数据清理（已导出的数据缓存）
    - 定时清理任务
    - 优雅关闭机制
    """
    
    def __init__(self, config_path: str = "config/settings.yaml"):
        """初始化资源清理器"""
        self.config = self._load_config(config_path)
        self.cleanup_config = self.config.get("resource_cleanup", {})
        
        # 基础配置
        self.enabled = self.cleanup_config.get("enabled", True)
        self.auto_cleanup_interval = self.cleanup_config.get("auto_cleanup_interval", 3600)  # 1小时
        self.cleanup_on_shutdown = self.cleanup_config.get("cleanup_on_shutdown", True)
        
        # 保留策略配置
        self.retention_policies = self.cleanup_config.get("retention_policies", {
            "logs": {"days": 7, "max_size_mb": 100},
            "browser_cache": {"days": 3, "max_size_mb": 500},
            "temp_files": {"days": 1, "max_size_mb": 50},
            "export_files": {"days": 30, "max_count": 100},
            "screenshots": {"days": 7, "max_count": 50}
        })
        
        # 清理路径配置
        self.cleanup_paths = self.cleanup_config.get("cleanup_paths", {
            "logs_dir": "./logs",
            "browser_data_dir": "./browser_data",
            "temp_dir": "./temp",
            "output_dir": "./output",
            "cache_dir": "./cache"
        })
        
        # 运行状态
        self.is_running = False
        self.cleanup_thread = None
        self.shutdown_callbacks = []
        self.cleanup_stats = {
            "total_cleanups": 0,
            "total_freed_mb": 0,
            "last_cleanup_time": None,
            "errors": []
        }
        
        # 清理计数器
        self._cleanup_counters = {
            "logs": 0,
            "browser_cache": 0,
            "temp_files": 0,
            "export_files": 0,
            "screenshots": 0,
            "memory_data": 0
        }
        
        logger.info(f"🧹 资源清理器初始化完成，自动清理间隔: {self.auto_cleanup_interval}秒")
        if not self.enabled:
            logger.warning("⚠️ 资源清理功能已禁用")
    
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            import yaml
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f) or {}
        except Exception as e:
            logger.warning(f"加载配置文件失败: {e}，使用默认配置")
            return {}
    
    def start_auto_cleanup(self) -> None:
        """启动自动清理服务"""
        if not self.enabled:
            logger.info("资源清理功能已禁用，跳过自动清理")
            return
        
        if self.is_running:
            logger.warning("自动清理已在运行中")
            return
        
        self.is_running = True
        self.cleanup_thread = threading.Thread(target=self._cleanup_loop, daemon=True)
        self.cleanup_thread.start()
        logger.info("🔄 自动清理服务已启动")
    
    def stop_auto_cleanup(self) -> None:
        """停止自动清理服务"""
        if not self.is_running:
            return
        
        logger.info("正在停止自动清理服务...")
        self.is_running = False
        
        if self.cleanup_thread and self.cleanup_thread.is_alive():
            self.cleanup_thread.join(timeout=5)
        
        logger.info("✅ 自动清理服务已停止")
    
    def _cleanup_loop(self) -> None:
        """清理循环"""
        while self.is_running:
            try:
                # 执行清理
                self.perform_cleanup()
                
                # 等待下次清理
                for _ in range(self.auto_cleanup_interval):
                    if not self.is_running:
                        break
                    threading.Event().wait(1)
                        
            except Exception as e:
                logger.error(f"自动清理循环出错: {e}")
                self.cleanup_stats["errors"].append({
                    "time": datetime.datetime.now().isoformat(),
                    "error": str(e)
                })
    
    def perform_cleanup(self, cleanup_types: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        执行清理操作
        
        Args:
            cleanup_types: 指定清理类型，None表示全部清理
            
        Returns:
            Dict[str, Any]: 清理结果统计
        """
        if not self.enabled:
            logger.info("资源清理功能已禁用")
            return {"success": False, "message": "清理功能已禁用"}
        
        logger.info("🧹 开始执行资源清理...")
        start_time = datetime.datetime.now()
        total_freed_mb = 0
        cleanup_results = {}
        
        # 默认清理所有类型
        if cleanup_types is None:
            cleanup_types = ["logs", "browser_cache", "temp_files", "export_files", "screenshots", "memory_data"]
        
        try:
            # 1. 清理日志文件
            if "logs" in cleanup_types:
                result = self._cleanup_logs()
                cleanup_results["logs"] = result
                total_freed_mb += result.get("freed_mb", 0)
            
            # 2. 清理浏览器缓存
            if "browser_cache" in cleanup_types:
                result = self._cleanup_browser_cache()
                cleanup_results["browser_cache"] = result
                total_freed_mb += result.get("freed_mb", 0)
            
            # 3. 清理临时文件
            if "temp_files" in cleanup_types:
                result = self._cleanup_temp_files()
                cleanup_results["temp_files"] = result
                total_freed_mb += result.get("freed_mb", 0)
            
            # 4. 清理导出文件
            if "export_files" in cleanup_types:
                result = self._cleanup_export_files()
                cleanup_results["export_files"] = result
                total_freed_mb += result.get("freed_mb", 0)
            
            # 5. 清理截图文件
            if "screenshots" in cleanup_types:
                result = self._cleanup_screenshots()
                cleanup_results["screenshots"] = result
                total_freed_mb += result.get("freed_mb", 0)
            
            # 6. 清理内存数据
            if "memory_data" in cleanup_types:
                result = self._cleanup_memory_data()
                cleanup_results["memory_data"] = result
                total_freed_mb += result.get("freed_mb", 0)
            
            # 更新统计
            self.cleanup_stats["total_cleanups"] += 1
            self.cleanup_stats["total_freed_mb"] += total_freed_mb
            self.cleanup_stats["last_cleanup_time"] = start_time.isoformat()
            
            duration = (datetime.datetime.now() - start_time).total_seconds()
            
            logger.success(f"✅ 清理完成，耗时: {duration:.2f}秒，释放空间: {total_freed_mb:.2f}MB")
            
            return {
                "success": True,
                "duration": duration,
                "total_freed_mb": total_freed_mb,
                "cleanup_results": cleanup_results,
                "timestamp": start_time.isoformat()
            }
            
        except Exception as e:
            logger.error(f"❌ 清理操作失败: {e}")
            self.cleanup_stats["errors"].append({
                "time": start_time.isoformat(),
                "error": str(e)
            })
            return {
                "success": False,
                "error": str(e),
                "duration": (datetime.datetime.now() - start_time).total_seconds()
            }
    
    def _cleanup_logs(self) -> Dict[str, Any]:
        """清理日志文件"""
        try:
            logs_dir = Path(self.cleanup_paths.get("logs_dir", "./logs"))
            if not logs_dir.exists():
                return {"success": True, "freed_mb": 0, "files_removed": 0}
            
            policy = self.retention_policies.get("logs", {})
            max_days = policy.get("days", 7)
            max_size_mb = policy.get("max_size_mb", 100)
            
            cutoff_date = datetime.datetime.now() - datetime.timedelta(days=max_days)
            files_removed = 0
            freed_bytes = 0
            
            # 按时间清理
            for log_file in logs_dir.glob("*.log"):
                try:
                    stat = log_file.stat()
                    file_time = datetime.datetime.fromtimestamp(stat.st_mtime)
                    
                    # 检查文件时间
                    if file_time < cutoff_date:
                        freed_bytes += stat.st_size
                        log_file.unlink()
                        files_removed += 1
                        logger.debug(f"删除过期日志: {log_file.name}")
                    
                    # 检查文件大小（单个文件过大）
                    elif stat.st_size > max_size_mb * 1024 * 1024:
                        # 压缩大文件而不是删除
                        compressed_file = self._compress_log_file(log_file)
                        if compressed_file:
                            original_size = stat.st_size
                            new_size = compressed_file.stat().st_size
                            freed_bytes += (original_size - new_size)
                            logger.debug(f"压缩日志文件: {log_file.name} -> {compressed_file.name}")
                        
                except Exception as e:
                    logger.debug(f"处理日志文件 {log_file} 失败: {e}")
            
            # 按总大小清理（如果总大小仍然超标）
            total_size = sum(f.stat().st_size for f in logs_dir.glob("*.log"))
            if total_size > max_size_mb * 1024 * 1024:
                # 删除最旧的文件直到满足大小限制
                log_files = sorted(logs_dir.glob("*.log"), key=lambda x: x.stat().st_mtime)
                for log_file in log_files:
                    if total_size <= max_size_mb * 1024 * 1024:
                        break
                    stat = log_file.stat()
                    freed_bytes += stat.st_size
                    total_size -= stat.st_size
                    log_file.unlink()
                    files_removed += 1
                    logger.debug(f"删除日志文件以控制总大小: {log_file.name}")
            
            freed_mb = freed_bytes / 1024 / 1024
            self._cleanup_counters["logs"] += files_removed
            
            if files_removed > 0:
                logger.info(f"📝 清理日志文件: {files_removed} 个文件，释放 {freed_mb:.2f}MB")
            
            return {
                "success": True,
                "files_removed": files_removed,
                "freed_mb": freed_mb
            }
            
        except Exception as e:
            logger.error(f"清理日志文件失败: {e}")
            return {"success": False, "error": str(e)}
    
    def _compress_log_file(self, log_file: Path) -> Optional[Path]:
        """压缩日志文件"""
        try:
            import gzip
            compressed_file = log_file.with_suffix(log_file.suffix + '.gz')
            
            with open(log_file, 'rb') as f_in:
                with gzip.open(compressed_file, 'wb') as f_out:
                    shutil.copyfileobj(f_in, f_out)
            
            log_file.unlink()  # 删除原文件
            return compressed_file
            
        except Exception as e:
            logger.debug(f"压缩日志文件失败: {e}")
            return None
    
    def _cleanup_browser_cache(self) -> Dict[str, Any]:
        """清理浏览器缓存和临时数据"""
        try:
            browser_dir = Path(self.cleanup_paths.get("browser_data_dir", "./browser_data"))
            if not browser_dir.exists():
                return {"success": True, "freed_mb": 0, "files_removed": 0}
            
            policy = self.retention_policies.get("browser_cache", {})
            max_days = policy.get("days", 3)
            max_size_mb = policy.get("max_size_mb", 500)
            
            cutoff_date = datetime.datetime.now() - datetime.timedelta(days=max_days)
            files_removed = 0
            freed_bytes = 0
            
            # 清理的目录和文件模式
            cleanup_patterns = [
                "**/Cache/**",
                "**/GPUCache/**", 
                "**/ShaderCache/**",
                "**/CrashpadMetrics*",
                "**/BrowserMetrics*",
                "**/Network/**/*.tmp",
                "**/Code Cache/**",
                "**/Service Worker/**",
                "**/storage/default/**/*.tmp"
            ]
            
            for pattern in cleanup_patterns:
                for item in browser_dir.glob(pattern):
                    try:
                        if item.is_file():
                            stat = item.stat()
                            file_time = datetime.datetime.fromtimestamp(stat.st_mtime)
                            
                            if file_time < cutoff_date:
                                freed_bytes += stat.st_size
                                item.unlink()
                                files_removed += 1
                                
                        elif item.is_dir() and not any(item.iterdir()):
                            # 删除空目录
                            item.rmdir()
                            
                    except Exception as e:
                        logger.debug(f"清理浏览器文件 {item} 失败: {e}")
            
            # 清理数据库文件的WAL和journal文件
            for db_file in browser_dir.glob("**/*.db"):
                try:
                    wal_file = db_file.with_suffix('.db-wal')
                    journal_file = db_file.with_suffix('.db-journal')
                    
                    for temp_file in [wal_file, journal_file]:
                        if temp_file.exists():
                            stat = temp_file.stat()
                            if datetime.datetime.fromtimestamp(stat.st_mtime) < cutoff_date:
                                freed_bytes += stat.st_size
                                temp_file.unlink()
                                files_removed += 1
                                
                except Exception as e:
                    logger.debug(f"清理数据库临时文件失败: {e}")
            
            freed_mb = freed_bytes / 1024 / 1024
            self._cleanup_counters["browser_cache"] += files_removed
            
            if files_removed > 0:
                logger.info(f"🌐 清理浏览器缓存: {files_removed} 个文件，释放 {freed_mb:.2f}MB")
            
            return {
                "success": True,
                "files_removed": files_removed,
                "freed_mb": freed_mb
            }
            
        except Exception as e:
            logger.error(f"清理浏览器缓存失败: {e}")
            return {"success": False, "error": str(e)}
    
    def _cleanup_temp_files(self) -> Dict[str, Any]:
        """清理临时文件"""
        try:
            files_removed = 0
            freed_bytes = 0
            
            policy = self.retention_policies.get("temp_files", {})
            max_days = policy.get("days", 1)
            cutoff_date = datetime.datetime.now() - datetime.timedelta(days=max_days)
            
            # 清理各种临时文件
            temp_patterns = [
                "*.tmp",
                "*.temp", 
                "*.lock",
                "*~",
                ".DS_Store",
                "Thumbs.db"
            ]
            
            # 在项目根目录和temp目录中清理
            search_dirs = [
                Path("."),
                Path(self.cleanup_paths.get("temp_dir", "./temp"))
            ]
            
            for search_dir in search_dirs:
                if not search_dir.exists():
                    continue
                    
                for pattern in temp_patterns:
                    for temp_file in search_dir.glob(pattern):
                        try:
                            if temp_file.is_file():
                                stat = temp_file.stat()
                                file_time = datetime.datetime.fromtimestamp(stat.st_mtime)
                                
                                if file_time < cutoff_date:
                                    freed_bytes += stat.st_size
                                    temp_file.unlink()
                                    files_removed += 1
                                    logger.debug(f"删除临时文件: {temp_file}")
                                    
                        except Exception as e:
                            logger.debug(f"删除临时文件 {temp_file} 失败: {e}")
            
            freed_mb = freed_bytes / 1024 / 1024
            self._cleanup_counters["temp_files"] += files_removed
            
            if files_removed > 0:
                logger.info(f"🗂️ 清理临时文件: {files_removed} 个文件，释放 {freed_mb:.2f}MB")
            
            return {
                "success": True,
                "files_removed": files_removed,
                "freed_mb": freed_mb
            }
            
        except Exception as e:
            logger.error(f"清理临时文件失败: {e}")
            return {"success": False, "error": str(e)}
    
    def _cleanup_export_files(self) -> Dict[str, Any]:
        """清理导出文件"""
        try:
            output_dir = Path(self.cleanup_paths.get("output_dir", "./output"))
            if not output_dir.exists():
                return {"success": True, "freed_mb": 0, "files_removed": 0}
            
            policy = self.retention_policies.get("export_files", {})
            max_days = policy.get("days", 30)
            max_count = policy.get("max_count", 100)
            
            cutoff_date = datetime.datetime.now() - datetime.timedelta(days=max_days)
            files_removed = 0
            freed_bytes = 0
            
            # 获取所有导出文件
            export_files = list(output_dir.glob("*.xlsx")) + list(output_dir.glob("*.csv"))
            
            # 按时间清理
            for export_file in export_files:
                try:
                    stat = export_file.stat()
                    file_time = datetime.datetime.fromtimestamp(stat.st_mtime)
                    
                    if file_time < cutoff_date:
                        freed_bytes += stat.st_size
                        export_file.unlink()
                        files_removed += 1
                        logger.debug(f"删除过期导出文件: {export_file.name}")
                        
                except Exception as e:
                    logger.debug(f"处理导出文件 {export_file} 失败: {e}")
            
            # 按数量清理（保留最新的文件）
            remaining_files = [f for f in export_files if f.exists()]
            if len(remaining_files) > max_count:
                # 按修改时间排序，删除最旧的文件
                remaining_files.sort(key=lambda x: x.stat().st_mtime)
                files_to_remove = remaining_files[:-max_count]
                
                for export_file in files_to_remove:
                    try:
                        stat = export_file.stat()
                        freed_bytes += stat.st_size
                        export_file.unlink()
                        files_removed += 1
                        logger.debug(f"删除导出文件以控制数量: {export_file.name}")
                        
                    except Exception as e:
                        logger.debug(f"删除导出文件 {export_file} 失败: {e}")
            
            # 清理临时Excel文件（~$开头的文件）
            temp_excel_files = list(output_dir.glob("~$*.xlsx"))
            for temp_file in temp_excel_files:
                try:
                    stat = temp_file.stat()
                    freed_bytes += stat.st_size
                    temp_file.unlink()
                    files_removed += 1
                    logger.debug(f"删除临时Excel文件: {temp_file.name}")
                    
                except Exception as e:
                    logger.debug(f"删除临时Excel文件 {temp_file} 失败: {e}")
            
            freed_mb = freed_bytes / 1024 / 1024
            self._cleanup_counters["export_files"] += files_removed
            
            if files_removed > 0:
                logger.info(f"📊 清理导出文件: {files_removed} 个文件，释放 {freed_mb:.2f}MB")
            
            return {
                "success": True,
                "files_removed": files_removed,
                "freed_mb": freed_mb
            }
            
        except Exception as e:
            logger.error(f"清理导出文件失败: {e}")
            return {"success": False, "error": str(e)}
    
    def _cleanup_screenshots(self) -> Dict[str, Any]:
        """清理截图文件"""
        try:
            files_removed = 0
            freed_bytes = 0
            
            policy = self.retention_policies.get("screenshots", {})
            max_days = policy.get("days", 7)
            max_count = policy.get("max_count", 50)
            
            cutoff_date = datetime.datetime.now() - datetime.timedelta(days=max_days)
            
            # 在项目根目录查找截图文件
            screenshot_patterns = ["*.png", "*.jpg", "*.jpeg", "*.gif"]
            screenshot_files = []
            
            for pattern in screenshot_patterns:
                screenshot_files.extend(Path(".").glob(pattern))
            
            # 过滤掉可能的logo或资源文件（通常在特定目录中）
            screenshot_files = [f for f in screenshot_files 
                             if not any(part in f.parts for part in ['assets', 'static', 'resources', 'icons'])]
            
            # 按时间清理
            for screenshot_file in screenshot_files:
                try:
                    stat = screenshot_file.stat()
                    file_time = datetime.datetime.fromtimestamp(stat.st_mtime)
                    
                    if file_time < cutoff_date:
                        freed_bytes += stat.st_size
                        screenshot_file.unlink()
                        files_removed += 1
                        logger.debug(f"删除过期截图: {screenshot_file.name}")
                        
                except Exception as e:
                    logger.debug(f"处理截图文件 {screenshot_file} 失败: {e}")
            
            # 按数量清理
            remaining_files = [f for f in screenshot_files if f.exists()]
            if len(remaining_files) > max_count:
                remaining_files.sort(key=lambda x: x.stat().st_mtime)
                files_to_remove = remaining_files[:-max_count]
                
                for screenshot_file in files_to_remove:
                    try:
                        stat = screenshot_file.stat()
                        freed_bytes += stat.st_size
                        screenshot_file.unlink()
                        files_removed += 1
                        logger.debug(f"删除截图文件以控制数量: {screenshot_file.name}")
                        
                    except Exception as e:
                        logger.debug(f"删除截图文件 {screenshot_file} 失败: {e}")
            
            freed_mb = freed_bytes / 1024 / 1024
            self._cleanup_counters["screenshots"] += files_removed
            
            if files_removed > 0:
                logger.info(f"📸 清理截图文件: {files_removed} 个文件，释放 {freed_mb:.2f}MB")
            
            return {
                "success": True,
                "files_removed": files_removed,
                "freed_mb": freed_mb
            }
            
        except Exception as e:
            logger.error(f"清理截图文件失败: {e}")
            return {"success": False, "error": str(e)}
    
    def _cleanup_memory_data(self) -> Dict[str, Any]:
        """清理内存数据和缓存"""
        try:
            import gc
            import sys
            
            # 记录清理前的内存使用
            process = psutil.Process()
            memory_before = process.memory_info().rss / 1024 / 1024  # MB
            
            # 强制垃圾回收
            collected_objects = gc.collect()
            
            # 清理Python模块缓存
            modules_to_clear = []
            for module_name in list(sys.modules.keys()):
                # 清理一些可能缓存大量数据的模块
                if any(pattern in module_name.lower() for pattern in 
                      ['cache', 'temp', 'buffer', 'queue']):
                    modules_to_clear.append(module_name)
            
            # 清理缓存目录
            cache_dir = Path(self.cleanup_paths.get("cache_dir", "./cache"))
            cache_files_removed = 0
            cache_freed_bytes = 0
            
            if cache_dir.exists():
                for cache_file in cache_dir.rglob("*"):
                    if cache_file.is_file():
                        try:
                            stat = cache_file.stat()
                            cache_freed_bytes += stat.st_size
                            cache_file.unlink()
                            cache_files_removed += 1
                        except Exception as e:
                            logger.debug(f"删除缓存文件失败: {e}")
            
            # 记录清理后的内存使用
            memory_after = process.memory_info().rss / 1024 / 1024  # MB
            memory_freed = max(0, memory_before - memory_after)
            
            freed_mb = memory_freed + (cache_freed_bytes / 1024 / 1024)
            self._cleanup_counters["memory_data"] += collected_objects + cache_files_removed
            
            if collected_objects > 0 or cache_files_removed > 0:
                logger.info(f"💾 清理内存数据: 垃圾回收 {collected_objects} 个对象，"
                          f"删除 {cache_files_removed} 个缓存文件，释放 {freed_mb:.2f}MB")
            
            return {
                "success": True,
                "objects_collected": collected_objects,
                "cache_files_removed": cache_files_removed,
                "freed_mb": freed_mb,
                "memory_before_mb": memory_before,
                "memory_after_mb": memory_after
            }
            
        except Exception as e:
            logger.error(f"清理内存数据失败: {e}")
            return {"success": False, "error": str(e)}
    
    def register_shutdown_callback(self, callback: Callable) -> None:
        """注册关闭时的清理回调"""
        self.shutdown_callbacks.append(callback)
        logger.debug(f"注册关闭回调: {callback.__name__}")
    
    def graceful_shutdown(self) -> Dict[str, Any]:
        """优雅关闭，执行清理工作"""
        logger.info("🛑 开始优雅关闭，执行清理工作...")
        
        results = {
            "cleanup_performed": False,
            "callbacks_executed": 0,
            "total_freed_mb": 0
        }
        
        try:
            # 执行注册的回调
            for callback in self.shutdown_callbacks:
                try:
                    callback()
                    results["callbacks_executed"] += 1
                    logger.debug(f"执行关闭回调: {callback.__name__}")
                except Exception as e:
                    logger.error(f"执行关闭回调失败: {e}")
            
            # 停止自动清理
            self.stop_auto_cleanup()
            
            # 执行最终清理（如果配置启用）
            if self.cleanup_on_shutdown:
                cleanup_result = self.perform_cleanup()
                results["cleanup_performed"] = cleanup_result.get("success", False)
                results["total_freed_mb"] = cleanup_result.get("total_freed_mb", 0)
            
            # 保存清理统计
            self._save_cleanup_stats()
            
            logger.success(f"✅ 优雅关闭完成，执行了 {results['callbacks_executed']} 个回调，"
                          f"释放空间 {results['total_freed_mb']:.2f}MB")
            
            return results
            
        except Exception as e:
            logger.error(f"优雅关闭失败: {e}")
            return {"success": False, "error": str(e)}
    
    def get_cleanup_stats(self) -> Dict[str, Any]:
        """获取清理统计信息"""
        # 计算磁盘使用情况
        disk_usage = {}
        for name, path in self.cleanup_paths.items():
            try:
                path_obj = Path(path)
                if path_obj.exists():
                    if path_obj.is_dir():
                        size = sum(f.stat().st_size for f in path_obj.rglob('*') if f.is_file())
                    else:
                        size = path_obj.stat().st_size
                    disk_usage[name] = {
                        "size_mb": size / 1024 / 1024,
                        "path": str(path_obj.absolute())
                    }
            except Exception as e:
                logger.debug(f"计算磁盘使用失败 {name}: {e}")
        
        return {
            "cleanup_stats": self.cleanup_stats.copy(),
            "cleanup_counters": self._cleanup_counters.copy(),
            "disk_usage": disk_usage,
            "retention_policies": self.retention_policies.copy(),
            "is_auto_cleanup_running": self.is_running,
            "system_memory_mb": psutil.virtual_memory().used / 1024 / 1024
        }
    
    def _save_cleanup_stats(self) -> None:
        """保存清理统计到文件"""
        try:
            stats_file = Path("logs/cleanup_stats.json")
            stats_file.parent.mkdir(exist_ok=True)
            
            stats = self.get_cleanup_stats()
            with open(stats_file, 'w', encoding='utf-8') as f:
                json.dump(stats, f, indent=2, ensure_ascii=False, default=str)
            
            logger.debug(f"清理统计已保存: {stats_file}")
            
        except Exception as e:
            logger.debug(f"保存清理统计失败: {e}")
    
    def emergency_cleanup(self) -> Dict[str, Any]:
        """紧急清理（在系统资源紧张时使用）"""
        logger.warning("🚨 执行紧急清理...")
        
        # 执行更激进的清理策略
        emergency_types = ["temp_files", "browser_cache", "memory_data"]
        result = self.perform_cleanup(cleanup_types=emergency_types)
        
        # 额外的紧急措施
        try:
            import gc
            gc.collect()
            gc.collect()  # 执行两次确保彻底清理
            
            # 清理更多浏览器数据
            browser_dir = Path(self.cleanup_paths.get("browser_data_dir", "./browser_data"))
            if browser_dir.exists():
                # 删除所有缓存目录
                cache_dirs = ["Cache", "GPUCache", "ShaderCache", "Code Cache"]
                for cache_dir_name in cache_dirs:
                    for cache_dir in browser_dir.glob(f"**/{cache_dir_name}"):
                        if cache_dir.is_dir():
                            try:
                                shutil.rmtree(cache_dir)
                                logger.debug(f"紧急删除缓存目录: {cache_dir}")
                            except Exception as e:
                                logger.debug(f"删除缓存目录失败: {e}")
            
            logger.warning("🚨 紧急清理完成")
            
        except Exception as e:
            logger.error(f"紧急清理失败: {e}")
        
        return result


# 全局资源清理器实例
_global_cleaner: Optional[ResourceCleaner] = None


def create_resource_cleaner(config_path: str = "config/settings.yaml") -> ResourceCleaner:
    """创建资源清理器实例"""
    global _global_cleaner
    if _global_cleaner is None:
        _global_cleaner = ResourceCleaner(config_path)
    return _global_cleaner


def get_resource_cleaner() -> Optional[ResourceCleaner]:
    """获取全局资源清理器实例"""
    return _global_cleaner


# 便捷函数
def quick_cleanup(cleanup_types: Optional[List[str]] = None) -> Dict[str, Any]:
    """快速清理"""
    cleaner = get_resource_cleaner()
    if cleaner:
        return cleaner.perform_cleanup(cleanup_types)
    return {"success": False, "message": "资源清理器未初始化"}


def emergency_cleanup() -> Dict[str, Any]:
    """紧急清理"""
    cleaner = get_resource_cleaner()
    if cleaner:
        return cleaner.emergency_cleanup()
    return {"success": False, "message": "资源清理器未初始化"}