{"timestamp": "2025-08-01T15:37:02.025066", "monitoring_duration_hours": 0.017116086814138624, "total_alerts": 8, "total_gc_collections": 0, "statistics": {"crawler": {"component": "crawler", "total_snapshots": 7, "avg_memory_mb": 117.34285714285714, "max_memory_mb": 127.4, "min_memory_mb": 100.1, "memory_growth_rate": 11.492827534675598, "leak_probability": 0.0, "garbage_collections_triggered": 0, "alerts_count": 0}, "browser_manager": {"component": "browser_manager", "total_snapshots": 4, "avg_memory_mb": 116.175, "max_memory_mb": 125.3, "min_memory_mb": 100.9, "memory_growth_rate": 21.44011616706848, "leak_probability": 0.0, "garbage_collections_triggered": 0, "alerts_count": 0}, "data_processor": {"component": "data_processor", "total_snapshots": 4, "avg_memory_mb": 118.625, "max_memory_mb": 126.1, "min_memory_mb": 100.9, "memory_growth_rate": 0.0, "leak_probability": 0.0, "garbage_collections_triggered": 0, "alerts_count": 0}, "api_monitor": {"component": "api_monitor", "total_snapshots": 4, "avg_memory_mb": 117.025, "max_memory_mb": 125.3, "min_memory_mb": 100.9, "memory_growth_rate": 20.5434250831604, "leak_probability": 0.0, "garbage_collections_triggered": 0, "alerts_count": 0}, "scroll_manager": {"component": "scroll_manager", "total_snapshots": 5, "avg_memory_mb": 119.28, "max_memory_mb": 126.1, "min_memory_mb": 100.9, "memory_growth_rate": 17.229362726211548, "leak_probability": 0.0, "garbage_collections_triggered": 0, "alerts_count": 5}, "anti_detection": {"component": "anti_detection", "total_snapshots": 3, "avg_memory_mb": 116.13333333333334, "max_memory_mb": 125.3, "min_memory_mb": 100.9, "memory_growth_rate": 8.076424598693848, "leak_probability": 0.0, "garbage_collections_triggered": 0, "alerts_count": 3}, "system": {"component": "system", "total_snapshots": 3, "avg_memory_mb": 116.16666666666667, "max_memory_mb": 125.3, "min_memory_mb": 101.0, "memory_growth_rate": 32.168065309524536, "leak_probability": 0.0, "garbage_collections_triggered": 0, "alerts_count": 0}}, "recent_alerts": [{"timestamp": 1754033821.2393572, "component": "scroll_manager", "alert_type": "warning", "memory_mb": 126.1, "threshold_mb": 100, "message": "组件 scroll_manager 内存使用较高: 126.1MB (阈值: 100MB)", "stage": "滚动完成: 冰箱"}, {"timestamp": 1754033820.7351894, "component": "anti_detection", "alert_type": "warning", "memory_mb": 125.3, "threshold_mb": 100, "message": "组件 anti_detection 内存使用较高: 125.3MB (阈值: 100MB)", "stage": ""}, {"timestamp": 1754033820.7191927, "component": "scroll_manager", "alert_type": "warning", "memory_mb": 125.3, "threshold_mb": 100, "message": "组件 scroll_manager 内存使用较高: 125.3MB (阈值: 100MB)", "stage": ""}, {"timestamp": 1754033790.612291, "component": "anti_detection", "alert_type": "warning", "memory_mb": 122.2, "threshold_mb": 100, "message": "组件 anti_detection 内存使用较高: 122.2MB (阈值: 100MB)", "stage": ""}, {"timestamp": 1754033790.5957897, "component": "scroll_manager", "alert_type": "warning", "memory_mb": 122.2, "threshold_mb": 100, "message": "组件 scroll_manager 内存使用较高: 122.2MB (阈值: 100MB)", "stage": ""}, {"timestamp": 1754033789.414953, "component": "scroll_manager", "alert_type": "warning", "memory_mb": 121.9, "threshold_mb": 100, "message": "组件 scroll_manager 内存使用较高: 121.9MB (阈值: 100MB)", "stage": "开始滚动: 冰箱"}, {"timestamp": 1754033760.4805038, "component": "anti_detection", "alert_type": "warning", "memory_mb": 100.9, "threshold_mb": 100, "message": "组件 anti_detection 内存使用较高: 100.9MB (阈值: 100MB)", "stage": ""}, {"timestamp": 1754033760.469858, "component": "scroll_manager", "alert_type": "warning", "memory_mb": 100.9, "threshold_mb": 100, "message": "组件 scroll_manager 内存使用较高: 100.9MB (阈值: 100MB)", "stage": ""}]}