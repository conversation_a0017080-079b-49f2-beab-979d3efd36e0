import { Component } from 'react';
import type { ErrorInfo, ReactNode } from 'react';
import { Result, Button } from 'antd';

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    // 更新 state 使下一次渲染显示错误界面
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('错误边界捕获到错误:', error, errorInfo);
    
    // 将错误信息存储到状态中
    this.setState({
      error,
      errorInfo
    });

    // 这里可以上报错误到错误监控服务
    // reportError(error, errorInfo);
  }

  handleReload = () => {
    // 重置错误状态
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
    
    // 刷新页面
    window.location.reload();
  };

  handleGoHome = () => {
    // 重置错误状态
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
    
    // 跳转到首页
    window.location.href = '/';
  };

  render() {
    if (this.state.hasError) {
      // 你可以自定义错误页面
      return (
        <div style={{ padding: '50px' }}>
          <Result
            status="error"
            title="页面出现了错误"
            subTitle="抱歉，页面遇到了意外错误，请尝试刷新页面或返回首页。"
            extra={[
              <Button type="primary" key="reload" onClick={this.handleReload}>
                刷新页面
              </Button>,
              <Button key="home" onClick={this.handleGoHome}>
                返回首页
              </Button>,
            ]}
          >
            {process.env.NODE_ENV === 'development' && (
              <div style={{ 
                marginTop: '20px',
                padding: '20px',
                backgroundColor: '#f5f5f5',
                borderRadius: '4px',
                textAlign: 'left',
                fontSize: '12px',
                fontFamily: 'monospace'
              }}>
                <details>
                  <summary style={{ marginBottom: '10px', cursor: 'pointer' }}>
                    <strong>错误详情 (仅开发环境显示)</strong>
                  </summary>
                  <div>
                    <p><strong>错误信息:</strong></p>
                    <pre style={{ whiteSpace: 'pre-wrap' }}>
                      {this.state.error && this.state.error.toString()}
                    </pre>
                    <p><strong>组件堆栈:</strong></p>
                    <pre style={{ whiteSpace: 'pre-wrap' }}>
                      {this.state.errorInfo && this.state.errorInfo.componentStack}
                    </pre>
                    <p><strong>错误堆栈:</strong></p>
                    <pre style={{ whiteSpace: 'pre-wrap' }}>
                      {this.state.error && this.state.error.stack}
                    </pre>
                  </div>
                </details>
              </div>
            )}
          </Result>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;