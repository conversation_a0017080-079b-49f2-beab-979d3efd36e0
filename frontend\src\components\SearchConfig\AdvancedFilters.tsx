import React, { useState } from 'react';
import {
  Card,
  Form,
  Slider,
  InputNumber,
  Rate,
  Select,
  Space,
  Tooltip,
  Typography,
  Row,
  Col,
  Button,
  Tag,
  Collapse
} from 'antd';
import {
  FilterOutlined,
  QuestionCircleOutlined,
  DeleteOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import type { AdvancedFiltersProps } from './types';
import { parseKeywords } from './utils';

const { Text, Title } = Typography;
const { Option } = Select;
const { Panel } = Collapse;

const AdvancedFilters: React.FC<AdvancedFiltersProps> = ({
  filters,
  onChange,
  disabled = false
}) => {
  const [excludeKeywordInput, setExcludeKeywordInput] = useState('');

  // 价格范围预设
  const pricePresets = [
    { label: '不限', value: undefined },
    { label: '0-50元', value: [0, 50] as [number, number] },
    { label: '50-100元', value: [50, 100] as [number, number] },
    { label: '100-300元', value: [100, 300] as [number, number] },
    { label: '300-500元', value: [300, 500] as [number, number] },
    { label: '500-1000元', value: [500, 1000] as [number, number] },
    { label: '1000元以上', value: [1000, 10000] as [number, number] }
  ];

  // 销量范围预设
  const soldCountPresets = [
    { label: '不限', value: undefined },
    { label: '100+', value: 100 },
    { label: '500+', value: 500 },
    { label: '1000+', value: 1000 },
    { label: '5000+', value: 5000 },
    { label: '10000+', value: 10000 }
  ];

  const handleFilterChange = (field: string, value: any) => {
    onChange({
      [field]: value
    });
  };

  // 处理价格范围变化
  const handlePriceRangeChange = (value: [number, number] | null | undefined) => {
    handleFilterChange('priceRange', value || undefined);
  };

  // 处理排除关键词添加
  const handleAddExcludeKeyword = () => {
    if (!excludeKeywordInput.trim()) return;
    
    const newKeywords = parseKeywords(excludeKeywordInput);
    const existingKeywords = filters.excludeKeywords || [];
    const uniqueKeywords = newKeywords.filter(keyword => !existingKeywords.includes(keyword));
    
    if (uniqueKeywords.length > 0) {
      handleFilterChange('excludeKeywords', [...existingKeywords, ...uniqueKeywords]);
    }
    
    setExcludeKeywordInput('');
  };

  // 移除排除关键词
  const handleRemoveExcludeKeyword = (keyword: string) => {
    const updatedKeywords = (filters.excludeKeywords || []).filter(k => k !== keyword);
    handleFilterChange('excludeKeywords', updatedKeywords.length > 0 ? updatedKeywords : undefined);
  };

  // 重置所有筛选条件
  const handleReset = () => {
    onChange({
      priceRange: undefined,
      minRating: undefined,
      minSoldCount: undefined,
      excludeKeywords: undefined
    });
  };

  // 检查是否有活跃的筛选条件
  const hasActiveFilters = !!(
    filters.priceRange ||
    filters.minRating ||
    filters.minSoldCount ||
    (filters.excludeKeywords && filters.excludeKeywords.length > 0)
  );

  return (
    <Card
      title={
        <Space>
          <FilterOutlined />
          <span>高级筛选</span>
          {hasActiveFilters && (
            <Tag color="blue" style={{ fontSize: '12px' }}>
              已启用
            </Tag>
          )}
        </Space>
      }
      size="small"
      extra={
        hasActiveFilters && (
          <Button
            type="text"
            size="small"
            icon={<ReloadOutlined />}
            onClick={handleReset}
            disabled={disabled}
          >
            重置
          </Button>
        )
      }
    >
      <Collapse
        defaultActiveKey={hasActiveFilters ? ['price', 'quality', 'exclude'] : []}
        size="small"
        ghost
      >
        {/* 价格筛选 */}
        <Panel
          header={
            <Space>
              <span>价格筛选</span>
              {filters.priceRange && (
                <Tag color="blue" style={{ fontSize: '12px' }}>
                  {filters.priceRange[0]}-{filters.priceRange[1]}元
                </Tag>
              )}
            </Space>
          }
          key="price"
        >
          <Form layout="vertical" size="small">
            {/* 价格预设 */}
            <Form.Item label="快速选择">
              <Space wrap>
                {pricePresets.map(preset => (
                  <Button
                    key={preset.label}
                    size="small"
                    type={
                      JSON.stringify(filters.priceRange) === JSON.stringify(preset.value)
                        ? 'primary'
                        : 'default'
                    }
                    onClick={() => handlePriceRangeChange(preset.value)}
                    disabled={disabled}
                  >
                    {preset.label}
                  </Button>
                ))}
              </Space>
            </Form.Item>

            {/* 自定义价格范围 */}
            <Form.Item
              label={
                <Space>
                  <span>价格范围 (元)</span>
                  <Tooltip title="设置商品价格的筛选范围，超出范围的商品将被过滤">
                    <QuestionCircleOutlined style={{ color: '#999' }} />
                  </Tooltip>
                </Space>
              }
            >
              <Row gutter={8} align="middle">
                <Col flex="auto">
                  <Slider
                    range
                    min={0}
                    max={2000}
                    step={10}
                    value={filters.priceRange || [0, 2000]}
                    onChange={(value) => handlePriceRangeChange(value as [number, number])}
                    disabled={disabled}
                    tooltip={{
                      formatter: (value) => `${value}元`
                    }}
                  />
                </Col>
              </Row>
              <Row gutter={8} style={{ marginTop: 8 }}>
                <Col span={12}>
                  <InputNumber
                    placeholder="最低价格"
                    min={0}
                    max={10000}
                    value={filters.priceRange?.[0]}
                    onChange={(value) => {
                      if (value !== null && value !== undefined) {
                        const newRange: [number, number] = [
                          value,
                          filters.priceRange?.[1] || 2000
                        ];
                        handlePriceRangeChange(newRange);
                      }
                    }}
                    style={{ width: '100%' }}
                    disabled={disabled}
                    formatter={(value) => `￥${value}`}
                    parser={(value) => Number(value?.replace('￥', '')) || 0}
                  />
                </Col>
                <Col span={12}>
                  <InputNumber
                    placeholder="最高价格"
                    min={0}
                    max={10000}
                    value={filters.priceRange?.[1]}
                    onChange={(value) => {
                      if (value !== null && value !== undefined) {
                        const newRange: [number, number] = [
                          filters.priceRange?.[0] || 0,
                          value
                        ];
                        handlePriceRangeChange(newRange);
                      }
                    }}
                    style={{ width: '100%' }}
                    disabled={disabled}
                    formatter={(value) => `￥${value}`}
                    parser={(value) => Number(value?.replace('￥', '')) || 0}
                  />
                </Col>
              </Row>
            </Form.Item>
          </Form>
        </Panel>

        {/* 质量筛选 */}
        <Panel
          header={
            <Space>
              <span>质量筛选</span>
              {(filters.minRating || filters.minSoldCount) && (
                <Tag color="green" style={{ fontSize: '12px' }}>
                  已设置
                </Tag>
              )}
            </Space>
          }
          key="quality"
        >
          <Form layout="vertical" size="small">
            <Row gutter={16}>
              {/* 最低评分 */}
              <Col xs={24} sm={12}>
                <Form.Item
                  label={
                    <Space>
                      <span>最低评分</span>
                      <Tooltip title="过滤评分低于设定值的商品">
                        <QuestionCircleOutlined style={{ color: '#999' }} />
                      </Tooltip>
                    </Space>
                  }
                >
                  <div>
                    <Rate
                      allowHalf
                      value={filters.minRating}
                      onChange={(value) => handleFilterChange('minRating', value || undefined)}
                      disabled={disabled}
                    />
                    <div style={{ marginTop: 4 }}>
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        {filters.minRating 
                          ? `${filters.minRating}星及以上` 
                          : '不限制评分'
                        }
                      </Text>
                    </div>
                  </div>
                </Form.Item>
              </Col>

              {/* 最低销量 */}
              <Col xs={24} sm={12}>
                <Form.Item
                  label={
                    <Space>
                      <span>最低销量</span>
                      <Tooltip title="过滤销量低于设定值的商品">
                        <QuestionCircleOutlined style={{ color: '#999' }} />
                      </Tooltip>
                    </Space>
                  }
                >
                  <Select
                    value={filters.minSoldCount}
                    onChange={(value) => handleFilterChange('minSoldCount', value)}
                    placeholder="选择最低销量"
                    allowClear
                    style={{ width: '100%' }}
                    disabled={disabled}
                  >
                    {soldCountPresets.map(preset => (
                      <Option key={preset.label} value={preset.value}>
                        {preset.label}
                      </Option>
                    ))}
                  </Select>
                  <div style={{ marginTop: 4 }}>
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      {filters.minSoldCount 
                        ? `${filters.minSoldCount}件及以上` 
                        : '不限制销量'
                      }
                    </Text>
                  </div>
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </Panel>

        {/* 排除关键词 */}
        <Panel
          header={
            <Space>
              <span>排除关键词</span>
              {filters.excludeKeywords && filters.excludeKeywords.length > 0 && (
                <Tag color="red" style={{ fontSize: '12px' }}>
                  {filters.excludeKeywords.length}个
                </Tag>
              )}
            </Space>
          }
          key="exclude"
        >
          <Form layout="vertical" size="small">
            <Form.Item
              label={
                <Space>
                  <span>排除包含以下关键词的商品</span>
                  <Tooltip title="输入不希望出现在搜索结果中的关键词，支持多个关键词用逗号分隔">
                    <QuestionCircleOutlined style={{ color: '#999' }} />
                  </Tooltip>
                </Space>
              }
            >
              <Space.Compact style={{ width: '100%' }}>
                <Select
                  mode="tags"
                  value={excludeKeywordInput}
                  onChange={setExcludeKeywordInput}
                  placeholder="输入要排除的关键词"
                  style={{ flex: 1 }}
                  disabled={disabled}
                  tokenSeparators={[',', '，', ' ', ';', '；']}
                  open={false}
                  maxTagCount={0}
                  maxTagPlaceholder={() => '...'}
                />
                <Button
                  type="primary"
                  onClick={handleAddExcludeKeyword}
                  disabled={disabled || !excludeKeywordInput.trim()}
                >
                  添加
                </Button>
              </Space.Compact>
            </Form.Item>

            {/* 已排除的关键词 */}
            {filters.excludeKeywords && filters.excludeKeywords.length > 0 && (
              <Form.Item label="已排除的关键词">
                <div>
                  {filters.excludeKeywords.map(keyword => (
                    <Tag
                      key={keyword}
                      closable={!disabled}
                      onClose={() => handleRemoveExcludeKeyword(keyword)}
                      color="red"
                      style={{ marginBottom: 4 }}
                    >
                      {keyword}
                    </Tag>
                  ))}
                </div>
                <div style={{ marginTop: 8 }}>
                  <Button
                    type="text"
                    size="small"
                    icon={<DeleteOutlined />}
                    danger
                    onClick={() => handleFilterChange('excludeKeywords', undefined)}
                    disabled={disabled}
                  >
                    清空排除列表
                  </Button>
                </div>
              </Form.Item>
            )}
          </Form>
        </Panel>
      </Collapse>

      {/* 筛选说明 */}
      <div style={{ marginTop: 16, padding: 12, backgroundColor: '#f0f9ff', borderRadius: 6 }}>
        <Title level={5} style={{ margin: 0, marginBottom: 8, color: '#666' }}>
          筛选说明
        </Title>
        <div style={{ fontSize: '12px', color: '#666', lineHeight: 1.5 }}>
          <div>• <strong>价格筛选</strong>：根据商品价格范围过滤结果</div>
          <div>• <strong>最低评分</strong>：只保留评分达到要求的商品</div>
          <div>• <strong>最低销量</strong>：只保留销量达到要求的商品</div>
          <div>• <strong>排除关键词</strong>：过滤掉标题包含指定关键词的商品</div>
        </div>
      </div>

      {/* 筛选效果预警 */}
      {hasActiveFilters && (
        <div style={{ 
          marginTop: 12, 
          padding: 12, 
          backgroundColor: '#fff7e6', 
          borderRadius: 6,
          border: '1px solid #ffd591'
        }}>
          <div style={{ fontSize: '12px', color: '#d4621b' }}>
            <strong>⚠️ 筛选提醒：</strong>
            启用筛选条件可能会显著减少搜索结果数量，如果结果过少，请适当放宽筛选条件。
          </div>
        </div>
      )}
    </Card>
  );
};

export default AdvancedFilters;