import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Space,
  List,
  Typography,
  Empty,
  Dropdown,
  Modal,
  Form,
  Input,
  message,
  Tag,
  Tooltip,
} from 'antd';
import {
  PlayCircleOutlined,
  StarOutlined,
  StarFilled,
  EditOutlined,
  DeleteOutlined,
  PlusOutlined,
  MoreOutlined,
  ThunderboltOutlined,
  ClockCircleOutlined,
  FireOutlined
} from '@ant-design/icons';
import type { MenuProps } from 'antd';
import type { SearchConfig } from '@/types';
import type { QuickStartConfig } from './types';
import { getConfigSummary, storage } from './utils';

const { Text, Paragraph } = Typography;

interface QuickStartProps {
  onStart: (config: SearchConfig) => void;
  currentConfig?: SearchConfig;
  loading?: boolean;
  className?: string;
}

const QuickStart: React.FC<QuickStartProps> = ({
  onStart,
  currentConfig,
  loading = false,
  className
}) => {
  const [quickConfigs, setQuickConfigs] = useState<QuickStartConfig[]>([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingConfig, setEditingConfig] = useState<QuickStartConfig | null>(null);
  const [form] = Form.useForm();

  // 加载快速配置
  const loadQuickConfigs = () => {
    const configs = storage.getQuickConfigs();
    setQuickConfigs(configs);
  };

  useEffect(() => {
    loadQuickConfigs();
  }, []);

  // 保存当前配置
  const handleSaveCurrentConfig = () => {
    if (!currentConfig?.keyword) {
      message.warning('请先配置搜索参数');
      return;
    }

    const newConfig: QuickStartConfig = {
      id: Date.now().toString(),
      name: `${currentConfig.keyword} - 快速配置`,
      config: currentConfig,
      lastUsed: new Date().toISOString(),
      useCount: 0
    };

    setEditingConfig(newConfig);
    form.setFieldsValue({ name: newConfig.name });
    setModalVisible(true);
  };

  // 编辑配置
  const handleEditConfig = (config: QuickStartConfig) => {
    setEditingConfig(config);
    form.setFieldsValue({ name: config.name });
    setModalVisible(true);
  };

  // 删除配置
  const handleDeleteConfig = (id: string) => {
    storage.removeQuickConfig(id);
    loadQuickConfigs();
    message.success('配置已删除');
  };

  // 启动配置
  const handleStartConfig = (config: QuickStartConfig) => {
    // 更新使用次数和最后使用时间
    const updatedConfig = {
      ...config,
      useCount: config.useCount + 1,
      lastUsed: new Date().toISOString()
    };
    
    storage.saveQuickConfig(updatedConfig);
    loadQuickConfigs();
    
    onStart(config.config);
    message.success(`已启动配置: ${config.name}`);
  };

  // 保存配置
  const handleSaveConfig = async () => {
    try {
      const values = await form.validateFields();
      
      if (!editingConfig) return;

      const configToSave = {
        ...editingConfig,
        name: values.name
      };

      storage.saveQuickConfig(configToSave);
      loadQuickConfigs();
      setModalVisible(false);
      setEditingConfig(null);
      form.resetFields();
      
      message.success('配置已保存');
    } catch (error) {
      console.error('保存配置失败:', error);
    }
  };

  // 获取配置操作菜单
  const getConfigMenu = (config: QuickStartConfig): MenuProps['items'] => [
    {
      key: 'edit',
      label: '编辑',
      icon: <EditOutlined />,
      onClick: () => handleEditConfig(config)
    },
    {
      key: 'delete',
      label: '删除',
      icon: <DeleteOutlined />,
      danger: true,
      onClick: () => {
        Modal.confirm({
          title: '确认删除配置？',
          content: `确定要删除配置 "${config.name}" 吗？`,
          okText: '确认删除',
          cancelText: '取消',
          okButtonProps: { danger: true },
          onOk: () => handleDeleteConfig(config.id)
        });
      }
    }
  ];

  // 预定义配置
  const presetConfigs: QuickStartConfig[] = [
    {
      id: 'preset-phone',
      name: '手机数码',
      config: {
        keyword: '手机',
        category: 'digital',
        sortType: 'sales',
        pageSize: 20,
        maxPages: 5
      },
      useCount: 0
    },
    {
      id: 'preset-clothing',
      name: '服装鞋包',
      config: {
        keyword: '连衣裙',
        category: 'clothing',
        sortType: 'sales',
        pageSize: 20,
        maxPages: 3
      },
      useCount: 0
    },
    {
      id: 'preset-home',
      name: '家居用品',
      config: {
        keyword: '餐具',
        category: 'home',
        sortType: 'rating',
        pageSize: 20,
        maxPages: 3
      },
      useCount: 0
    }
  ];

  // 获取常用配置（按使用次数排序）
  const popularConfigs = quickConfigs
    .filter(config => config.useCount > 0)
    .sort((a, b) => b.useCount - a.useCount)
    .slice(0, 3);

  // 获取最近使用配置
  const recentConfigs = quickConfigs
    .filter(config => config.lastUsed)
    .sort((a, b) => new Date(b.lastUsed!).getTime() - new Date(a.lastUsed!).getTime())
    .slice(0, 3);

  return (
    <div className={className}>
      <Space direction="vertical" style={{ width: '100%' }} size="large">
        {/* 当前配置快速保存 */}
        {currentConfig?.keyword && (
          <Card 
            size="small" 
            title={<Space><ThunderboltOutlined />当前配置</Space>}
            extra={
              <Button 
                size="small" 
                icon={<StarOutlined />}
                onClick={handleSaveCurrentConfig}
              >
                保存为快速配置
              </Button>
            }
          >
            <Paragraph ellipsis={{ rows: 2 }}>
              {getConfigSummary(currentConfig).join(' • ')}
            </Paragraph>
          </Card>
        )}

        {/* 常用配置 */}
        {popularConfigs.length > 0 && (
          <Card 
            size="small"
            title={<Space><FireOutlined />常用配置</Space>}
          >
            <List
              size="small"
              dataSource={popularConfigs}
              renderItem={(config) => (
                <List.Item
                  actions={[
                    <Tooltip key="start" title="启动配置">
                      <Button
                        type="primary"
                        size="small"
                        icon={<PlayCircleOutlined />}
                        loading={loading}
                        onClick={() => handleStartConfig(config)}
                      />
                    </Tooltip>,
                    <Dropdown
                      key="more"
                      menu={{ items: getConfigMenu(config) }}
                      trigger={['click']}
                      placement="bottomRight"
                    >
                      <Button size="small" icon={<MoreOutlined />} />
                    </Dropdown>
                  ]}
                >
                  <List.Item.Meta
                    title={
                      <Space>
                        <Text strong>{config.name}</Text>
                        <Tag color="orange">{config.useCount} 次</Tag>
                      </Space>
                    }
                    description={getConfigSummary(config.config).slice(0, 2).join(' • ')}
                  />
                </List.Item>
              )}
            />
          </Card>
        )}

        {/* 最近使用 */}
        {recentConfigs.length > 0 && (
          <Card 
            size="small"
            title={<Space><ClockCircleOutlined />最近使用</Space>}
          >
            <List
              size="small"
              dataSource={recentConfigs}
              renderItem={(config) => (
                <List.Item
                  actions={[
                    <Tooltip key="start" title="启动配置">
                      <Button
                        type="primary"
                        size="small"
                        icon={<PlayCircleOutlined />}
                        loading={loading}
                        onClick={() => handleStartConfig(config)}
                      />
                    </Tooltip>,
                    <Dropdown
                      key="more"
                      menu={{ items: getConfigMenu(config) }}
                      trigger={['click']}
                      placement="bottomRight"
                    >
                      <Button size="small" icon={<MoreOutlined />} />
                    </Dropdown>
                  ]}
                >
                  <List.Item.Meta
                    title={config.name}
                    description={
                      <Space>
                        <Text type="secondary">
                          {new Date(config.lastUsed!).toLocaleString()}
                        </Text>
                        <Text>{getConfigSummary(config.config)[0]}</Text>
                      </Space>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        )}

        {/* 预设配置 */}
        <Card 
          size="small"
          title={<Space><StarFilled />预设配置</Space>}
        >
          <List
            size="small"
            dataSource={presetConfigs}
            renderItem={(config) => (
              <List.Item
                actions={[
                  <Button
                    key="start"
                    type="primary"
                    size="small"
                    icon={<PlayCircleOutlined />}
                    loading={loading}
                    onClick={() => handleStartConfig(config)}
                  >
                    启动
                  </Button>
                ]}
              >
                <List.Item.Meta
                  title={config.name}
                  description={getConfigSummary(config.config).slice(0, 3).join(' • ')}
                />
              </List.Item>
            )}
          />
        </Card>

        {/* 我的配置 */}
        <Card 
          size="small"
          title="我的配置"
          extra={
            <Button 
              size="small" 
              icon={<PlusOutlined />}
              onClick={handleSaveCurrentConfig}
              disabled={!currentConfig?.keyword}
            >
              添加配置
            </Button>
          }
        >
          {quickConfigs.length === 0 ? (
            <Empty 
              description="暂无保存的配置"
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              style={{ margin: '20px 0' }}
            />
          ) : (
            <List
              size="small"
              dataSource={quickConfigs}
              renderItem={(config) => (
                <List.Item
                  actions={[
                    <Tooltip key="start" title="启动配置">
                      <Button
                        type="primary"
                        size="small"
                        icon={<PlayCircleOutlined />}
                        loading={loading}
                        onClick={() => handleStartConfig(config)}
                      />
                    </Tooltip>,
                    <Dropdown
                      key="more"
                      menu={{ items: getConfigMenu(config) }}
                      trigger={['click']}
                      placement="bottomRight"
                    >
                      <Button size="small" icon={<MoreOutlined />} />
                    </Dropdown>
                  ]}
                >
                  <List.Item.Meta
                    title={
                      <Space>
                        <Text>{config.name}</Text>
                        {config.useCount > 0 && (
                          <Tag>{config.useCount} 次</Tag>
                        )}
                      </Space>
                    }
                    description={
                      <div>
                        <Paragraph ellipsis={{ rows: 1 }}>
                          {getConfigSummary(config.config).join(' • ')}
                        </Paragraph>
                        {config.lastUsed && (
                          <Text type="secondary" style={{ fontSize: '12px' }}>
                            最后使用: {new Date(config.lastUsed).toLocaleString()}
                          </Text>
                        )}
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          )}
        </Card>
      </Space>

      {/* 编辑配置弹窗 */}
      <Modal
        title={editingConfig?.id.startsWith('preset') ? '预设配置无法编辑' : '编辑配置'}
        open={modalVisible}
        onOk={handleSaveConfig}
        onCancel={() => {
          setModalVisible(false);
          setEditingConfig(null);
          form.resetFields();
        }}
        okText="保存"
        cancelText="取消"
      >
        {editingConfig && (
          <Form form={form} layout="vertical">
            <Form.Item
              name="name"
              label="配置名称"
              rules={[
                { required: true, message: '请输入配置名称' },
                { max: 50, message: '名称长度不能超过50个字符' }
              ]}
            >
              <Input placeholder="请输入配置名称" />
            </Form.Item>
            
            <Form.Item label="配置内容">
              <div style={{ 
                padding: '12px', 
                backgroundColor: '#f5f5f5', 
                borderRadius: '6px' 
              }}>
                {getConfigSummary(editingConfig.config).map((item, index) => (
                  <div key={index} style={{ marginBottom: '4px' }}>
                    <Text type="secondary">• {item}</Text>
                  </div>
                ))}
              </div>
            </Form.Item>
          </Form>
        )}
      </Modal>
    </div>
  );
};

export default QuickStart;