#!/bin/bash

# 设置颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}📋 $1${NC}"
}

echo "============================="
echo "  拼多多爬虫前端启动脚本"
echo "============================="
echo

# 检查Node.js是否安装
echo "[1/6] 检查Node.js环境..."
if ! command -v node &> /dev/null; then
    print_error "未找到Node.js，请先安装Node.js"
    echo "安装方法："
    echo "  Ubuntu/Debian: sudo apt-get install nodejs npm"
    echo "  CentOS/RHEL: sudo yum install nodejs npm"
    echo "  macOS: brew install node"
    echo "  或访问: https://nodejs.org/"
    exit 1
fi

NODE_VERSION=$(node --version)
print_success "Node.js版本: $NODE_VERSION"

# 检查npm是否可用
echo "[2/6] 检查npm环境..."
if ! command -v npm &> /dev/null; then
    print_error "未找到npm，请重新安装Node.js"
    exit 1
fi

NPM_VERSION=$(npm --version)
print_success "npm版本: $NPM_VERSION"

# 切换到脚本所在目录
echo "[3/6] 切换到前端目录..."
cd "$(dirname "$0")"
if [ ! -f "package.json" ]; then
    print_error "当前目录下未找到package.json文件"
    print_error "请确保脚本位于frontend目录中"
    exit 1
fi
print_success "当前目录: $(pwd)"

# 检查端口占用
echo "[4/6] 检查端口占用..."
if command -v lsof &> /dev/null; then
    if lsof -i :5173 &> /dev/null; then
        print_warning "端口5173已被占用，将尝试使用其他端口"
    else
        print_success "端口5173可用"
    fi
elif command -v netstat &> /dev/null; then
    if netstat -an | grep ":5173" | grep "LISTEN" &> /dev/null; then
        print_warning "端口5173已被占用，将尝试使用其他端口"
    else
        print_success "端口5173可用"
    fi
else
    print_info "无法检查端口占用状态，继续启动..."
fi

# 检查依赖是否安装
echo "[5/6] 检查依赖安装状态..."
if [ ! -d "node_modules" ]; then
    print_info "未找到node_modules，开始安装依赖..."
    echo "这可能需要几分钟时间，请耐心等待..."
    if ! npm install; then
        print_error "依赖安装失败，请检查网络连接"
        exit 1
    fi
    print_success "依赖安装完成"
else
    print_success "依赖已安装"
fi

# 启动开发服务器
echo "[6/6] 启动前端开发服务器..."
echo
echo "🚀 正在启动前端服务器..."
echo "📍 服务地址: http://localhost:5173"
echo "🔧 开发模式: Vite + React + TypeScript"
echo "📝 热重载: 已启用"
echo
echo "提示："
echo "- 按Ctrl+C停止服务器"
echo "- 浏览器会自动打开前端页面"
echo "- 确保后端服务已启动(端口8001)"
echo

# 启动服务
npm run dev

# 如果服务意外退出
echo
print_warning "前端服务已停止"