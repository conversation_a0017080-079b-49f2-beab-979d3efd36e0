# 爬取控制面板集成测试指南

## 测试环境准备

### 1. 启动开发环境

```bash
# 启动前端开发服务器
cd frontend
npm run dev

# 启动后端服务器
cd backend
python api_server.py
```

### 2. 访问测试页面

打开浏览器访问: `http://localhost:5173`

## 功能测试清单

### ✅ 基础功能测试

#### 1. 页面加载测试
- [ ] 页面正常加载，无控制台错误
- [ ] 步骤指示器正确显示 4 个步骤
- [ ] WebSocket 连接状态正常显示
- [ ] 各个标签页能正常切换

#### 2. Cookie 验证测试
- [ ] 点击"Cookie验证"步骤
- [ ] Cookie 管理组件正常显示
- [ ] 能够导入和验证 Cookie
- [ ] 验证成功后状态指示器变为绿色
- [ ] 验证失败时显示错误信息

#### 3. 搜索配置测试
- [ ] 配置表单所有字段正常工作
- [ ] 必填字段验证正确
- [ ] 价格范围验证正确
- [ ] 配置验证通过后状态指示器变为绿色

#### 4. 任务启动测试
- [ ] Cookie和配置验证通过后，启动按钮可用
- [ ] 点击启动按钮能成功启动任务
- [ ] 任务启动后自动切换到监控标签页
- [ ] WebSocket 连接建立成功

#### 5. 实时监控测试
- [ ] 进度信息实时更新
- [ ] 数据收集统计正确显示
- [ ] 任务控制按钮状态正确
- [ ] 错误信息正确处理

### 🚀 高级功能测试

#### 1. 任务控制测试
- [ ] 暂停按钮正常工作（如已实现）
- [ ] 恢复按钮正常工作（如已实现）
- [ ] 停止按钮正常工作
- [ ] 取消按钮正常工作
- [ ] 重试按钮正常工作

#### 2. 任务列表测试
- [ ] 活动任务正确显示
- [ ] 任务状态实时更新
- [ ] 任务进度正确显示
- [ ] 批量操作功能正常
- [ ] 任务切换功能正常

#### 3. 快速启动测试
- [ ] 预设配置能正常启动
- [ ] 保存当前配置功能正常
- [ ] 自定义配置管理正常
- [ ] 配置使用统计正确
- [ ] 最近使用配置显示正确

#### 4. 历史记录测试
- [ ] 历史任务正确显示
- [ ] 筛选和搜索功能正常
- [ ] 任务详情查看正常
- [ ] 重新运行功能正常
- [ ] 批量删除功能正常
- [ ] 数据导出功能正常

#### 5. 数据预览测试
- [ ] 收集的数据正确显示
- [ ] 数据统计信息正确
- [ ] 表格功能正常（排序、筛选）
- [ ] 数据导出功能正常
- [ ] 实时数据更新正常

### 🔧 错误处理测试

#### 1. 网络错误测试
- [ ] 断网情况下的错误处理
- [ ] WebSocket 断开重连功能
- [ ] API 请求失败处理
- [ ] 超时处理

#### 2. 数据错误测试
- [ ] 无效 Cookie 处理
- [ ] 错误配置参数处理
- [ ] 服务器错误响应处理
- [ ] 数据格式错误处理

#### 3. 用户操作错误测试
- [ ] 重复点击按钮处理
- [ ] 无效输入处理
- [ ] 权限不足处理
- [ ] 浏览器兼容性测试

### 📱 响应式设计测试

#### 1. 不同屏幕尺寸测试
- [ ] 桌面端 (1920x1080) 显示正常
- [ ] 笔记本 (1366x768) 显示正常
- [ ] 平板 (768x1024) 显示正常
- [ ] 手机 (375x667) 显示正常

#### 2. 组件自适应测试
- [ ] 表格在小屏幕上正常滚动
- [ ] 按钮组合理布局
- [ ] 步骤指示器自适应
- [ ] 标签页在小屏幕上正常工作

### ⚡ 性能测试

#### 1. 加载性能测试
- [ ] 首次加载时间 < 3秒
- [ ] 组件懒加载正常
- [ ] 大量数据加载性能
- [ ] 内存使用合理

#### 2. 运行时性能测试
- [ ] 实时数据更新不卡顿
- [ ] 大量任务列表滚动流畅
- [ ] 历史记录查询响应快速
- [ ] 组件切换动画流畅

## 测试数据准备

### 1. Cookie 数据
准备有效的拼多多 Cookie 数据用于测试

### 2. 搜索配置
```javascript
// 基础配置
{
  keyword: "手机",
  category: "digital",
  sortType: "sales",
  pageSize: 20,
  maxPages: 5
}

// 复杂配置
{
  keyword: "苹果手机",
  category: "digital",
  minPrice: 3000,
  maxPrice: 8000,
  sortType: "price_desc",
  pageSize: 50,
  maxPages: 10
}
```

### 3. 测试任务数据
创建多个不同状态的测试任务来验证各种场景

## 测试报告模板

### 测试结果记录

| 功能模块 | 测试项目 | 预期结果 | 实际结果 | 状态 | 备注 |
|---------|---------|---------|---------|------|------|
| Cookie验证 | 页面加载 | 正常显示 | ✅ 正常 | 通过 | - |
| 搜索配置 | 表单验证 | 正确验证 | ✅ 正常 | 通过 | - |
| 任务启动 | 启动流程 | 成功启动 | ❌ 失败 | 失败 | API错误 |
| ... | ... | ... | ... | ... | ... |

### 问题记录

| 问题ID | 问题描述 | 严重程度 | 复现步骤 | 预期修复时间 |
|--------|---------|----------|----------|-------------|
| BUG-001 | Cookie验证失败 | 高 | 1.导入Cookie 2.点击验证 | 1天 |
| ... | ... | ... | ... | ... |

### 性能指标

| 指标 | 目标值 | 实际值 | 状态 |
|------|--------|--------|------|
| 首次加载时间 | <3秒 | 2.1秒 | ✅通过 |
| WebSocket连接时间 | <1秒 | 0.8秒 | ✅通过 |
| 数据更新延迟 | <500ms | 300ms | ✅ 通过 |
| 内存使用峰值 | <100MB | 85MB | ✅ 通过 |

## 自动化测试脚本

### Playwright 测试示例

```javascript
// test/crawl-control.spec.js
import { test, expect } from '@playwright/test';

test('爬取控制面板基础功能', async ({ page }) => {
  await page.goto('http://localhost:5173');
  
  // 测试页面加载
  await expect(page.locator('h2')).toContainText('爬取控制面板');
  
  // 测试步骤指示器
  await expect(page.locator('.ant-steps-item')).toHaveCount(4);
  
  // 测试Cookie验证
  await page.click('[data-testid="cookie-step"]');
  await expect(page.locator('[data-testid="cookie-manager"]')).toBeVisible();
  
  // 测试搜索配置
  await page.click('[data-testid="config-step"]');
  await page.fill('[name="keyword"]', '手机');
  await page.selectOption('[name="category"]', 'digital');
  
  // 更多测试...
});
```

### Jest 单元测试示例

```javascript
// test/utils.test.js
import { validateConfigStep, generateTaskName } from '../src/components/CrawlControl/utils';

describe('CrawlControl Utils', () => {
  test('validateConfigStep - 正确配置', () => {
    const config = {
      keyword: '手机',
      pageSize: 20,
      maxPages: 10
    };
    
    const result = validateConfigStep(config);
    expect(result.valid).toBe(true);
  });
  
  test('generateTaskName - 生成任务名称', () => {
    const config = { keyword: '手机' };
    const name = generateTaskName(config);
    
    expect(name).toContain('手机');
    expect(name).toMatch(/\d{2}\/\d{2}/); // 包含日期
  });
});
```

## 部署前检查清单

### 1. 代码质量检查
- [ ] ESLint 检查通过
- [ ] TypeScript 类型检查通过
- [ ] 单元测试覆盖率 > 80%
- [ ] 集成测试通过

### 2. 性能优化检查
- [ ] Bundle 大小合理
- [ ] 图片资源优化
- [ ] 代码分割实现
- [ ] 缓存策略配置

### 3. 安全检查
- [ ] XSS 防护
- [ ] CSRF 防护
- [ ] 敏感信息处理
- [ ] 权限控制验证

### 4. 兼容性检查
- [ ] Chrome 最新版本
- [ ] Firefox 最新版本
- [ ] Safari 最新版本
- [ ] Edge 最新版本
- [ ] 移动端浏览器

## 测试完成标准

1. ✅ 所有核心功能正常工作
2. ✅ 错误处理机制完善
3. ✅ 性能指标达标
4. ✅ 用户体验良好
5. ✅ 代码质量符合标准
6. ✅ 文档完整准确

---

**测试负责人**: [姓名]  
**测试时间**: [日期]  
**测试环境**: [环境描述]  
**测试版本**: [版本号]