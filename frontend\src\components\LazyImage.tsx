import React, { useState, useRef, useEffect } from 'react';
import { Image, Skeleton } from 'antd';

interface LazyImageProps {
  src: string;
  alt: string;
  width?: number | string;
  height?: number | string;
  className?: string;
  style?: React.CSSProperties;
  fallback?: string;
  placeholder?: React.ReactNode;
  onClick?: () => void;
}

const LazyImage: React.FC<LazyImageProps> = ({
  src,
  alt,
  width = 60,
  height = 60,
  className,
  style,
  fallback,
  placeholder,
  onClick,
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(false);
  const [hasError, setHasError] = useState(false);
  const imgRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      {
        threshold: 0.1,
        rootMargin: '50px',
      }
    );

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => observer.disconnect();
  }, []);

  const handleLoad = () => {
    setIsLoaded(true);
  };

  const handleError = () => {
    setHasError(true);
    setIsLoaded(true);
  };

  const defaultFallback = "data:image/png;base64,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 identify";

  const containerStyle: React.CSSProperties = {
    width,
    height,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f0f0f0',
    border: '1px solid #d9d9d9',
    borderRadius: '4px',
    overflow: 'hidden',
    cursor: onClick ? 'pointer' : 'default',
    ...style,
  };

  if (!isInView) {
    return (
      <div
        ref={imgRef}
        className={className}
        style={containerStyle}
        onClick={onClick}
      >
        {placeholder || (
          <Skeleton.Image
            style={{ width: '100%', height: '100%' }}
            active={false}
          />
        )}
      </div>
    );
  }

  if (hasError) {
    return (
      <div
        className={className}
        style={containerStyle}
        onClick={onClick}
      >
        <span style={{ fontSize: '12px', color: '#999' }}>加载失败</span>
      </div>
    );
  }

  return (
    <div
      className={className}
      style={containerStyle}
      onClick={onClick}
    >
      {!isLoaded && (
        <Skeleton.Image
          style={{ 
            width: '100%', 
            height: '100%',
            position: 'absolute',
            zIndex: 1
          }}
          active
        />
      )}
      <Image
        src={src}
        alt={alt}
        width="100%"
        height="100%"
        style={{
          objectFit: 'cover',
          opacity: isLoaded ? 1 : 0,
          transition: 'opacity 0.3s ease-in-out',
        }}
        preview={false}
        fallback={fallback || defaultFallback}
        onLoad={handleLoad}
        onError={handleError}
      />
    </div>
  );
};

export default LazyImage;