@echo off
chcp 65001 >nul
echo =============================
echo  拼多多爬虫前端启动脚本
echo =============================
echo.

:: 设置颜色
color 0A

:: 检查Node.js是否安装
echo [1/6] 检查Node.js环境...
where node >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到Node.js，请先安装Node.js
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
)

:: 显示Node.js版本
for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
echo ✅ Node.js版本: %NODE_VERSION%

:: 检查npm是否可用
echo [2/6] 检查npm环境...
where npm >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到npm，请重新安装Node.js
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('npm --version') do set NPM_VERSION=%%i
echo ✅ npm版本: %NPM_VERSION%

:: 切换到frontend目录
echo [3/6] 切换到前端目录...
cd /d "%~dp0"
if not exist "package.json" (
    echo ❌ 错误: 当前目录下未找到package.json文件
    echo 请确保脚本位于frontend目录中
    pause
    exit /b 1
)
echo ✅ 当前目录: %CD%

:: 检查端口占用
echo [4/6] 检查端口占用...
netstat -an | find "LISTENING" | find ":5173" >nul
if %errorlevel% equ 0 (
    echo ⚠️  警告: 端口5173已被占用，将尝试使用其他端口
) else (
    echo ✅ 端口5173可用
)

:: 检查依赖是否安装
echo [5/6] 检查依赖安装状态...
if not exist "node_modules" (
    echo 📦 未找到node_modules，开始安装依赖...
    echo 这可能需要几分钟时间，请耐心等待...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ 依赖安装失败，请检查网络连接
        pause
        exit /b 1
    )
    echo ✅ 依赖安装完成
) else (
    echo ✅ 依赖已安装
)

:: 启动开发服务器
echo [6/6] 启动前端开发服务器...
echo.
echo 🚀 正在启动前端服务器...
echo 📍 服务地址: http://localhost:5173
echo 🔧 开发模式: Vite + React + TypeScript
echo 📝 热重载: 已启用
echo.
echo 提示：
echo - 按Ctrl+C停止服务器
echo - 浏览器会自动打开前端页面
echo - 确保后端服务已启动(端口8001)
echo.

:: 启动服务
npm run dev

:: 如果服务意外退出
echo.
echo ⚠️  前端服务已停止
pause