"""
日志管理器模块
配置和管理应用程序日志
"""

import sys
from pathlib import Path
from loguru import logger
from typing import Dict, Any

from .helpers import load_config, ensure_dir, get_timestamp_string


class LoggerManager:
    """日志管理器类"""

    _initialized = False

    def __init__(self, config_path: str = "config/settings.yaml"):
        """初始化日志管理器"""
        if LoggerManager._initialized:
            return

        self.config = load_config(config_path)
        self.logging_config = self.config.get("logging", {})
        self.setup_logger()
        LoggerManager._initialized = True
    
    def setup_logger(self) -> None:
        """设置日志配置"""
        # 移除默认处理器
        logger.remove()
        
        # 获取配置
        log_level = self.logging_config.get("level", "INFO")
        log_dir = self.logging_config.get("log_dir", "./logs")
        log_file_template = self.logging_config.get("log_file_template", "pdd_crawler_{date}.log")
        retention_days = self.logging_config.get("retention_days", 7)
        console_output = self.logging_config.get("console_output", True)
        
        # 确保日志目录存在
        ensure_dir(log_dir)
        
        # 控制台输出
        if console_output:
            logger.add(
                sys.stdout,
                level=log_level,
                format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
                       "<level>{level: <8}</level> | "
                       "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
                       "<level>{message}</level>",
                colorize=True
            )
        
        # 文件输出
        log_file = Path(log_dir) / log_file_template.format(
            date=get_timestamp_string()[:8]  # YYYYMMDD
        )
        
        logger.add(
            str(log_file),
            level=log_level,
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}",
            rotation="1 day",
            retention=f"{retention_days} days",
            compression="zip",
            encoding="utf-8"
        )
        
        # 错误日志单独文件
        error_log_file = Path(log_dir) / f"error_{get_timestamp_string()[:8]}.log"
        logger.add(
            str(error_log_file),
            level="ERROR",
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}",
            rotation="1 day",
            retention=f"{retention_days} days",
            compression="zip",
            encoding="utf-8"
        )
        
        logger.info("日志系统初始化完成")
    
    @staticmethod
    def log_function_call(func_name: str, args: Dict[str, Any] = None, kwargs: Dict[str, Any] = None) -> None:
        """记录函数调用"""
        args_str = f"args={args}" if args else ""
        kwargs_str = f"kwargs={kwargs}" if kwargs else ""
        params = ", ".join(filter(None, [args_str, kwargs_str]))
        logger.debug(f"调用函数: {func_name}({params})")
    
    @staticmethod
    def log_performance(operation: str, duration: float, details: str = "") -> None:
        """记录性能信息"""
        logger.info(f"性能统计 - {operation}: {duration:.2f}秒 {details}")
    
    @staticmethod
    def log_data_stats(operation: str, count: int, details: str = "") -> None:
        """记录数据统计"""
        logger.info(f"数据统计 - {operation}: {count}条 {details}")
    
    @staticmethod
    def log_error_with_context(error: Exception, context: Dict[str, Any] = None) -> None:
        """记录带上下文的错误"""
        context_str = f"上下文: {context}" if context else ""
        logger.error(f"错误: {str(error)} {context_str}", exc_info=True)


# 创建全局日志管理器实例（单例模式）
_log_manager_instance = None

def get_log_manager():
    """获取日志管理器实例（单例模式）"""
    global _log_manager_instance
    if _log_manager_instance is None:
        _log_manager_instance = LoggerManager()
    return _log_manager_instance

# 为了向后兼容，保留这个变量
log_manager = get_log_manager()


def get_logger():
    """获取日志器实例"""
    return logger


# 便捷函数
def log_debug(message: str) -> None:
    """记录调试信息"""
    logger.debug(message)


def log_info(message: str) -> None:
    """记录信息"""
    logger.info(message)


def log_warning(message: str) -> None:
    """记录警告"""
    logger.warning(message)


def log_error(message: str, exc_info: bool = False) -> None:
    """记录错误"""
    logger.error(message, exc_info=exc_info)


def log_critical(message: str) -> None:
    """记录严重错误"""
    logger.critical(message)


def log_function_start(func_name: str, **kwargs) -> None:
    """记录函数开始"""
    params = ", ".join([f"{k}={v}" for k, v in kwargs.items()])
    logger.debug(f"开始执行: {func_name}({params})")


def log_function_end(func_name: str, result: Any = None) -> None:
    """记录函数结束"""
    result_str = f" -> {result}" if result is not None else ""
    logger.debug(f"执行完成: {func_name}{result_str}")


def log_step(step_name: str, details: str = "") -> None:
    """记录步骤"""
    logger.info(f"步骤: {step_name} {details}")


def log_progress(current: int, total: int, operation: str = "") -> None:
    """记录进度"""
    percentage = (current / total * 100) if total > 0 else 0
    logger.info(f"进度: {current}/{total} ({percentage:.1f}%) {operation}")


def log_success(message: str) -> None:
    """记录成功信息"""
    logger.success(message)


def log_exception(exc: Exception, context: str = "") -> None:
    """记录异常"""
    context_str = f" - {context}" if context else ""
    logger.exception(f"异常{context_str}: {str(exc)}")


# 装饰器
def log_execution_time(func):
    """记录函数执行时间的装饰器"""
    import time
    import functools
    
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            duration = time.time() - start_time
            logger.debug(f"函数 {func.__name__} 执行时间: {duration:.2f}秒")
            return result
        except Exception as e:
            duration = time.time() - start_time
            logger.error(f"函数 {func.__name__} 执行失败 (耗时: {duration:.2f}秒): {str(e)}")
            raise
    
    return wrapper


def log_async_execution_time(func):
    """记录异步函数执行时间的装饰器"""
    import time
    import functools
    
    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = await func(*args, **kwargs)
            duration = time.time() - start_time
            logger.debug(f"异步函数 {func.__name__} 执行时间: {duration:.2f}秒")
            return result
        except Exception as e:
            duration = time.time() - start_time
            logger.error(f"异步函数 {func.__name__} 执行失败 (耗时: {duration:.2f}秒): {str(e)}")
            raise
    
    return wrapper
