@echo off
chcp 65001 >nul
echo =============================
echo  拼多多爬虫前端部署脚本
echo =============================
echo.

:: 设置颜色
color 0B

echo [1/5] 环境检查...
:: 检查Node.js
where node >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到Node.js
    pause
    exit /b 1
)

:: 检查npm
where npm >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到npm
    pause
    exit /b 1
)

echo ✅ 环境检查通过

echo [2/5] 安装依赖...
if not exist "node_modules" (
    echo 📦 安装项目依赖...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
) else (
    echo ✅ 依赖已存在
)

echo [3/5] 代码检查...
echo 🔍 运行ESLint检查...
npm run lint
if %errorlevel% neq 0 (
    echo ⚠️  代码检查发现问题，是否继续部署？(y/n)
    set /p continue=
    if /i not "%continue%"=="y" (
        echo 部署已取消
        pause
        exit /b 1
    )
)

echo 📋 运行TypeScript类型检查...
npm run type-check
if %errorlevel% neq 0 (
    echo ❌ TypeScript类型检查失败
    pause
    exit /b 1
)

echo [4/5] 构建生产版本...
echo 🏗️  构建中，请稍等...
npm run build
if %errorlevel% neq 0 (
    echo ❌ 构建失败
    pause
    exit /b 1
)

echo ✅ 构建完成
echo 📊 构建产物信息:
dir /s dist

echo [5/5] 部署准备...
echo 🚀 构建产物已生成到 dist 目录
echo 
echo 部署建议：
echo 1. 将 dist 目录上传到Web服务器
echo 2. 配置服务器支持单页应用（SPA）路由
echo 3. 配置HTTPS和GZIP压缩
echo 4. 设置静态资源缓存策略
echo.
echo 预览构建结果：
echo   npm run preview
echo.
echo 本地预览地址：
echo   http://localhost:4173
echo.

echo ✅ 部署脚本执行完成！
pause