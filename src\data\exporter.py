"""
Excel导出器模块
负责将商品数据导出为格式化的Excel文件
"""

import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
from openpyxl.utils import get_column_letter
from openpyxl.worksheet.table import Table, TableStyleInfo
from loguru import logger
import pandas as pd

from src.utils.helpers import load_config, ensure_dir, sanitize_filename, get_timestamp_string


class ExcelExporter:
    """Excel导出器类"""
    
    def __init__(self, config_path: str = "config/settings.yaml"):
        """初始化Excel导出器"""
        self.config = load_config(config_path)
        self.export_config = self.config.get("export", {})
        self.excel_config = self.export_config.get("excel", {})
        
        # 导出配置
        self.output_dir = self.export_config.get("output_dir", "./output")
        self.filename_template = self.export_config.get("filename_template", "拼多多商品数据_{timestamp}.xlsx")
        self.sheet_name_template = self.excel_config.get("sheet_name_template", "{keyword}_商品数据")
        self.column_mapping = self.excel_config.get("column_mapping", {})
        
        # 确保输出目录存在
        ensure_dir(self.output_dir)
        
        logger.info("Excel导出器初始化完成")
    
    async def export_data(self, data_by_keyword: Dict[str, List[Dict[str, Any]]], filename: Optional[str] = None) -> str:
        """
        导出数据到Excel文件
        
        Args:
            data_by_keyword: 按关键词分组的商品数据
            filename: 可选的文件名
            
        Returns:
            str: 导出文件的路径
        """
        try:
            # 生成文件名
            if not filename:
                timestamp = get_timestamp_string()
                filename = self.filename_template.format(timestamp=timestamp)
            
            filename = sanitize_filename(filename)
            file_path = Path(self.output_dir) / filename
            
            # 创建工作簿
            workbook = Workbook()
            
            # 删除默认工作表
            if "Sheet" in workbook.sheetnames:
                workbook.remove(workbook["Sheet"])
            
            # 为每个关键词创建工作表
            for keyword, goods_list in data_by_keyword.items():
                if not goods_list:
                    continue
                
                sheet_name = self._generate_sheet_name(keyword)
                worksheet = workbook.create_sheet(title=sheet_name)
                
                # 导出数据到工作表
                await self._export_to_worksheet(worksheet, goods_list, keyword)
            
            # 如果没有数据，创建一个空的工作表
            if not workbook.worksheets:
                worksheet = workbook.create_sheet(title="无数据")
                worksheet["A1"] = "未找到商品数据"
            
            # 保存文件
            workbook.save(file_path)
            
            logger.info(f"数据导出完成: {file_path}")
            return str(file_path)
            
        except Exception as e:
            logger.error(f"导出Excel文件失败: {e}")
            raise

    async def export_data_with_sorting(self, grouped_data: Dict[str, Dict[str, List[Dict[str, Any]]]], filename: Optional[str] = None) -> str:
        """
        支持排序的数据导出
        
        Args:
            grouped_data: 按关键词和排序分组的数据
            格式: {keyword: {sort_type: [goods_list]}}
            filename: 可选的文件名
            
        Returns:
            str: 导出文件的路径
        """
        try:
            # 生成文件名
            if not filename:
                timestamp = get_timestamp_string()
                filename = self.filename_template.format(timestamp=timestamp)
            
            filename = sanitize_filename(filename)
            file_path = Path(self.output_dir) / filename
            
            # 创建工作簿
            workbook = Workbook()
            
            # 删除默认工作表
            if "Sheet" in workbook.sheetnames:
                workbook.remove(workbook["Sheet"])
            
            # 为每个关键词的每种排序创建工作表
            for keyword, sort_data in grouped_data.items():
                for sort_type, goods_list in sort_data.items():
                    if not goods_list:
                        continue
                    
                    # 生成工作表名称：关键词_排序类型
                    sheet_name = self._generate_sheet_name_with_sort(keyword, sort_type)
                    worksheet = workbook.create_sheet(title=sheet_name)
                    
                    # 导出数据到工作表
                    await self._export_to_worksheet(worksheet, goods_list, f"{keyword}_{sort_type}")
            
            # 如果没有数据，创建一个空的工作表
            if not workbook.worksheets:
                worksheet = workbook.create_sheet(title="无数据")
                worksheet["A1"] = "未找到商品数据"
            
            # 保存文件
            workbook.save(file_path)
            
            logger.info(f"支持排序的数据导出完成: {file_path}")
            return str(file_path)
            
        except Exception as e:
            logger.error(f"导出支持排序的Excel文件失败: {e}")
            raise
    
    def _generate_sheet_name_with_sort(self, keyword: str, sort_type: str) -> str:
        """
        生成包含排序信息的工作表名称
        
        Args:
            keyword: 搜索关键词
            sort_type: 排序类型
            
        Returns:
            str: 工作表名称
        """
        # 排序类型映射为中文
        sort_name_mapping = {
            "comprehensive": "综合排序",
            "price_asc": "价格从低到高", 
            "price_desc": "价格从高到低",
            "sales_desc": "销量从高到低",
            "sales_asc": "销量从低到高",
            "default": "默认排序"
        }
        
        sort_name = sort_name_mapping.get(sort_type, sort_type)
        sheet_name = f"{keyword}_{sort_name}"
        
        # Excel工作表名称限制
        invalid_chars = ['\\', '/', '*', '[', ']', ':', '?']
        for char in invalid_chars:
            sheet_name = sheet_name.replace(char, '_')
        
        if len(sheet_name) > 31:
            # 如果太长，截断关键词部分
            max_keyword_len = 31 - len(sort_name) - 1
            if max_keyword_len > 0:
                sheet_name = f"{keyword[:max_keyword_len]}_{sort_name}"
            else:
                sheet_name = sheet_name[:28] + "..."
        
        return sheet_name
    
    async def _export_to_worksheet(self, worksheet, goods_list: List[Dict[str, Any]], keyword: str) -> None:
        """
        导出数据到工作表 - 专业美观版
        
        Args:
            worksheet: 工作表对象
            goods_list: 商品数据列表
            keyword: 搜索关键词
        """
        if not goods_list:
            return
        
        # 过滤出有实际数据的字段
        available_fields = self._get_available_fields(goods_list)
        headers = [self.column_mapping[field] for field in available_fields if field in self.column_mapping]
        
        if not headers:
            return
        
        # 写入标题行 - 专业商务风格
        for col_idx, header in enumerate(headers, 1):
            cell = worksheet.cell(row=1, column=col_idx, value=header)
            cell.font = Font(bold=True, color="FFFFFF", size=12, name="微软雅黑")
            cell.fill = PatternFill(start_color="1F4E79", end_color="1F4E79", fill_type="solid")
            cell.alignment = Alignment(horizontal="center", vertical="center", wrap_text=True)
            cell.border = self._get_header_border()
        
        # 🎯 补贴数据统计（基于实际写入Excel的数据）
        subsidy_count = 0
        gov_subsidy_count = 0

        # 🔧 修复：基于最终写入Excel的subsidy_info字段进行统计
        for i, g in enumerate(goods_list):
            # 获取实际写入Excel的补贴详情字段
            subsidy_info = g.get("subsidy_info", "")

            # 确保subsidy_info是字符串类型
            if subsidy_info is None:
                subsidy_info = ""
            elif not isinstance(subsidy_info, str):
                subsidy_info = str(subsidy_info)

            # 基于补贴详情内容进行统计（与Excel文件中的实际数据一致）
            if subsidy_info and subsidy_info.strip():  # 非空的补贴信息
                if '百亿补贴' in subsidy_info:
                    subsidy_count += 1
                if '国补' in subsidy_info or '政府补贴' in subsidy_info:
                    gov_subsidy_count += 1

            # 前5个商品的详细调试信息
            if i < 5:
                # 同时显示原始字段和最终字段，便于调试
                is_subsidy = g.get("is_subsidy")
                is_gov_subsidy = g.get("is_government_subsidy")
                logger.debug(f"🔍 Excel统计调试 - 商品{i+1}: {g.get('goods_name', '')[:20]}... "
                           f"原始字段 is_subsidy={is_subsidy}, is_government_subsidy={is_gov_subsidy}, "
                           f"最终字段 subsidy_info='{subsidy_info}' (用于统计)")

        logger.info(f"📊 Excel导出统计 - {keyword}: 总商品数={len(goods_list)}, 百亿补贴={subsidy_count}, 国补={gov_subsidy_count}")
        
        # 写入数据行 - 优化格式和条件格式
        for row_idx, goods in enumerate(goods_list, 2):
            for col_idx, field_name in enumerate(available_fields, 1):
                if field_name not in self.column_mapping:
                    continue
                    
                value = self._format_cell_value(goods.get(field_name, ""), field_name)
                cell = worksheet.cell(row=row_idx, column=col_idx, value=value)
                cell.border = self._get_data_border()
                cell.font = Font(name="微软雅黑", size=10)
                
                # 设置特定列的格式和对齐
                if field_name in ["price", "original_price", "coupon_price", "market_price"]:
                    cell.alignment = Alignment(horizontal="right", vertical="center")
                    cell.number_format = '0.00"元"'
                    # 价格条件格式
                    self._apply_price_conditional_format(cell, value)
                elif field_name in ["sales", "comment_count"]:
                    cell.alignment = Alignment(horizontal="right", vertical="center")
                    if isinstance(value, (int, float)) and value >= 10000:
                        cell.number_format = '#,##0'
                    # 销量条件格式
                    self._apply_sales_conditional_format(cell, value, field_name)
                elif field_name == "rating":
                    cell.alignment = Alignment(horizontal="center", vertical="center")
                    # 评分条件格式
                    self._apply_rating_conditional_format(cell, value)
                elif field_name in ["goods_url", "image_url", "hd_thumb_url"]:
                    cell.font = Font(color="0066CC", underline="single", size=9, name="微软雅黑")
                    cell.alignment = Alignment(horizontal="left", vertical="center")
                elif field_name == "goods_name":
                    cell.alignment = Alignment(horizontal="left", vertical="center", wrap_text=True)
                    cell.font = Font(name="微软雅黑", size=10, bold=True)
                elif field_name == "marketing_tags":
                    cell.alignment = Alignment(horizontal="left", vertical="center", wrap_text=True)
                    # 营销标签条件格式
                    self._apply_marketing_conditional_format(cell, value)
                elif field_name == "brand_name":
                    cell.alignment = Alignment(horizontal="center", vertical="center")
                    cell.font = Font(name="微软雅黑", size=10, bold=True, color="2F5597")
                elif field_name in ["is_subsidy", "is_government_subsidy", "has_promotion_tags", "has_gov_subsidy"]:
                    cell.alignment = Alignment(horizontal="center", vertical="center")
                    # 🎯 调试日志：跟踪补贴字段的原始值
                    if row_idx <= 2:  # 只记录前两行避免日志过多
                        logger.debug(f"Excel导出 - 行{row_idx} {field_name}: 原始值={goods.get(field_name)} (类型: {type(goods.get(field_name)).__name__}), 格式化值={value}")
                    self._apply_subsidy_format(cell, value, field_name)
                elif field_name in ["subsidy_info", "promotion_text"]:
                    cell.alignment = Alignment(horizontal="left", vertical="center", wrap_text=True)
                    # 🎯 调试日志：跟踪补贴详情字段
                    if row_idx <= 2:  # 只记录前两行避免日志过多
                        logger.debug(f"Excel导出 - 行{row_idx} subsidy_info: 原始值={goods.get('subsidy_info')} (类型: {type(goods.get('subsidy_info')).__name__}), 格式化值={value}")
                    self._apply_subsidy_info_format(cell, value)
                else:
                    cell.alignment = Alignment(horizontal="left", vertical="center")
                
                # 交替行颜色 - 更柔和的配色
                if row_idx % 2 == 0:
                    cell.fill = PatternFill(start_color="F8F9FA", end_color="F8F9FA", fill_type="solid")
                else:
                    cell.fill = PatternFill(start_color="FFFFFF", end_color="FFFFFF", fill_type="solid")
        
        # 设置行高
        for row in range(1, len(goods_list) + 2):
            if row == 1:  # 标题行
                worksheet.row_dimensions[row].height = 25
            else:  # 数据行
                worksheet.row_dimensions[row].height = 20
        
        # 调整列宽 - 智能自适应
        self._adjust_column_widths_smart(worksheet, available_fields, goods_list)
        
        # 冻结首行和首列
        worksheet.freeze_panes = "B2"
        
        # 创建表格 - 使用专业样式
        if len(goods_list) > 0:
            table_range = f"A1:{get_column_letter(len(headers))}{len(goods_list) + 1}"
            table = Table(displayName=f"Table_{keyword.replace(' ', '_')}", ref=table_range)
            
            # 设置表格样式 - 专业商务风格
            style = TableStyleInfo(
                name="TableStyleMedium15",
                showFirstColumn=True,
                showLastColumn=False,
                showRowStripes=True,
                showColumnStripes=False
            )
            table.tableStyleInfo = style
            
            worksheet.add_table(table)
        
        # 添加专业统计信息和图表
        self._add_professional_statistics(worksheet, goods_list, keyword, len(headers))
    
    def _apply_price_conditional_format(self, cell, value):
        """应用价格条件格式"""
        if isinstance(value, (int, float)):
            if value >= 3000:
                # 高价商品 - 红色背景
                cell.fill = PatternFill(start_color="FFE6E6", end_color="FFE6E6", fill_type="solid")
                cell.font = Font(name="微软雅黑", size=10, color="CC0000", bold=True)
            elif value >= 1000:
                # 中价商品 - 橙色背景
                cell.fill = PatternFill(start_color="FFF2E6", end_color="FFF2E6", fill_type="solid")
                cell.font = Font(name="微软雅黑", size=10, color="FF6600")
            elif value <= 100:
                # 低价商品 - 绿色背景
                cell.fill = PatternFill(start_color="E6F7E6", end_color="E6F7E6", fill_type="solid")
                cell.font = Font(name="微软雅黑", size=10, color="009900")
    
    def _apply_sales_conditional_format(self, cell, value, field_name):
        """应用销量条件格式"""
        if isinstance(value, (int, float)):
            if field_name == "sales":
                if value >= 50000:
                    # 超高销量 - 金色背景
                    cell.fill = PatternFill(start_color="FFF9E6", end_color="FFF9E6", fill_type="solid")
                    cell.font = Font(name="微软雅黑", size=10, color="FF9900", bold=True)
                elif value >= 10000:
                    # 高销量 - 浅蓝色背景
                    cell.fill = PatternFill(start_color="E6F3FF", end_color="E6F3FF", fill_type="solid")
                    cell.font = Font(name="微软雅黑", size=10, color="0066CC")
            elif field_name == "comment_count":
                if value >= 5000:
                    # 高评论数 - 紫色背景
                    cell.fill = PatternFill(start_color="F0E6FF", end_color="F0E6FF", fill_type="solid")
                    cell.font = Font(name="微软雅黑", size=10, color="6600CC")
    
    def _apply_rating_conditional_format(self, cell, value):
        """应用评分条件格式"""
        try:
            rating = float(str(value).replace("分", ""))
            if rating >= 4.8:
                # 优秀评分 - 绿色背景
                cell.fill = PatternFill(start_color="E6F7E6", end_color="E6F7E6", fill_type="solid")
                cell.font = Font(name="微软雅黑", size=10, color="009900", bold=True)
            elif rating >= 4.5:
                # 良好评分 - 蓝色背景
                cell.fill = PatternFill(start_color="E6F3FF", end_color="E6F3FF", fill_type="solid")
                cell.font = Font(name="微软雅黑", size=10, color="0066CC")
            elif rating < 4.0:
                # 较低评分 - 黄色背景
                cell.fill = PatternFill(start_color="FFFACD", end_color="FFFACD", fill_type="solid")
                cell.font = Font(name="微软雅黑", size=10, color="FF6600")
        except:
            pass
    
    def _apply_marketing_conditional_format(self, cell, value):
        """应用营销标签条件格式"""
        if value and isinstance(value, str):
            if "百亿补贴" in value:
                # 百亿补贴 - 红色背景
                cell.fill = PatternFill(start_color="FFE6E6", end_color="FFE6E6", fill_type="solid")
                cell.font = Font(name="微软雅黑", size=9, color="CC0000", bold=True)
            elif any(tag in value for tag in ["限时", "秒杀", "抢购"]):
                # 限时活动 - 橙色背景
                cell.fill = PatternFill(start_color="FFF2E6", end_color="FFF2E6", fill_type="solid")
                cell.font = Font(name="微软雅黑", size=9, color="FF6600", bold=True)
            elif "会员" in value:
                # 会员专享 - 紫色背景
                cell.fill = PatternFill(start_color="F0E6FF", end_color="F0E6FF", fill_type="solid")
                cell.font = Font(name="微软雅黑", size=9, color="6600CC", bold=True)
            elif "新人" in value:
                # 新人优惠 - 绿色背景
                cell.fill = PatternFill(start_color="E6F7E6", end_color="E6F7E6", fill_type="solid")
                cell.font = Font(name="微软雅黑", size=9, color="009900", bold=True)

    def _apply_subsidy_format(self, cell, value, field_name):
        """应用补贴字段条件格式"""
        # 🎯 修复：支持多种True值格式
        if value is True or (isinstance(value, str) and value.lower() == "true") or (isinstance(value, (int, float)) and value == 1):
            if field_name in ["is_subsidy", "has_promotion_tags"]:
                # 百亿补贴 - 红色背景
                cell.fill = PatternFill(start_color="FFE6E6", end_color="FFE6E6", fill_type="solid")
                cell.font = Font(name="微软雅黑", size=9, color="CC0000", bold=True)
                cell.value = "是"
            elif field_name in ["is_government_subsidy", "has_gov_subsidy"]:
                # 国补商品 - 蓝色背景
                cell.fill = PatternFill(start_color="E6F3FF", end_color="E6F3FF", fill_type="solid")
                cell.font = Font(name="微软雅黑", size=9, color="0066CC", bold=True)
                cell.value = "是"
        else:
            # 非补贴商品显示空白
            cell.value = ""

    def _apply_subsidy_info_format(self, cell, value):
        """应用补贴详情条件格式"""
        if value and isinstance(value, str):
            if "百亿补贴" in value:
                # 包含百亿补贴 - 红色字体
                cell.font = Font(name="微软雅黑", size=9, color="CC0000", bold=True)
            elif "国补" in value:
                # 包含国补 - 蓝色字体
                cell.font = Font(name="微软雅黑", size=9, color="0066CC", bold=True)
            else:
                cell.font = Font(name="微软雅黑", size=9, color="666666")

    def _get_header_border(self) -> Border:
        """获取表头边框样式"""
        thick_border = Side(border_style="thick", color="1F4E79")
        return Border(
            left=thick_border,
            right=thick_border,
            top=thick_border,
            bottom=thick_border
        )
    
    def _get_data_border(self) -> Border:
        """获取数据边框样式"""
        thin_border = Side(border_style="thin", color="D0D0D0")
        return Border(
            left=thin_border,
            right=thin_border,
            top=thin_border,
            bottom=thin_border
        )

    def _get_available_fields(self, goods_list: List[Dict[str, Any]]) -> List[str]:
        """
        获取有实际数据的字段列表
        
        Args:
            goods_list: 商品数据列表
            
        Returns:
            List[str]: 有数据的字段列表，按重要性排序
        """
        if not goods_list:
            return []
        
        # 定义字段优先级顺序
        field_priority = [
            "goods_id", "goods_name", "keyword",
            "price", "original_price", "coupon_price", "market_price", "price_type_name",
            "sales", "comment_count", "rating", "sales_tip",
            "brand_name", "category",
            "subsidy_info",  # 补贴详情（已包含百亿补贴和国补信息）
            "activity_type_name", "merchant_type_name",
            "marketing_tags", "tags", "special_text",  # 营销标签
            "image_url", "hd_thumb_url",
            "goods_url",
            "created_time"
        ]
        
        # 统计每个字段的有效数据数量
        field_data_count = {}
        for field in self.column_mapping.keys():
            count = 0
            has_non_zero_value = False  # 是否有非零值
            
            for goods in goods_list:
                value = goods.get(field)
                
                # 特殊处理某些字段
                if field in ["comment_count", "rating"] and (value == 0 or value is None):
                    # 评论数和评分如果全是0，视为无数据
                    continue
                elif field == "category" and (value is None or str(value).strip() == ""):
                    # 分类如果全是空，视为无数据
                    continue
                elif field == "marketing_tags" and (value is None or str(value).strip() == ""):
                    # 营销标签如果全是空，视为无数据
                    continue
                elif field == "special_text" and (value is None or str(value).strip() == ""):
                    # 特殊信息如果全是空，视为无数据
                    continue
                    
                # 正常判断逻辑
                if value is not None and str(value).strip():
                    if field in ["comment_count", "rating"]:
                        # 对于数值字段，检查是否有非零值
                        if value != 0:
                            has_non_zero_value = True
                            count += 1
                    elif str(value).strip() not in ["", "0", "0.0", "暂无", "无", "None"]:
                        count += 1
                        
            field_data_count[field] = count
            
            # 对于数值字段，如果没有非零值，设为0
            if field in ["comment_count", "rating"] and not has_non_zero_value:
                field_data_count[field] = 0
        
        # 过滤出有数据的字段
        # 更严格的过滤：必须至少有1个有效数据才显示
        available_fields = [field for field, count in field_data_count.items() if count >= 1]
        
        # 按优先级排序
        sorted_fields = []
        for field in field_priority:
            if field in available_fields:
                sorted_fields.append(field)
        
        # 添加其他有数据但不在优先级列表中的字段
        for field in available_fields:
            if field not in sorted_fields:
                sorted_fields.append(field)
        
        return sorted_fields
    
    def _adjust_column_widths_smart(self, worksheet, available_fields: List[str], goods_list: List[Dict[str, Any]]) -> None:
        """
        智能调整列宽
        
        Args:
            worksheet: 工作表对象
            available_fields: 可用字段列表
            goods_list: 商品数据列表
        """
        # 基础列宽配置
        base_widths = {
            "goods_id": 12,
            "goods_name": 45,
            "keyword": 15,
            "price": 12,
            "original_price": 12,
            "coupon_price": 12,
            "market_price": 12,
            "price_type_name": 15,
            "sales": 12,
            "comment_count": 12,
            "rating": 8,
            "sales_tip": 20,
            "brand_name": 15,
            "category": 18,
            "activity_type_name": 15,
            "merchant_type_name": 15,
            "marketing_tags": 30,  # 营销标签字段宽度
            "tags": 25,
            "special_text": 20,
            "image_url": 35,
            "hd_thumb_url": 35,
            "goods_url": 40,
            "created_time": 18
        }
        
        for col_idx, field_name in enumerate(available_fields, 1):
            # 获取基础宽度
            base_width = base_widths.get(field_name, 15)
            
            # 根据实际数据内容调整宽度
            if goods_list and field_name in ["goods_name", "marketing_tags", "tags", "special_text"]:
                max_length = 0
                for goods in goods_list[:10]:  # 只检查前10个商品
                    value = str(goods.get(field_name, ""))
                    if len(value) > max_length:
                        max_length = len(value)

                # 根据内容长度调整宽度
                if field_name == "goods_name":
                    width = min(max(base_width, max_length * 0.8), 60)
                elif field_name == "marketing_tags":
                    width = min(max(base_width, max_length * 0.7), 40)  # 营销标签适中宽度
                else:
                    width = min(max(base_width, max_length * 0.6), 35)
            else:
                width = base_width
            
            worksheet.column_dimensions[get_column_letter(col_idx)].width = width
    
    def _add_professional_statistics(self, worksheet, goods_list: List[Dict[str, Any]], keyword: str, num_columns: int) -> None:
        """
        添加专业统计信息和可视化分析
        
        Args:
            worksheet: 工作表对象
            goods_list: 商品数据列表
            keyword: 搜索关键词
            num_columns: 列数
        """
        if not goods_list:
            return
        
        # 在数据下方添加统计信息
        start_row = len(goods_list) + 4
        
        # 统计标题 - 专业设计
        title_cell = worksheet.cell(row=start_row, column=1, value="📊 数据分析报告")
        title_cell.font = Font(bold=True, size=16, color="1F4E79", name="微软雅黑")
        title_cell.fill = PatternFill(start_color="E8F1FF", end_color="E8F1FF", fill_type="solid")
        title_cell.alignment = Alignment(horizontal="center", vertical="center")
        title_cell.border = self._get_header_border()
        
        # 合并标题单元格
        if num_columns > 1:
            worksheet.merge_cells(f"A{start_row}:{get_column_letter(num_columns)}{start_row}")
        
        # 基本统计信息
        current_row = start_row + 2
        
        # 第一部分：基本信息
        basic_stats = [
            ("🔍 搜索关键词", keyword),
            ("📦 商品总数", f"{len(goods_list):,} 个"),
            ("⏰ 分析时间", datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
        ]
        
        current_row = self._add_stats_section(worksheet, "基本信息", basic_stats, current_row, num_columns)
        
        # 第二部分：价格分析
        price_stats = self._calculate_price_statistics(goods_list)
        if price_stats:
            current_row = self._add_stats_section(worksheet, "价格分析", price_stats, current_row, num_columns)
        
        # 第三部分：销量分析
        sales_stats = self._calculate_sales_statistics(goods_list)
        if sales_stats:
            current_row = self._add_stats_section(worksheet, "销量分析", sales_stats, current_row, num_columns)
        
        # 第四部分：品牌分析
        brand_stats = self._calculate_brand_statistics(goods_list)
        if brand_stats:
            current_row = self._add_stats_section(worksheet, "品牌分析", brand_stats, current_row, num_columns)
        
        # 第五部分：营销分析
        marketing_stats = self._calculate_marketing_statistics(goods_list)
        if marketing_stats:
            current_row = self._add_stats_section(worksheet, "营销分析", marketing_stats, current_row, num_columns)
    
    def _add_stats_section(self, worksheet, section_title: str, stats: List[tuple], start_row: int, num_columns: int) -> int:
        """添加统计信息部分"""
        # 部分标题
        section_cell = worksheet.cell(row=start_row, column=1, value=f"📈 {section_title}")
        section_cell.font = Font(bold=True, size=12, color="2F5597", name="微软雅黑")
        section_cell.fill = PatternFill(start_color="F0F4FF", end_color="F0F4FF", fill_type="solid")
        section_cell.alignment = Alignment(horizontal="left", vertical="center")
        section_cell.border = self._get_data_border()
        
        # 合并部分标题
        if num_columns > 2:
            worksheet.merge_cells(f"A{start_row}:{get_column_letter(min(3, num_columns))}{start_row}")
        
        current_row = start_row + 1
        
        # 写入统计数据
        for i, (label, value) in enumerate(stats):
            row = current_row + i
            
            # 标签列
            label_cell = worksheet.cell(row=row, column=1, value=label)
            label_cell.font = Font(bold=True, size=10, name="微软雅黑")
            label_cell.fill = PatternFill(start_color="F8F9FA", end_color="F8F9FA", fill_type="solid")
            label_cell.alignment = Alignment(horizontal="left", vertical="center")
            label_cell.border = self._get_data_border()
            
            # 数值列
            value_cell = worksheet.cell(row=row, column=2, value=value)
            value_cell.font = Font(size=10, name="微软雅黑")
            value_cell.alignment = Alignment(horizontal="left", vertical="center")
            value_cell.border = self._get_data_border()
            
            # 根据数值类型设置格式
            if isinstance(value, str) and any(char in value for char in ["元", "%", "个", "万"]):
                value_cell.font = Font(size=10, name="微软雅黑", bold=True, color="2F5597")
        
        return current_row + len(stats) + 1
    
    def _calculate_price_statistics(self, goods_list: List[Dict[str, Any]]) -> List[tuple]:
        """计算价格统计信息"""
        prices = []
        for goods in goods_list:
            for price_field in ["price", "original_price", "coupon_price", "market_price"]:
                price_value = goods.get(price_field)
                if price_value:
                    try:
                        if isinstance(price_value, str):
                            import re
                            numbers = re.findall(r'\d+\.?\d*', price_value)
                            if numbers:
                                prices.append(float(numbers[0]))
                        elif isinstance(price_value, (int, float)):
                            prices.append(float(price_value))
                    except:
                        continue
                    break  # 只取第一个有效价格
        
        if not prices:
            return []
        
        avg_price = sum(prices) / len(prices)
        min_price = min(prices)
        max_price = max(prices)
        
        # 价格区间分布
        low_price_count = sum(1 for p in prices if p <= 500)
        mid_price_count = sum(1 for p in prices if 500 < p <= 2000)
        high_price_count = sum(1 for p in prices if p > 2000)
        
        return [
            ("💰 平均价格", f"{avg_price:.2f} 元"),
            ("💸 最低价格", f"{min_price:.2f} 元"),
            ("💎 最高价格", f"{max_price:.2f} 元"),
            ("📊 价格范围", f"{min_price:.2f} - {max_price:.2f} 元"),
            ("🔸 低价商品(≤500元)", f"{low_price_count} 个 ({low_price_count/len(prices)*100:.1f}%)"),
            ("🔸 中价商品(500-2000元)", f"{mid_price_count} 个 ({mid_price_count/len(prices)*100:.1f}%)"),
            ("🔸 高价商品(>2000元)", f"{high_price_count} 个 ({high_price_count/len(prices)*100:.1f}%)")
        ]
    
    def _calculate_sales_statistics(self, goods_list: List[Dict[str, Any]]) -> List[tuple]:
        """计算销量统计信息"""
        sales_list = []
        for goods in goods_list:
            sales_value = goods.get("sales")
            if sales_value:
                try:
                    if isinstance(sales_value, str):
                        import re
                        if "万" in sales_value:
                            numbers = re.findall(r'(\d+\.?\d*)\s*万', sales_value)
                            if numbers:
                                sales_list.append(int(float(numbers[0]) * 10000))
                        else:
                            numbers = re.findall(r'\d+', sales_value)
                            if numbers:
                                sales_list.append(int(numbers[0]))
                    elif isinstance(sales_value, (int, float)):
                        sales_list.append(int(sales_value))
                except:
                    continue
        
        if not sales_list:
            return []
        
        avg_sales = sum(sales_list) / len(sales_list)
        min_sales = min(sales_list)
        max_sales = max(sales_list)
        total_sales = sum(sales_list)
        
        # 销量区间分布
        low_sales_count = sum(1 for s in sales_list if s <= 1000)
        mid_sales_count = sum(1 for s in sales_list if 1000 < s <= 10000)
        high_sales_count = sum(1 for s in sales_list if s > 10000)
        
        return [
            ("📈 平均销量", f"{avg_sales:,.0f} 件"),
            ("📉 最低销量", f"{min_sales:,} 件"),
            ("🔥 最高销量", f"{max_sales:,} 件"),
            ("📊 总销量", f"{total_sales:,} 件"),
            ("🔸 低销量(≤1000件)", f"{low_sales_count} 个 ({low_sales_count/len(sales_list)*100:.1f}%)"),
            ("🔸 中销量(1000-10000件)", f"{mid_sales_count} 个 ({mid_sales_count/len(sales_list)*100:.1f}%)"),
            ("🔸 高销量(>10000件)", f"{high_sales_count} 个 ({high_sales_count/len(sales_list)*100:.1f}%)")
        ]
    
    def _calculate_brand_statistics(self, goods_list: List[Dict[str, Any]]) -> List[tuple]:
        """计算品牌统计信息"""
        brands = {}
        main_brands = {}
        
        for goods in goods_list:
            brand_name = goods.get("brand_name", "")
            if brand_name and brand_name.strip():
                brand_name = brand_name.strip()
                brands[brand_name] = brands.get(brand_name, 0) + 1
                
                # 提取主品牌
                if "(" in brand_name and ")" in brand_name:
                    main_brand = brand_name.split("(")[1].split(")")[0]
                else:
                    main_brand = brand_name
                main_brands[main_brand] = main_brands.get(main_brand, 0) + 1
        
        if not brands:
            return []
        
        # 排序品牌
        top_brands = sorted(brands.items(), key=lambda x: x[1], reverse=True)[:5]
        top_main_brands = sorted(main_brands.items(), key=lambda x: x[1], reverse=True)[:3]
        
        stats = [
            ("🏷️ 品牌总数", f"{len(brands)} 个"),
            ("🏢 主品牌数", f"{len(main_brands)} 个"),
            ("📊 品牌覆盖率", f"{len([g for g in goods_list if g.get('brand_name')])/len(goods_list)*100:.1f}%")
        ]
        
        # 添加热门品牌
        for i, (brand, count) in enumerate(top_brands, 1):
            stats.append((f"🔸 热门品牌{i}", f"{brand}: {count} 个"))
        
        return stats
    
    def _calculate_marketing_statistics(self, goods_list: List[Dict[str, Any]]) -> List[tuple]:
        """计算营销统计信息"""
        marketing_tags = {}
        products_with_marketing = 0
        
        for goods in goods_list:
            tags = goods.get("marketing_tags", "")
            if tags and isinstance(tags, str):
                products_with_marketing += 1
                for tag in tags.split(","):
                    tag = tag.strip()
                    if tag:
                        marketing_tags[tag] = marketing_tags.get(tag, 0) + 1
        
        if not marketing_tags:
            return []
        
        # 排序营销标签
        top_marketing = sorted(marketing_tags.items(), key=lambda x: x[1], reverse=True)[:5]
        
        stats = [
            ("🎯 营销覆盖率", f"{products_with_marketing/len(goods_list)*100:.1f}%"),
            ("🏷️ 营销标签数", f"{len(marketing_tags)} 种"),
            ("📦 有营销商品", f"{products_with_marketing} 个")
        ]
        
        # 添加热门营销标签
        for i, (tag, count) in enumerate(top_marketing, 1):
            stats.append((f"🔸 热门标签{i}", f"{tag}: {count} 个"))
        
        return stats
    
    def _get_thin_border(self) -> Border:
        """获取细边框样式"""
        thin_border = Side(border_style="thin", color="CCCCCC")
        return Border(
            left=thin_border,
            right=thin_border,
            top=thin_border,
            bottom=thin_border
        )
    
    def _get_column_headers(self) -> List[str]:
        """获取列标题"""
        return list(self.column_mapping.values())
    
    def _format_cell_value(self, value: Any, field_name: str) -> Any:
        """
        格式化单元格值 - 优化版
        
        Args:
            value: 原始值
            field_name: 字段名
            
        Returns:
            格式化后的值
        """
        if value is None or value == "":
            return ""
        
        # 价格格式化
        if field_name in ["price", "original_price", "coupon_price", "market_price"]:
            try:
                if isinstance(value, str):
                    # 提取数字
                    import re
                    numbers = re.findall(r'\d+\.?\d*', value)
                    if numbers:
                        price = float(numbers[0])
                        return price if price > 0 else ""
                elif isinstance(value, (int, float)):
                    price = float(value)
                    return price if price > 0 else ""
            except:
                pass
            return ""
        
        # 销量格式化
        elif field_name in ["sales", "comment_count"]:
            try:
                if isinstance(value, str):
                    # 提取数字，支持万、千等单位
                    import re
                    if "万" in value:
                        numbers = re.findall(r'(\d+\.?\d*)\s*万', value)
                        if numbers:
                            return int(float(numbers[0]) * 10000)
                    elif "千" in value:
                        numbers = re.findall(r'(\d+\.?\d*)\s*千', value)
                        if numbers:
                            return int(float(numbers[0]) * 1000)
                    else:
                        numbers = re.findall(r'\d+', value)
                        if numbers:
                            return int(numbers[0])
                elif isinstance(value, (int, float)):
                    return int(value)
            except:
                pass
            return 0
        
        # 评分格式化
        elif field_name == "rating":
            try:
                if isinstance(value, str):
                    import re
                    numbers = re.findall(r'\d+\.?\d*', value)
                    if numbers:
                        rating = float(numbers[0])
                        if rating > 5:
                            rating = rating / 10
                        return f"{rating:.1f}" if rating > 0 else ""
                elif isinstance(value, (int, float)):
                    rating = float(value)
                    if rating > 5:
                        rating = rating / 10
                    return f"{rating:.1f}" if rating > 0 else ""
            except:
                pass
            return ""
        
        # 商品名称格式化
        elif field_name == "goods_name":
            if isinstance(value, str):
                # 移除HTML标签
                import re
                name = re.sub(r'<[^>]+>', '', value)
                # 移除多余空格
                name = re.sub(r'\s+', ' ', name).strip()
                # 限制长度
                if len(name) > 80:
                    name = name[:77] + "..."
                return name
        
        # URL字段格式化
        elif field_name in ["goods_url", "image_url", "hd_thumb_url"]:
            url_str = str(value).strip()
            if url_str and len(url_str) > 10:
                # 确保URL格式正确
                if not url_str.startswith(('http://', 'https://')):
                    if url_str.startswith('//'):
                        url_str = 'https:' + url_str
                return url_str
            return ""
        
        # 标签格式化
        elif field_name == "tags":
            if isinstance(value, str):
                # 清理标签，移除重复
                tags = [tag.strip() for tag in value.split(',') if tag.strip()]
                unique_tags = []
                for tag in tags:
                    if tag not in unique_tags and len(tag) > 0:
                        unique_tags.append(tag)
                return ', '.join(unique_tags[:5])  # 最多显示5个标签
            return ""
        
        # 品牌名称格式化
        elif field_name == "brand_name":
            brand_str = str(value).strip()
            if brand_str and not brand_str.startswith(("品牌_", "品牌ID", "未知")):
                return brand_str
            return ""
        
        # 🎯 补贴布尔字段格式化
        elif field_name in ["is_subsidy", "is_government_subsidy", "has_promotion_tags", "has_gov_subsidy"]:
            # 使用专门的补贴字段格式化方法
            return self.format_subsidy_field(value, field_name)
        
        # 🎯 补贴详情字段特殊处理
        elif field_name in ["subsidy_info", "promotion_text"]:
            # 补贴详情字段：显示具体的补贴信息
            text_str = str(value).strip()
            if text_str and text_str not in ["True", "False", "None", ""]:
                return text_str
            return ""

        # 其他文本字段
        elif field_name in ["category", "sales_tip", "special_text", "activity_type_name", "merchant_type_name", "price_type_name", "marketing_tags"]:
            text_str = str(value).strip()
            if text_str and len(text_str) > 0:
                # 限制长度
                if len(text_str) > 50:
                    text_str = text_str[:47] + "..."
                return text_str
            return ""
        
        # 默认处理
        return str(value).strip() if value else ""

    def format_subsidy_field(self, value, field_name: str = "") -> str:
        """
        🎯 增强的百亿补贴字段格式化 - 支持多种数据类型和安全转换

        Args:
            value: 需要格式化的值
            field_name: 字段名称（用于调试）

        Returns:
            str: 格式化后的字符串（"是" 或 ""）
        """
        try:
            # 调试日志（仅在前几条记录中记录）
            if hasattr(self, '_debug_count'):
                self._debug_count += 1
            else:
                self._debug_count = 1

            if self._debug_count <= 10:  # 记录前10条便于调试
                logger.debug(f"补贴字段格式化 - {field_name}: 原始值={repr(value)} (类型: {type(value).__name__})")

            # None值处理
            if value is None:
                return ""

            # 布尔值处理
            if isinstance(value, bool):
                result = "是" if value else ""
                if self._debug_count <= 10:
                    logger.debug(f"布尔值处理: {value} -> {result}")
                return result

            # 字符串处理
            elif isinstance(value, str):
                value_lower = value.lower().strip()
                if value_lower in ['true', '1', 'yes', '是', 'on', 'enabled', '百亿补贴']:
                    if self._debug_count <= 5:
                        logger.debug(f"字符串处理: {value} -> 是")
                    return "是"
                elif value_lower in ['false', '0', 'no', '否', 'off', 'disabled', '', 'none']:
                    return ""
                else:
                    # 检查是否包含补贴相关关键词
                    if any(keyword in value_lower for keyword in ['补贴', 'subsidy', 'billion']):
                        if self._debug_count <= 5:
                            logger.debug(f"关键词匹配: {value} -> 是")
                        return "是"
                    return ""

            # 数字处理
            elif isinstance(value, (int, float)):
                result = "是" if value == 1 or value > 0 else ""
                if self._debug_count <= 5:
                    logger.debug(f"数字处理: {value} -> {result}")
                return result

            # 列表/字典处理（可能来自API响应）
            elif isinstance(value, (list, dict)):
                # 如果是列表且包含20001，认为是百亿补贴
                if isinstance(value, list) and 20001 in value:
                    if self._debug_count <= 5:
                        logger.debug(f"列表处理: {value} -> 是")
                    return "是"
                # 如果是字典且包含相关字段
                elif isinstance(value, dict):
                    for key, val in value.items():
                        if 'subsidy' in str(key).lower() or 'billion' in str(key).lower():
                            if val:
                                if self._debug_count <= 5:
                                    logger.debug(f"字典处理: {value} -> 是")
                                return "是"
                return ""

            # 其他类型转换为字符串处理
            else:
                str_value = str(value).lower().strip()
                if str_value in ['true', '1', 'yes', '是']:
                    if self._debug_count <= 5:
                        logger.debug(f"其他类型处理: {value} -> 是")
                    return "是"
                return ""

        except Exception as e:
            logger.debug(f"补贴字段格式化异常: {e}, 值: {value}, 类型: {type(value)}")
            return ""

    def _get_border(self) -> Border:
        """获取边框样式"""
        thin_border = Side(border_style="thin", color="000000")
        return Border(
            left=thin_border,
            right=thin_border,
            top=thin_border,
            bottom=thin_border
        )
    
    def _adjust_column_widths(self, worksheet) -> None:
        """调整列宽"""
        # 预设列宽
        column_widths = {
            "goods_id": 15,
            "goods_name": 40,
            "price": 12,
            "original_price": 12,
            "sales": 15,
            "shop_name": 25,
            "goods_url": 50,
            "image_url": 50,
            "category": 20,
            "tags": 30,
            "rating": 10,
            "comment_count": 15,
            "created_time": 20
        }
        
        for col_idx, (field_name, _) in enumerate(self.column_mapping.items(), 1):
            width = column_widths.get(field_name, 15)
            worksheet.column_dimensions[get_column_letter(col_idx)].width = width
    
    def _add_statistics(self, worksheet, goods_list: List[Dict[str, Any]], keyword: str) -> None:
        """
        添加统计信息
        
        Args:
            worksheet: 工作表对象
            goods_list: 商品数据列表
            keyword: 搜索关键词
        """
        if not goods_list:
            return
        
        # 在数据下方添加统计信息
        start_row = len(goods_list) + 3
        
        # 统计标题
        worksheet.cell(row=start_row, column=1, value="数据统计").font = Font(bold=True, size=14)
        
        # 基本统计
        stats = [
            ("搜索关键词", keyword),
            ("商品总数", len(goods_list)),
            ("采集时间", datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
        ]
        
        # 价格统计
        prices = []
        for goods in goods_list:
            price_str = goods.get("price", "0")
            try:
                if isinstance(price_str, str):
                    import re
                    numbers = re.findall(r'\d+\.?\d*', price_str)
                    if numbers:
                        prices.append(float(numbers[0]))
                elif isinstance(price_str, (int, float)):
                    prices.append(float(price_str))
            except:
                continue
        
        if prices:
            stats.extend([
                ("平均价格", f"{sum(prices) / len(prices):.2f}元"),
                ("最低价格", f"{min(prices):.2f}元"),
                ("最高价格", f"{max(prices):.2f}元")
            ])
        
        # 写入统计信息
        for i, (label, value) in enumerate(stats):
            worksheet.cell(row=start_row + 1 + i, column=1, value=label).font = Font(bold=True)
            worksheet.cell(row=start_row + 1 + i, column=2, value=value)
    
    def _generate_sheet_name(self, keyword: str) -> str:
        """
        生成工作表名称
        
        Args:
            keyword: 搜索关键词
            
        Returns:
            str: 工作表名称
        """
        sheet_name = self.sheet_name_template.format(keyword=keyword)
        
        # Excel工作表名称限制
        # 长度不超过31个字符，不能包含特殊字符
        invalid_chars = ['\\', '/', '*', '[', ']', ':', '?']
        for char in invalid_chars:
            sheet_name = sheet_name.replace(char, '_')
        
        if len(sheet_name) > 31:
            sheet_name = sheet_name[:28] + "..."
        
        return sheet_name
    
    def get_export_summary(self, file_path: str, data_by_keyword: Dict[str, List[Dict[str, Any]]]) -> Dict[str, Any]:
        """
        获取导出摘要信息
        
        Args:
            file_path: 导出文件路径
            data_by_keyword: 按关键词分组的数据
            
        Returns:
            Dict[str, Any]: 导出摘要
        """
        total_goods = sum(len(goods_list) for goods_list in data_by_keyword.values())
        
        summary = {
            "file_path": file_path,
            "total_keywords": len(data_by_keyword),
            "total_goods": total_goods,
            "keywords": list(data_by_keyword.keys()),
            "goods_by_keyword": {k: len(v) for k, v in data_by_keyword.items()},
            "export_time": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        # 文件大小
        try:
            file_size = Path(file_path).stat().st_size
            summary["file_size"] = file_size
            summary["file_size_mb"] = round(file_size / 1024 / 1024, 2)
        except:
            pass
        
        return summary
    
    async def export_to_csv(self, data_by_keyword: Dict[str, List[Dict[str, Any]]], filename: Optional[str] = None) -> str:
        """
        导出数据到CSV文件
        
        Args:
            data_by_keyword: 按关键词分组的商品数据
            filename: 可选的文件名
            
        Returns:
            str: 导出文件的路径
        """
        try:
            # 生成文件名（使用.csv扩展名）
            if not filename:
                timestamp = get_timestamp_string()
                filename = self.filename_template.format(timestamp=timestamp)
                # 替换扩展名为.csv
                filename = filename.replace('.xlsx', '.csv')
            
            # 确保文件名以.csv结尾
            if not filename.endswith('.csv'):
                filename = filename.replace('.xlsx', '.csv')
                if not filename.endswith('.csv'):
                    filename += '.csv'
            
            # 安全的文件名
            safe_filename = sanitize_filename(filename)
            file_path = Path(self.output_dir) / safe_filename
            
            logger.info(f"开始导出CSV文件: {file_path}")
            
            # 合并所有关键词的数据
            all_data = []
            for keyword, goods_list in data_by_keyword.items():
                for goods in goods_list:
                    # 确保每个商品都有keyword字段
                    goods['keyword'] = keyword
                    all_data.append(goods)
            
            if not all_data:
                logger.warning("没有数据可导出")
                return str(file_path)
            
            # 转换为DataFrame
            df = pd.DataFrame(all_data)
            
            # 根据column_mapping重命名列
            rename_dict = {}
            for field_name, display_name in self.column_mapping.items():
                if field_name in df.columns:
                    rename_dict[field_name] = display_name
            
            if rename_dict:
                df = df.rename(columns=rename_dict)
            
            # 按照优先级排序列
            priority_columns = [
                '商品ID', '商品名称', '搜索关键词',
                '拼团价(元)', '原价(元)', '券后价(元)', '市场价(元)',
                '销量', '评论数', '品牌名称',
                '补贴详情',  # 已包含百亿补贴和国补信息
                '活动类型', '商家类型', '营销标签',
                '商品标签', '特殊信息',
                '商品图片', '高清图片', '商品链接', '采集时间'
            ]
            
            # 获取实际存在的列
            existing_columns = []
            for col in priority_columns:
                if col in df.columns:
                    existing_columns.append(col)
            
            # 添加其他列
            for col in df.columns:
                if col not in existing_columns:
                    existing_columns.append(col)
            
            # 重新排序DataFrame
            df = df[existing_columns]
            
            # 格式化数据
            for col in df.columns:
                if col in ['拼团价(元)', '原价(元)', '券后价(元)', '市场价(元)']:
                    # 确保价格字段为数值
                    df[col] = pd.to_numeric(df[col], errors='coerce')
            
            # 导出到CSV
            df.to_csv(file_path, index=False, encoding='utf-8-sig')
            
            logger.success(f"CSV导出成功: {file_path}")
            logger.info(f"导出数据条数: {len(df)}")
            
            return str(file_path)
            
        except Exception as e:
            logger.error(f"CSV导出失败: {e}")
            import traceback
            traceback.print_exc()
            raise
