import React, { useState, useEffect, useCallback } from 'react';
import { Card, Row, Col, Progress, Badge, Statistic, Alert, Space, Button } from 'antd';
import { ReloadOutlined, CheckCircleOutlined, ExclamationCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';
import { apiService } from '@/services/api';

interface SystemHealth {
  status: 'healthy' | 'warning' | 'error';
  api: {
    status: 'online' | 'offline';
    responseTime: number;
    lastCheck: string;
  };
  websocket: {
    status: 'connected' | 'disconnected' | 'connecting';
    lastMessage: string;
  };
  performance: {
    memory: number;
    cpu: number;
    network: number;
  };
  services: {
    crawler: 'running' | 'stopped' | 'error';
    database: 'connected' | 'disconnected';
    storage: 'available' | 'full' | 'error';
  };
}

const SystemMonitor: React.FC = () => {
  const [health, setHealth] = useState<SystemHealth>({
    status: 'healthy',
    api: {
      status: 'offline',
      responseTime: 0,
      lastCheck: '',
    },
    websocket: {
      status: 'disconnected',
      lastMessage: '',
    },
    performance: {
      memory: 0,
      cpu: 0,
      network: 0,
    },
    services: {
      crawler: 'stopped',
      database: 'disconnected',
      storage: 'available',
    },
  });

  const [loading, setLoading] = useState(false);

  // 检查API状态
  const checkApiHealth = useCallback(async () => {
    try {
      const startTime = Date.now();
      const response = await apiService.healthCheck();
      const responseTime = Date.now() - startTime;

      setHealth(prev => ({
        ...prev,
        api: {
          status: response.success ? 'online' : 'offline',
          responseTime,
          lastCheck: new Date().toLocaleTimeString(),
        },
      }));

      return response.success;
    } catch (error) {
      setHealth(prev => ({
        ...prev,
        api: {
          status: 'offline',
          responseTime: 0,
          lastCheck: new Date().toLocaleTimeString(),
        },
      }));
      return false;
    }
  }, []);

  // 检查WebSocket状态
  const checkWebSocketHealth = useCallback(() => {
    // 这里可以从WebSocket hook获取状态
    const wsStatus = 'connected'; // 暂时写死，实际应该从useWebSocket获取
    setHealth(prev => ({
      ...prev,
      websocket: {
        status: wsStatus as any,
        lastMessage: new Date().toLocaleTimeString(),
      },
    }));
  }, []);

  // 模拟性能监控
  const updatePerformanceMetrics = useCallback(() => {
    // 在实际项目中，这些数据应该从后端API获取
    setHealth(prev => ({
      ...prev,
      performance: {
        memory: Math.floor(Math.random() * 30) + 50, // 50-80%
        cpu: Math.floor(Math.random() * 20) + 10,    // 10-30%
        network: Math.floor(Math.random() * 40) + 30, // 30-70%
      },
    }));
  }, []);

  // 检查服务状态
  const checkServicesHealth = useCallback(async () => {
    try {
      // 检查爬虫状态
      const crawlStatus = await apiService.getCrawlStatus();
      const crawlerStatus = crawlStatus.success && crawlStatus.data?.status === 'running' ? 'running' : 'stopped';

      setHealth(prev => ({
        ...prev,
        services: {
          crawler: crawlerStatus,
          database: 'connected', // 暂时写死
          storage: 'available',  // 暂时写死
        },
      }));
    } catch (error) {
      setHealth(prev => ({
        ...prev,
        services: {
          crawler: 'error',
          database: 'disconnected',
          storage: 'error',
        },
      }));
    }
  }, []);

  // 计算整体健康状态
  const calculateOverallHealth = useCallback(() => {
    const { api, websocket, services, performance } = health;
    
    // 检查关键服务
    if (api.status === 'offline') {
      return 'error';
    }

    // 检查性能指标
    if (performance.memory > 90 || performance.cpu > 80) {
      return 'warning';
    }

    // 检查服务状态
    if (services.database === 'disconnected' || services.storage === 'error') {
      return 'warning';
    }

    return 'healthy';
  }, [health]);

  // 手动刷新
  const handleRefresh = async () => {
    setLoading(true);
    try {
      await Promise.all([
        checkApiHealth(),
        checkWebSocketHealth(),
        checkServicesHealth(),
        updatePerformanceMetrics(),
      ]);
    } finally {
      setLoading(false);
    }
  };

  // 定期更新
  useEffect(() => {
    const interval = setInterval(() => {
      checkApiHealth();
      checkWebSocketHealth();
      checkServicesHealth();
      updatePerformanceMetrics();
    }, 30000); // 30秒更新一次

    // 初始化检查
    handleRefresh();

    return () => clearInterval(interval);
  }, [checkApiHealth, checkWebSocketHealth, checkServicesHealth, updatePerformanceMetrics]);

  // 更新整体状态
  useEffect(() => {
    const overallStatus = calculateOverallHealth();
    setHealth(prev => ({
      ...prev,
      status: overallStatus,
    }));
  }, [calculateOverallHealth, health.api, health.performance, health.services]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
      case 'online':
      case 'connected':
      case 'running':
      case 'available':
        return 'success';
      case 'warning':
      case 'connecting':
        return 'warning';
      case 'error':
      case 'offline':
      case 'disconnected':
      case 'stopped':
      case 'full':
        return 'error';
      default:
        return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (getStatusColor(status)) {
      case 'success':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'warning':
        return <ExclamationCircleOutlined style={{ color: '#faad14' }} />;
      case 'error':
        return <CloseCircleOutlined style={{ color: '#f5222d' }} />;
      default:
        return null;
    }
  };

  return (
    <div style={{ padding: '16px 0' }}>
      {/* 系统状态总览 */}
      <Card 
        title={
          <Space>
            <span>系统状态监控</span>
            <Badge 
              status={getStatusColor(health.status) as any}
              text={health.status === 'healthy' ? '正常' : health.status === 'warning' ? '警告' : '异常'}
            />
          </Space>
        }
        extra={
          <Button 
            icon={<ReloadOutlined />} 
            onClick={handleRefresh}
            loading={loading}
            size="small"
          >
            刷新
          </Button>
        }
        size="small"
      >
        {/* 整体状态提示 */}
        {health.status !== 'healthy' && (
          <Alert
            message={health.status === 'warning' ? '系统运行正常但有警告' : '系统存在严重问题'}
            type={health.status === 'warning' ? 'warning' : 'error'}
            showIcon
            style={{ marginBottom: 16 }}
          />
        )}

        <Row gutter={[16, 16]}>
          {/* API状态 */}
          <Col xs={24} sm={12} md={6}>
            <Card size="small" bordered={false}>
              <Statistic
                title="API服务"
                value={health.api.status === 'online' ? '在线' : '离线'}
                prefix={getStatusIcon(health.api.status)}
                suffix={health.api.status === 'online' ? `${health.api.responseTime}ms` : ''}
                valueStyle={{ fontSize: '14px' }}
              />
              <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
                最后检查: {health.api.lastCheck}
              </div>
            </Card>
          </Col>

          {/* WebSocket状态 */}
          <Col xs={24} sm={12} md={6}>
            <Card size="small" bordered={false}>
              <Statistic
                title="实时连接"
                value={
                  health.websocket.status === 'connected' ? '已连接' :
                  health.websocket.status === 'connecting' ? '连接中' : '已断开'
                }
                prefix={getStatusIcon(health.websocket.status)}
                valueStyle={{ fontSize: '14px' }}
              />
              <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
                最后消息: {health.websocket.lastMessage}
              </div>
            </Card>
          </Col>

          {/* 爬虫服务状态 */}
          <Col xs={24} sm={12} md={6}>
            <Card size="small" bordered={false}>
              <Statistic
                title="爬虫服务"
                value={
                  health.services.crawler === 'running' ? '运行中' :
                  health.services.crawler === 'stopped' ? '已停止' : '异常'
                }
                prefix={getStatusIcon(health.services.crawler)}
                valueStyle={{ fontSize: '14px' }}
              />
            </Card>
          </Col>

          {/* 数据库状态 */}
          <Col xs={24} sm={12} md={6}>
            <Card size="small" bordered={false}>
              <Statistic
                title="数据存储"
                value={health.services.database === 'connected' ? '已连接' : '断开'}
                prefix={getStatusIcon(health.services.database)}
                valueStyle={{ fontSize: '14px' }}
              />
            </Card>
          </Col>
        </Row>

        {/* 性能指标 */}
        <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
          <Col xs={24} sm={8}>
            <Card size="small" bordered={false}>
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '12px', color: '#666', marginBottom: '8px' }}>内存使用</div>
                <Progress
                  type="circle"
                  size={60}
                  percent={health.performance.memory}
                  status={health.performance.memory > 90 ? 'exception' : health.performance.memory > 75 ? 'active' : 'success'}
                  format={percent => `${percent}%`}
                />
              </div>
            </Card>
          </Col>

          <Col xs={24} sm={8}>
            <Card size="small" bordered={false}>
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '12px', color: '#666', marginBottom: '8px' }}>CPU使用</div>
                <Progress
                  type="circle"
                  size={60}
                  percent={health.performance.cpu}
                  status={health.performance.cpu > 80 ? 'exception' : health.performance.cpu > 60 ? 'active' : 'success'}
                  format={percent => `${percent}%`}
                />
              </div>
            </Card>
          </Col>

          <Col xs={24} sm={8}>
            <Card size="small" bordered={false}>
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '12px', color: '#666', marginBottom: '8px' }}>网络使用</div>
                <Progress
                  type="circle"
                  size={60}
                  percent={health.performance.network}
                  status={health.performance.network > 90 ? 'exception' : health.performance.network > 70 ? 'active' : 'success'}
                  format={percent => `${percent}%`}
                />
              </div>
            </Card>
          </Col>
        </Row>
      </Card>
    </div>
  );
};

export default SystemMonitor;