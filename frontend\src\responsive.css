/* 响应式布局改进 */

/* 基础响应式设置 */
* {
  box-sizing: border-box;
}

/* 确保根容器充满视口 */
#root {
  width: 100%;
  min-height: 100vh;
  overflow-x: hidden;
}

/* Ant Design 布局响应式调整 */
.ant-layout {
  min-height: 100vh;
  width: 100%;
}

.ant-layout-sider {
  flex: 0 0 200px !important;
  max-width: 200px !important;
  min-width: 200px !important;
}

/* 内容区域响应式 */
.ant-layout-content {
  padding: 24px;
  min-height: calc(100vh - 64px);
  overflow-x: auto;
}

/* 卡片响应式 */
.ant-card {
  margin-bottom: 16px;
  width: 100%;
}

/* 表单响应式 */
.ant-form {
  max-width: 100%;
}

.ant-form-item {
  margin-bottom: 16px;
}

/* 按钮组响应式 */
.ant-space {
  flex-wrap: wrap;
}

/* Tabs 响应式 */
.ant-tabs {
  width: 100%;
}

.ant-tabs-nav {
  flex-wrap: wrap;
}

/* 步骤条响应式 */
.ant-steps {
  overflow-x: auto;
  padding-bottom: 8px;
}

/* 大屏幕 (≥1920px) */
@media screen and (min-width: 1920px) {
  .ant-layout-content {
    max-width: 1600px;
    margin: 0 auto;
  }
  
  .ant-col-24 {
    padding: 0 12px;
  }
}

/* 标准桌面 (1366px - 1919px) */
@media screen and (min-width: 1366px) and (max-width: 1919px) {
  .ant-layout-content {
    padding: 20px;
  }
  
  .ant-card {
    margin-bottom: 16px;
  }
}

/* 小屏幕桌面 (1024px - 1365px) */
@media screen and (min-width: 1024px) and (max-width: 1365px) {
  .ant-layout-content {
    padding: 16px;
  }
  
  .ant-layout-sider {
    flex: 0 0 180px !important;
    max-width: 180px !important;
    min-width: 180px !important;
  }
  
  .ant-card {
    margin-bottom: 12px;
  }
}

/* 平板设备 (768px - 1023px) */
@media screen and (min-width: 768px) and (max-width: 1023px) {
  .ant-layout-sider {
    flex: 0 0 160px !important;
    max-width: 160px !important;
    min-width: 160px !important;
  }
  
  .ant-layout-content {
    padding: 12px;
  }
  
  .ant-tabs-tab {
    padding: 8px 12px;
    font-size: 14px;
  }
  
  .ant-steps-item-title {
    font-size: 13px;
  }
}

/* 移动设备 (< 768px) */
@media screen and (max-width: 767px) {
  /* 隐藏侧边栏，使用抽屉式菜单 */
  .ant-layout-sider {
    position: fixed !important;
    left: -200px;
    z-index: 999;
    transition: left 0.3s;
    height: 100vh;
  }
  
  .ant-layout-sider.ant-layout-sider-collapsed {
    left: 0;
  }
  
  .ant-layout-content {
    padding: 12px;
    margin-left: 0 !important;
  }
  
  /* 标签页改为下拉菜单 */
  .ant-tabs-nav {
    overflow-x: auto;
    overflow-y: hidden;
    white-space: nowrap;
    -webkit-overflow-scrolling: touch;
  }
  
  .ant-tabs-tab {
    padding: 6px 10px;
    font-size: 13px;
  }
  
  /* 步骤条简化 */
  .ant-steps {
    display: flex;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  
  .ant-steps-item {
    flex: 0 0 auto;
    min-width: 80px;
  }
  
  .ant-steps-item-title {
    font-size: 12px;
  }
  
  .ant-steps-item-description {
    display: none;
  }
  
  /* 卡片间距调整 */
  .ant-card {
    margin-bottom: 8px;
  }
  
  .ant-card-head {
    padding: 12px;
  }
  
  .ant-card-body {
    padding: 12px;
  }
  
  /* 表单元素全宽 */
  .ant-form-item-control-input {
    width: 100%;
  }
  
  .ant-input,
  .ant-select,
  .ant-input-number,
  .ant-picker {
    width: 100% !important;
  }
  
  /* 按钮响应式 */
  .ant-btn {
    margin-bottom: 8px;
    width: 100%;
  }
  
  .ant-space-horizontal {
    flex-direction: column;
    width: 100%;
  }
  
  .ant-space-horizontal > .ant-space-item {
    width: 100%;
  }
  
  /* 栅格系统在移动端 */
  .ant-row {
    margin: 0 !important;
  }
  
  .ant-col {
    padding: 0 !important;
    margin-bottom: 8px;
  }
  
  /* 表格响应式 */
  .ant-table-wrapper {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  
  .ant-table {
    min-width: 600px;
  }
}

/* 超小屏幕 (< 480px) */
@media screen and (max-width: 480px) {
  .ant-layout-header {
    padding: 0 12px;
  }
  
  .ant-layout-header h2 {
    font-size: 16px;
  }
  
  .ant-badge {
    transform: scale(0.8);
  }
  
  .ant-typography {
    font-size: 13px;
  }
}

/* 横屏模式优化 */
@media screen and (orientation: landscape) and (max-height: 600px) {
  .ant-layout-header {
    position: sticky;
    top: 0;
    z-index: 100;
  }
  
  .ant-layout-content {
    padding: 8px;
    min-height: calc(100vh - 48px);
  }
  
  .ant-card {
    margin-bottom: 8px;
  }
  
  .ant-steps {
    display: none;
  }
}

/* 打印样式 */
@media print {
  .ant-layout-sider,
  .ant-layout-header,
  .ant-tabs-nav,
  .ant-btn,
  .ant-space {
    display: none !important;
  }
  
  .ant-layout-content {
    padding: 0;
    margin: 0;
  }
  
  .ant-card {
    page-break-inside: avoid;
    border: 1px solid #d9d9d9;
    margin-bottom: 20px;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .ant-card,
  .ant-btn,
  .ant-input {
    border-width: 2px;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Cookie管理器特定样式 */
.cookie-manager-textarea {
  width: 100%;
  min-height: 120px;
  font-family: monospace;
}

/* 搜索配置特定样式 */
.search-config-form {
  max-width: 100%;
}

.keyword-input-wrapper {
  width: 100%;
}

/* 数据预览表格响应式 */
.data-preview-table {
  width: 100%;
  overflow-x: auto;
}

/* 进度监控响应式 */
.progress-monitor-card {
  width: 100%;
}

.progress-stats {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

@media screen and (max-width: 768px) {
  .progress-stats {
    flex-direction: column;
  }
}