import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import type { CrawlStatus, Product, SearchConfig, Statistics } from '@/types';

// 任务相关类型
export interface TaskInfo {
  id: string;
  name: string;
  config: SearchConfig;
  status: 'pending' | 'running' | 'paused' | 'completed' | 'failed' | 'cancelled';
  createdAt: string;
  startedAt?: string;
  completedAt?: string;
  progress: {
    currentPage: number;
    totalPages: number;
    collectedCount: number;
    failedCount: number;
  };
  statistics?: {
    duration: number;
    speed: number; // 条/分钟
    successRate: number;
  };
  error?: string;
}

interface AppState {
  // 爬虫状态
  crawlStatus: CrawlStatus;
  setCrawlStatus: (status: CrawlStatus | ((prev: CrawlStatus) => CrawlStatus)) => void;

  // 搜索配置
  searchConfig: SearchConfig;
  setSearchConfig: (config: SearchConfig) => void;

  // 产品数据
  products: Product[];
  setProducts: (products: Product[]) => void;
  addProducts: (products: Product[]) => void;
  clearProducts: () => void;

  // 统计数据
  statistics: Statistics | null;
  setStatistics: (stats: Statistics) => void;

  // WebSocket连接状态
  wsConnected: boolean;
  setWsConnected: (connected: boolean) => void;

  // 全局加载状态
  loading: {
    crawling: boolean;
    fetching: boolean;
    exporting: boolean;
  };
  setLoading: (key: keyof AppState['loading'], value: boolean) => void;

  // 错误信息
  error: string | null;
  setError: (error: string | null) => void;

  // 分页信息
  pagination: {
    current: number;
    pageSize: number;
    total: number;
  };
  setPagination: (pagination: Partial<AppState['pagination']>) => void;

  // 过滤器状态
  filters: {
    keyword: string;
    priceRange: [number, number] | null;
    brands: string[];
    categories: string[];
    minRating: number | null;
  };
  setFilters: (filters: Partial<AppState['filters']>) => void;
  resetFilters: () => void;

  // 排序状态
  sorting: {
    field: string | null;
    order: 'asc' | 'desc' | null;
  };
  setSorting: (field: string | null, order: 'asc' | 'desc' | null) => void;

  // 任务管理
  currentTask: TaskInfo | null;
  setCurrentTask: (task: TaskInfo | null) => void;
  activeTasks: TaskInfo[];
  setActiveTasks: (tasks: TaskInfo[]) => void;
  addTask: (task: TaskInfo) => void;
  updateTask: (taskId: string, updates: Partial<TaskInfo>) => void;
  removeTask: (taskId: string) => void;

  // Cookie 验证状态
  cookieValid: boolean;
  setCookieValid: (valid: boolean) => void;
}

const initialCrawlStatus: CrawlStatus = {
  isRunning: false,
  progress: {
    currentPage: 0,
    totalPages: 0,
    collectedCount: 0,
    failedCount: 0,
  },
};

const initialSearchConfig: SearchConfig = {
  keyword: '',
  pageSize: 20,
  maxPages: 10,
  sortType: 'sales',
};

const initialPagination = {
  current: 1,
  pageSize: 20,
  total: 0,
};

const initialFilters = {
  keyword: '',
  priceRange: null as [number, number] | null,
  brands: [] as string[],
  categories: [] as string[],
  minRating: null as number | null,
};

const initialSorting = {
  field: null as string | null,
  order: null as 'asc' | 'desc' | null,
};

export const useAppStore = create<AppState>()(
  devtools(
    (set) => ({
      // 初始状态
      crawlStatus: initialCrawlStatus,
      searchConfig: initialSearchConfig,
      products: [],
      statistics: null,
      wsConnected: false,
      loading: {
        crawling: false,
        fetching: false,
        exporting: false,
      },
      error: null,
      pagination: initialPagination,
      filters: initialFilters,
      sorting: initialSorting,
      currentTask: null,
      activeTasks: [],
      cookieValid: false,

      // Actions
      setCrawlStatus: (status) => 
        set((state) => ({ 
          crawlStatus: typeof status === 'function' ? status(state.crawlStatus) : status 
        }), false, 'setCrawlStatus'),

      setSearchConfig: (config) => 
        set({ searchConfig: config }, false, 'setSearchConfig'),

      setProducts: (products) => 
        set({ products }, false, 'setProducts'),

      addProducts: (newProducts) => 
        set((state) => ({ 
          products: [...state.products, ...newProducts] 
        }), false, 'addProducts'),

      clearProducts: () => 
        set({ products: [] }, false, 'clearProducts'),

      setStatistics: (statistics) => 
        set({ statistics }, false, 'setStatistics'),

      setWsConnected: (wsConnected) => 
        set({ wsConnected }, false, 'setWsConnected'),

      setLoading: (key, value) => 
        set((state) => ({
          loading: { ...state.loading, [key]: value }
        }), false, 'setLoading'),

      setError: (error) => 
        set({ error }, false, 'setError'),

      setPagination: (pagination) => 
        set((state) => ({
          pagination: { ...state.pagination, ...pagination }
        }), false, 'setPagination'),

      setFilters: (filters) => 
        set((state) => ({
          filters: { ...state.filters, ...filters }
        }), false, 'setFilters'),

      resetFilters: () => 
        set({ filters: initialFilters }, false, 'resetFilters'),

      setSorting: (field, order) => 
        set({ sorting: { field, order } }, false, 'setSorting'),

      // 任务管理
      setCurrentTask: (task) => 
        set({ currentTask: task }, false, 'setCurrentTask'),

      setActiveTasks: (tasks) => 
        set({ activeTasks: tasks }, false, 'setActiveTasks'),

      addTask: (task) => 
        set((state) => ({ 
          activeTasks: [task, ...state.activeTasks],
          currentTask: task
        }), false, 'addTask'),

      updateTask: (taskId, updates) => 
        set((state) => ({
          activeTasks: state.activeTasks.map(task => 
            task.id === taskId ? { ...task, ...updates } : task
          ),
          currentTask: state.currentTask?.id === taskId ? 
            { ...state.currentTask, ...updates } : state.currentTask
        }), false, 'updateTask'),

      removeTask: (taskId) => 
        set((state) => ({
          activeTasks: state.activeTasks.filter(task => task.id !== taskId),
          currentTask: state.currentTask?.id === taskId ? null : state.currentTask
        }), false, 'removeTask'),

      // Cookie 验证状态
      setCookieValid: (valid) => 
        set({ cookieValid: valid }, false, 'setCookieValid'),
    }),
    {
      name: 'pdd-crawler-store',
    }
  )
);