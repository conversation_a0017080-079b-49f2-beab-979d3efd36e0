import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  Steps,
  Row,
  Col,
  Alert,
  Button,
  Space,
  Typography,
  Tabs,
  Badge,
  notification
} from 'antd';
import {
  PlayCircleOutlined,
  MonitorOutlined,
  HistoryOutlined,
  ThunderboltOutlined,
  SettingOutlined,
  KeyOutlined,
  SearchOutlined
} from '@ant-design/icons';
import { useAppStore } from '@/stores/appStore';
import { useApi } from '@/hooks/useApi';
import { useWebSocket } from '@/hooks/useWebSocket';
import CookieManager from '@/components/CookieManager';
import SearchConfig from '@/components/SearchConfig';
import ProgressMonitor from '@/components/ProgressMonitor';
import DataPreview from '@/components/DataPreview';
import TaskControl from './TaskControl';
import TaskList from './TaskList';
import TaskHistory from './TaskHistory';
import type { 
  ControlPanelState, 
  TaskInfo, 
  TaskControlActions,
  StepValidation 
} from './types';
import type { SearchConfig as SearchConfigType } from '@/types';
import {
  validateCookieStep,
  validateConfigStep,
  generateTaskName,
  storage
} from './utils';

const { Title } = Typography;
const { Step } = Steps;

const CrawlControlPanel: React.FC = () => {
  // 状态管理
  const [state, setState] = useState<ControlPanelState>({
    activeTab: 'control',
    currentStep: 0,
    taskProgress: 0,
    stepValidation: {
      cookieValid: false,
      configValid: false,
      taskRunning: false,
      dataCollected: false,
    },
    loading: {
      cookie: false,
      config: false,
      task: false,
      data: false,
    },
    error: {
      cookie: null,
      config: null,
      task: null,
      data: null,
    },
  });

  // Store hooks
  const {
    crawlStatus,
    cookieStatus,
    products,
    error: globalError,
    isWsConnected,
    clearProducts,
  } = useAppStore();

  // API hooks
  const { 
    validateCookie, 
    startCrawl, 
    stopCrawl, 
    getCrawlStatus,
    getProducts 
  } = useApi();

  // WebSocket hook
  const { 
    isConnected: wsConnected, 
    sendMessage,
    stats: wsStats 
  } = useWebSocket(state.currentTask?.id);

  // 本地状态
  const [activeTab, setActiveTab] = useState('control');
  const [currentTask, setCurrentTask] = useState<TaskInfo | null>(null);
  const [allTasks, setAllTasks] = useState<TaskInfo[]>([]);
  const [cookieValid, setCookieValid] = useState(false);
  const [isSearching, setIsSearching] = useState(false);
  const [totalProductCount, setTotalProductCount] = useState(0);

  // 从存储加载任务列表
  useEffect(() => {
    const savedTasks = storage.getTasks();
    setAllTasks(savedTasks);
    
    // 查找运行中的任务
    const runningTask = savedTasks.find(t => t.status === 'running');
    if (runningTask) {
      setCurrentTask(runningTask);
      setIsSearching(true);
    }
  }, []);

  // 监听爬虫状态
  useEffect(() => {
    if (crawlStatus?.isRunning) {
      setIsSearching(true);
    } else {
      setIsSearching(false);
    }
  }, [crawlStatus]);

  // 监听Cookie状态
  useEffect(() => {
    setCookieValid(cookieStatus?.valid || false);
  }, [cookieStatus]);

  // 监听产品数量
  useEffect(() => {
    setTotalProductCount(products?.length || 0);
  }, [products]);

  // 处理Cookie更新
  const handleCookieUpdated = useCallback((valid: boolean) => {
    setCookieValid(valid);
    if (valid) {
      notification.success({
        message: 'Cookie验证成功',
        description: '可以开始配置搜索参数了',
      });
    }
  }, []);

  // 处理开始搜索
  const handleStartSearch = useCallback(async (config: SearchConfigType) => {
    try {
      setIsSearching(true);
      clearProducts();
      
      const result = await startCrawl(config);
      if (result?.task_id) {
        const newTask: TaskInfo = {
          id: result.task_id,
          name: generateTaskName(config),
          status: 'running',
          startTime: new Date().toISOString(),
          config,
          progress: {
            currentPage: 0,
            totalPages: 0,
            collectedCount: 0,
            failedCount: 0,
          }
        };
        
        setCurrentTask(newTask);
        setAllTasks(prev => {
          const updated = [newTask, ...prev];
          storage.saveTasks(updated);
          return updated;
        });
        
        setActiveTab('monitor');
        
        notification.success({
          message: '爬虫任务已启动',
          description: `任务ID: ${result.task_id}`,
        });
      }
    } catch (error) {
      console.error('启动爬虫失败:', error);
      setIsSearching(false);
      notification.error({
        message: '启动失败',
        description: error instanceof Error ? error.message : '未知错误',
      });
    }
  }, [startCrawl, clearProducts]);

  // 处理停止搜索
  const handleStopSearch = useCallback(async () => {
    if (!currentTask?.id) return;
    
    try {
      await stopCrawl(currentTask.id);
      setIsSearching(false);
      
      // 更新任务状态
      const updatedTask = { ...currentTask, status: 'stopped' as const };
      setCurrentTask(updatedTask);
      setAllTasks(prev => {
        const updated = prev.map(t => 
          t.id === currentTask.id ? updatedTask : t
        );
        storage.saveTasks(updated);
        return updated;
      });
      
      notification.info({
        message: '爬虫已停止',
      });
    } catch (error) {
      console.error('停止爬虫失败:', error);
      notification.error({
        message: '停止失败',
        description: error instanceof Error ? error.message : '未知错误',
      });
    }
  }, [currentTask, stopCrawl]);

  // 处理恢复搜索
  const handleResumeSearch = useCallback(async () => {
    if (!currentTask?.id) return;
    
    try {
      // 调用API恢复任务
      await getCrawlStatus(currentTask.id);
      setIsSearching(true);
      
      // 更新任务状态
      const updatedTask = { ...currentTask, status: 'running' as const };
      setCurrentTask(updatedTask);
      setAllTasks(prev => {
        const updated = prev.map(t => 
          t.id === currentTask.id ? updatedTask : t
        );
        storage.saveTasks(updated);
        return updated;
      });
      
      notification.success({
        message: '爬虫已恢复',
      });
    } catch (error) {
      console.error('恢复爬虫失败:', error);
      notification.error({
        message: '恢复失败',
        description: error instanceof Error ? error.message : '未知错误',
      });
    }
  }, [currentTask, getCrawlStatus]);

  // 处理任务选择
  const handleTaskSelect = useCallback((taskId: string) => {
    const task = allTasks.find(t => t.id === taskId);
    if (task) {
      setCurrentTask(task);
      if (task.status === 'running') {
        setIsSearching(true);
        setActiveTab('monitor');
      }
    }
  }, [allTasks]);

  // 处理任务加载
  const handleTaskLoad = useCallback((task: TaskInfo) => {
    setCurrentTask(task);
    if (task.status === 'running') {
      setIsSearching(true);
    }
    setActiveTab('monitor');
  }, []);

  // 任务操作集合
  const taskActions: TaskControlActions = {
    onPause: handleStopSearch,
    onResume: handleResumeSearch,
    onStop: handleStopSearch,
    onRestart: async () => {
      if (currentTask?.config) {
        await handleStartSearch(currentTask.config);
      }
    },
    onExport: async () => {
      // 实现导出功能
      notification.info({
        message: '导出功能开发中',
      });
    },
    onDelete: async () => {
      if (!currentTask) return;
      
      setAllTasks(prev => {
        const updated = prev.filter(t => t.id !== currentTask.id);
        storage.saveTasks(updated);
        return updated;
      });
      setCurrentTask(null);
      notification.success({
        message: '任务已删除',
      });
    },
  };


  // 步骤配置
  const steps = [
    {
      title: 'Cookie验证',
      description: cookieValid ? '已验证' : '待验证',
      icon: <KeyOutlined />,
      status: cookieValid ? 'finish' as const : 'wait' as const,
    },
    {
      title: '配置参数',
      description: '设置搜索条件',
      icon: <SettingOutlined />,
      status: currentTask ? 'finish' as const : 'wait' as const,
    },
    {
      title: '执行爬取',
      description: isSearching ? '进行中' : '待开始',
      icon: <PlayCircleOutlined />,
      status: isSearching ? 'process' as const : 'wait' as const,
    },
    {
      title: '数据预览',
      description: `已采集 ${totalProductCount} 条`,
      icon: <ThunderboltOutlined />,
      status: totalProductCount > 0 ? 'finish' as const : 'wait' as const,
    },
  ];

  return (
    <div>
      {/* 顶部状态栏 */}
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={[16, 16]} align="middle">
          <Col flex="auto">
            <Steps 
              current={
                isSearching ? 2 : 
                currentTask ? 1 : 
                cookieValid ? 1 : 0
              } 
              items={steps}
              size="small"
            />
          </Col>
          <Col>
            <Space>
              <Badge 
                status={wsConnected ? 'success' : 'error'} 
                text={wsConnected ? 'WebSocket已连接' : 'WebSocket未连接'} 
              />
              {wsStats && (
                <Typography.Text type="secondary">
                  延迟: {wsStats.averageLatency.toFixed(0)}ms
                </Typography.Text>
              )}
            </Space>
          </Col>
        </Row>

        {/* 错误提示 */}
        {globalError && (
          <Alert
            message="错误"
            description={globalError}
            type="error"
            showIcon
            closable
            style={{ marginTop: 16 }}
            onClose={() => useAppStore.setState({ error: null })}
          />
        )}

      </Card>

      {/* 标签页内容 */}
      <Tabs 
        activeKey={activeTab} 
        onChange={setActiveTab}
        type="card"
        size="large"
        items={[
          {
            label: (
              <Space>
                <SettingOutlined />
                控制面板
              </Space>
            ),
            key: 'control',
            children: (
              <Row gutter={[16, 16]}>
                {/* Cookie 管理 */}
                <Col span={24}>
                  <Card title="Cookie 管理" extra={
                    <Badge 
                      status={cookieValid ? 'success' : 'error'} 
                      text={cookieValid ? '已验证' : '未验证'} 
                    />
                  }>
                    <CookieManager
                      onCookieUpdated={handleCookieUpdated}
                      disabled={isSearching}
                    />
                  </Card>
                </Col>

                {/* 搜索配置 */}
                <Col span={24}>
                  <Card title="搜索配置">
                    <SearchConfig
                      onStartSearch={handleStartSearch}
                      disabled={!cookieValid || isSearching}
                    />
                  </Card>
                </Col>
              </Row>
            )
          },
          {
            label: (
              <Space>
                <MonitorOutlined />
                实时监控
                {currentTask && (
                  <Badge 
                    status={currentTask.status === 'running' ? 'processing' : 'default'} 
                  />
                )}
              </Space>
            ),
            key: 'monitor',
            children: currentTask ? (
              <ProgressMonitor 
                taskId={currentTask.id}
                showAdvanced={true}
                refreshInterval={1000}
              />
            ) : (
              <Card>
                <div style={{ textAlign: 'center', padding: '60px 0' }}>
                  <Typography.Text type="secondary">
                    暂无运行中的任务
                  </Typography.Text>
                  <br />
                  <Button 
                    type="link" 
                    onClick={() => setActiveTab('control')}
                    style={{ marginTop: 16 }}
                  >
                    前往控制面板启动任务
                  </Button>
                </div>
              </Card>
            )
          },
          {
            label: (
              <Space>
                <ThunderboltOutlined />
                数据预览
                <Badge count={totalProductCount} overflowCount={9999} />
              </Space>
            ),
            key: 'preview',
            children: (
              <DataPreview 
                height={600}
              />
            )
          },
          {
            label: (
              <Space>
                <SearchOutlined />
                任务管理
                {allTasks.length > 0 && (
                  <Badge count={allTasks.length} />
                )}
              </Space>
            ),
            key: 'tasks',
            children: (
              <TaskList 
                tasks={allTasks}
                onTaskSelect={handleTaskSelect}
                selectedTaskId={currentTask?.id}
              />
            )
          },
          {
            label: (
              <Space>
                <HistoryOutlined />
                历史记录
              </Space>
            ),
            key: 'history',
            children: (
              <TaskHistory 
                onTaskLoad={handleTaskLoad}
                actions={taskActions}
              />
            )
          }
        ]}
      />
    </div>
  );
};

export default CrawlControlPanel;