# 拼多多爬虫前端实现指南

## 📋 项目概述

本指南详细说明如何实现功能完全对等的拼多多爬虫前端系统，确保与后端 FastAPI + WebSocket 服务的完美集成。

## 🚀 快速开始

### 环境要求

```bash
Node.js: >= 18.0.0
npm: >= 8.0.0
Git: >= 2.30.0
```

### 项目初始化

```bash
# 1. 创建项目
npm create vite@latest pdd-crawler-frontend -- --template react-ts
cd pdd-crawler-frontend

# 2. 安装核心依赖
npm install \
  react@^18.2.0 \
  react-dom@^18.2.0 \
  @types/react@^18.0.0 \
  @types/react-dom@^18.0.0 \
  typescript@^5.0.0

# 3. 安装UI组件库
npm install antd@^5.0.0

# 4. 安装状态管理
npm install zustand@^4.0.0

# 5. 安装网络通信
npm install \
  axios@^1.0.0 \
  react-use-websocket@^4.0.0

# 6. 安装路由
npm install react-router-dom@^6.0.0

# 7. 安装工具库
npm install \
  dayjs@^1.11.0 \
  file-saver@^2.0.5 \
  react-error-boundary@^4.0.0

# 8. 安装开发依赖
npm install -D \
  @typescript-eslint/eslint-plugin@^6.0.0 \
  @typescript-eslint/parser@^6.0.0 \
  eslint@^8.0.0 \
  eslint-plugin-react@^7.32.0 \
  eslint-plugin-react-hooks@^4.6.0 \
  prettier@^3.0.0 \
  @vitejs/plugin-react@^4.0.0
```

### 项目结构搭建

```bash
mkdir -p src/{components,hooks,services,stores,types,utils,styles}
mkdir -p src/components/{common,crawler}
mkdir -p src/components/crawler/{CrawlControl,ProgressMonitor,DataPreview,CookieManager,ExportManager}
mkdir -p src/services/{api,websocket}
mkdir -p public
```

## 🏗️ 核心实现步骤

### Phase 1: 基础架构搭建（第1-2天）

#### 1.1 配置文件设置

**vite.config.ts**
```typescript
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  server: {
    port: 5173,
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
      },
      '/ws': {
        target: 'ws://localhost:8000',
        ws: true,
      },
    },
  },
  build: {
    outDir: 'dist',
    sourcemap: false,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          antd: ['antd'],
          utils: ['axios', 'dayjs'],
          websocket: ['react-use-websocket'],
        },
      },
    },
  },
})
```

**tsconfig.json**
```json
{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
    }
  },
  "include": ["src"],
  "references": [{ "path": "./tsconfig.node.json" }]
}
```

**环境变量配置**
```bash
# .env.development
VITE_API_BASE_URL=http://localhost:8000
VITE_WS_BASE_URL=ws://localhost:8000
VITE_APP_TITLE=拼多多爬虫系统

# .env.production  
VITE_API_BASE_URL=https://your-api-domain.com
VITE_WS_BASE_URL=wss://your-api-domain.com
VITE_APP_TITLE=拼多多爬虫系统
```

#### 1.2 基础类型定义

**src/types/index.ts**
```typescript
// 导出所有类型
export * from './api'
export * from './crawler'
export * from './cookie'
export * from './product'
export * from './websocket'

// 通用类型
export interface BaseResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  code?: number;
}

export interface PaginationConfig {
  current: number;
  pageSize: number;
  total: number;
  showTotal?: boolean;
  showSizeChanger?: boolean;
}

export interface TableColumn<T = any> {
  key: string;
  title: string;
  dataIndex: keyof T;
  width?: number;
  align?: 'left' | 'center' | 'right';
  sorter?: boolean;
  render?: (value: any, record: T, index: number) => React.ReactNode;
}
```

**src/types/product.ts**
```typescript
// 商品数据类型（与后端完全一致）
export interface Product {
  // 基础信息
  goods_id: string;
  goods_name: string;
  price: number;
  original_price?: number;
  coupon_price?: number;
  market_price?: number;
  currency?: string;
  
  // 媒体信息
  thumb_url: string;
  image_url?: string;
  hd_thumb_url?: string;
  
  // 销售信息
  sales: number;
  sales_tip: string;
  comment_count?: number;
  rating?: number;
  
  // 商家信息
  mall_name?: string;
  mall_id?: string;
  merchant_type_name?: string;
  
  // 营销信息
  tags?: string[];
  marketing_tags?: string[];
  special_text?: string;
  subsidy_info?: string; // 补贴详情 - 重要字段
  activity_type_name?: string;
  
  // 分类和品牌
  category?: string;
  brand_name?: string;
  
  // 技术信息
  goods_url: string;
  keyword?: string; // 关联的搜索关键词
  created_time?: string;
  collect_time?: string;
  
  // 扩展字段
  [key: string]: any;
}

// 产品统计信息
export interface ProductStatistics {
  totalCount: number;
  averagePrice: number;
  priceRange: {
    min: number;
    max: number;
  };
  brandDistribution: Record<string, number>;
  categoryDistribution: Record<string, number>;
  keywordDistribution: Record<string, number>;
}
```

**src/types/crawler.ts**
```typescript
// 爬取配置类型
export interface CrawlConfig {
  keywords: string[];
  targetCount: number;
  sortMethod?: 'default' | 'price_asc' | 'price_desc' | 'sales_desc';
  maxPages?: number;
  headless?: boolean;
  enableFilter?: boolean;
  filterConfig?: FilterConfig;
}

export interface FilterConfig {
  minPrice?: number;
  maxPrice?: number;
  minSales?: number;
  brands?: string[];
  excludeKeywords?: string[];
}

// 任务相关类型
export interface CrawlTask {
  id: string;
  config: CrawlConfig;
  status: TaskStatus;
  progress: ProgressInfo;
  startTime: string;
  endTime?: string;
  error?: string;
  results: Product[];
}

export type TaskStatus = 'pending' | 'running' | 'paused' | 'completed' | 'failed';

export interface ProgressInfo {
  current: number;
  total: number;
  percentage: number;
  currentKeyword?: string;
  keywordProgress: Record<string, KeywordProgress>;
}

export interface KeywordProgress {
  keyword: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  current: number;
  target: number;
  percentage: number;
  startTime?: string;
  endTime?: string;
}
```

#### 1.3 常量定义

**src/utils/constants.ts**
```typescript
// API 配置
export const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';
export const WS_BASE_URL = import.meta.env.VITE_WS_BASE_URL || 'ws://localhost:8000';

// WebSocket 配置
export const WEBSOCKET_CONFIG = {
  RECONNECT_ATTEMPTS: 10,
  BASE_RECONNECT_DELAY: 1000,
  MAX_RECONNECT_DELAY: 10000,
  HEARTBEAT_INTERVAL: 25000,
  HEARTBEAT_TIMEOUT: 60000,
  MESSAGE_QUEUE_SIZE: 100,
} as const;

// API 配置
export const API_CONFIG = {
  TIMEOUT: 30000,
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,
} as const;

// 默认爬取配置
export const DEFAULT_CRAWL_CONFIG: CrawlConfig = {
  keywords: [],
  targetCount: 60,
  sortMethod: 'default',
  maxPages: 5,
  headless: true,
  enableFilter: false,
};

// 排序选项
export const SORT_OPTIONS = [
  { value: 'default', label: '默认排序' },
  { value: 'price_asc', label: '价格从低到高' },
  { value: 'price_desc', label: '价格从高到低' },
  { value: 'sales_desc', label: '销量从高到低' },
] as const;

// 表格列配置
export const PRODUCT_TABLE_COLUMNS = [
  { key: 'goods_name', title: '商品名称', width: 300 },
  { key: 'price', title: '价格', width: 100 },
  { key: 'original_price', title: '原价', width: 100 },
  { key: 'sales', title: '销量', width: 100 },
  { key: 'brand_name', title: '品牌', width: 120 },
  { key: 'subsidy_info', title: '补贴详情', width: 150 },
  { key: 'keyword', title: '关键词', width: 100 },
] as const;

// 错误消息
export const ERROR_MESSAGES = {
  NETWORK_ERROR: '网络连接失败，请检查网络状态',
  WEBSOCKET_ERROR: 'WebSocket连接失败，请刷新页面重试',
  COOKIE_INVALID: 'Cookie已失效，请重新导入',
  CONFIG_INVALID: '配置参数不正确，请检查后重试',
  EXPORT_FAILED: '数据导出失败，请重试',
} as const;
```

### Phase 2: 核心服务层实现（第3-4天）

#### 2.1 HTTP API客户端

**src/services/api/client.ts**
```typescript
import axios, { AxiosInstance, AxiosRequestConfig, AxiosError } from 'axios';
import { API_BASE_URL, API_CONFIG } from '@/utils/constants';

export interface ApiError {
  code: number;
  message: string;
  details?: any;
}

export class ApiClient {
  private axiosInstance: AxiosInstance;

  constructor(baseURL: string = API_BASE_URL) {
    this.axiosInstance = axios.create({
      baseURL,
      timeout: API_CONFIG.TIMEOUT,
      headers: {
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // 请求拦截器
    this.axiosInstance.interceptors.request.use(
      (config) => {
        config.headers['X-Request-ID'] = `${Date.now()}-${Math.random()}`;
        console.log(`🚀 API请求: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        console.error('❌ 请求拦截错误:', error);
        return Promise.reject(error);
      }
    );

    // 响应拦截器
    this.axiosInstance.interceptors.response.use(
      (response) => {
        console.log(`✅ API响应: ${response.status} ${response.config.url}`);
        return response.data;
      },
      (error) => {
        const apiError = this.handleError(error);
        console.error('❌ API错误:', apiError);
        return Promise.reject(apiError);
      }
    );
  }

  private handleError(error: AxiosError): ApiError {
    if (error.response) {
      return {
        code: error.response.status,
        message: error.response.data?.message || error.message,
        details: error.response.data,
      };
    } else if (error.request) {
      return {
        code: 0,
        message: '网络连接失败，请检查网络状态',
        details: error.request,
      };
    } else {
      return {
        code: -1,
        message: error.message || '未知错误',
        details: error,
      };
    }
  }

  async get<T>(url: string, params?: any): Promise<T> {
    return this.axiosInstance.get(url, { params });
  }

  async post<T>(url: string, data?: any): Promise<T> {
    return this.axiosInstance.post(url, data);
  }

  async put<T>(url: string, data?: any): Promise<T> {
    return this.axiosInstance.put(url, data);
  }

  async delete<T>(url: string): Promise<T> {
    return this.axiosInstance.delete(url);
  }

  // 文件下载
  async downloadFile(url: string, params?: any): Promise<Blob> {
    const response = await this.axiosInstance.get(url, {
      params,
      responseType: 'blob',
    });
    return response.data;
  }
}

// 创建默认实例
export const apiClient = new ApiClient();
```

**src/services/api/crawler.ts**
```typescript
import { ApiClient } from './client';
import type { CrawlConfig, CrawlResponse, CrawlStatus, PreviewData, ExportResponse } from '@/types';

export class CrawlerApi extends ApiClient {
  // 启动爬虫任务
  async startCrawl(config: CrawlConfig): Promise<CrawlResponse> {
    return this.post('/api/crawl/start', {
      keywords: config.keywords,
      targetCount: config.targetCount,
      sortMethod: config.sortMethod || 'default',
      maxPages: config.maxPages || 5,
      headless: config.headless ?? true,
      enableFilter: config.enableFilter ?? false,
    });
  }

  // 获取任务状态
  async getCrawlStatus(taskId: string): Promise<CrawlStatus> {
    return this.get(`/api/crawl/${taskId}/status`);
  }

  // 任务控制
  async pauseCrawl(taskId: string): Promise<void> {
    return this.post(`/api/crawl/${taskId}/pause`);
  }

  async resumeCrawl(taskId: string): Promise<void> {
    return this.post(`/api/crawl/${taskId}/resume`);
  }

  async stopCrawl(taskId: string): Promise<void> {
    return this.post(`/api/crawl/${taskId}/stop`);
  }

  // 数据相关
  async getPreviewData(taskId: string, limit: number = 20): Promise<PreviewData> {
    return this.get(`/api/crawl/${taskId}/preview`, { limit });
  }

  // 导出功能
  async exportExcel(taskId: string): Promise<ExportResponse> {
    return this.post(`/api/export/${taskId}`);
  }

  async exportCsv(taskId: string): Promise<ExportResponse> {
    return this.post(`/api/export/${taskId}/csv`);
  }

  async downloadExport(taskId: string, format: 'xlsx' | 'csv' = 'xlsx'): Promise<Blob> {
    return this.downloadFile(`/api/export/${taskId}/download`, { format });
  }
}

export const crawlerApi = new CrawlerApi();
```

#### 2.2 WebSocket管理器

**src/services/websocket/manager.ts**
```typescript
import useWebSocketLib, { ReadyState } from 'react-use-websocket';
import { WS_BASE_URL, WEBSOCKET_CONFIG } from '@/utils/constants';
import type { CrawlerWebSocketMessage } from '@/types';

export interface WebSocketConfig {
  shouldReconnect?: boolean;
  reconnectAttempts?: number;
  reconnectInterval?: number | ((attemptNumber: number) => number);
  onOpen?: () => void;
  onClose?: () => void;
  onError?: (error: Event) => void;
  onMessage?: (message: MessageEvent) => void;
}

export function useCrawlerWebSocket(taskId: string | null, config?: WebSocketConfig) {
  const socketUrl = taskId ? `${WS_BASE_URL}/ws/crawl/${taskId}` : null;

  const {
    sendMessage,
    sendJsonMessage,
    lastMessage,
    lastJsonMessage,
    readyState,
    getWebSocket,
  } = useWebSocketLib(
    socketUrl,
    {
      shouldReconnect: (closeEvent) => {
        console.log('🔌 WebSocket关闭:', closeEvent.code, closeEvent.reason);
        return config?.shouldReconnect ?? true;
      },

      reconnectAttempts: config?.reconnectAttempts ?? WEBSOCKET_CONFIG.RECONNECT_ATTEMPTS,

      reconnectInterval: config?.reconnectInterval ?? ((attemptNumber) => {
        const delay = Math.min(
          Math.pow(2, attemptNumber) * WEBSOCKET_CONFIG.BASE_RECONNECT_DELAY,
          WEBSOCKET_CONFIG.MAX_RECONNECT_DELAY
        );
        console.log(`🔄 WebSocket重连: 第${attemptNumber + 1}次尝试，延迟${delay}ms`);
        return delay;
      }),

      heartbeat: {
        message: JSON.stringify({ type: 'ping' }),
        returnMessage: JSON.stringify({ type: 'pong' }),
        timeout: WEBSOCKET_CONFIG.HEARTBEAT_TIMEOUT,
        interval: WEBSOCKET_CONFIG.HEARTBEAT_INTERVAL,
      },

      onOpen: (event) => {
        console.log('✅ WebSocket连接建立:', taskId);
        config?.onOpen?.();
      },

      onClose: (event) => {
        console.log('❌ WebSocket连接关闭:', event.code, event.reason);
        config?.onClose?.();
      },

      onError: (event) => {
        console.error('💥 WebSocket错误:', event);
        config?.onError?.(event);
      },

      onMessage: (event) => {
        console.log('📨 WebSocket消息:', event.data);
        config?.onMessage?.(event);
      },

      filter: (message) => {
        try {
          const data = JSON.parse(message.data);
          return data && typeof data === 'object';
        } catch {
          return false;
        }
      },

      protocols: ['crawler-protocol'],
      queryParams: {
        version: '1.0',
        client: 'react-frontend',
      },
    }
  );

  const connectionStatus = {
    [ReadyState.CONNECTING]: 'connecting',
    [ReadyState.OPEN]: 'connected',
    [ReadyState.CLOSING]: 'closing',
    [ReadyState.CLOSED]: 'closed',
    [ReadyState.UNINSTANTIATED]: 'uninstantiated',
  }[readyState] as const;

  return {
    sendMessage,
    sendJsonMessage,
    lastMessage,
    lastJsonMessage,
    connectionStatus,
    isConnected: readyState === ReadyState.OPEN,
    isConnecting: readyState === ReadyState.CONNECTING,
    getWebSocket,
  };
}
```

### Phase 3: 状态管理实现（第5天）

#### 3.1 Zustand状态管理

**src/stores/crawlerStore.ts**
```typescript
import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { crawlerApi } from '@/services/api/crawler';
import type { CrawlConfig, CrawlTask, Product, ProgressInfo, KeywordProgress } from '@/types';
import { DEFAULT_CRAWL_CONFIG } from '@/utils/constants';

export interface CrawlerState {
  // 任务状态
  currentTask: CrawlTask | null;
  taskHistory: CrawlTask[];
  isRunning: boolean;
  isPaused: boolean;
  
  // 配置状态
  config: CrawlConfig;
  
  // 数据状态
  products: Product[];
  productsByKeyword: Record<string, Product[]>;
  statistics: any;
  
  // 进度状态
  progress: ProgressInfo;
  
  // UI状态
  loading: boolean;
  error: string | null;
  
  // 操作方法
  setConfig: (config: Partial<CrawlConfig>) => void;
  startCrawl: (config?: CrawlConfig) => Promise<void>;
  stopCrawl: () => Promise<void>;
  pauseCrawl: () => Promise<void>;
  resumeCrawl: () => Promise<void>;
  updateProgress: (progress: Partial<ProgressInfo>) => void;
  addProducts: (products: Product[], keyword?: string) => void;
  updateKeywordStatus: (keyword: string, status: string, data?: any) => void;
  setTaskCompleted: (data: any) => void;
  setError: (error: string | null) => void;
  clearData: () => void;
  reset: () => void;
}

export const useCrawlerStore = create<CrawlerState>()(
  devtools(
    persist(
      (set, get) => ({
        // 初始状态
        currentTask: null,
        taskHistory: [],
        isRunning: false,
        isPaused: false,
        config: DEFAULT_CRAWL_CONFIG,
        products: [],
        productsByKeyword: {},
        statistics: {},
        progress: {
          current: 0,
          total: 0,
          percentage: 0,
          keywordProgress: {},
        },
        loading: false,
        error: null,

        // 配置管理
        setConfig: (newConfig) => {
          set((state) => ({
            config: { ...state.config, ...newConfig },
          }));
        },

        // 任务控制
        startCrawl: async (config) => {
          const finalConfig = config || get().config;
          set({ loading: true, error: null });

          try {
            const response = await crawlerApi.startCrawl(finalConfig);
            
            if (response.success) {
              const newTask: CrawlTask = {
                id: response.taskId,
                config: finalConfig,
                status: 'running',
                progress: {
                  current: 0,
                  total: finalConfig.targetCount,
                  percentage: 0,
                  keywordProgress: {},
                },
                startTime: new Date().toISOString(),
                results: [],
              };

              set({
                currentTask: newTask,
                isRunning: true,
                isPaused: false,
                loading: false,
                products: [],
                productsByKeyword: {},
                progress: newTask.progress,
              });
            } else {
              throw new Error(response.message || '启动任务失败');
            }
          } catch (error) {
            set({ 
              loading: false, 
              error: error instanceof Error ? error.message : '启动任务失败',
            });
            throw error;
          }
        },

        stopCrawl: async () => {
          const { currentTask } = get();
          if (!currentTask) return;

          try {
            await crawlerApi.stopCrawl(currentTask.id);
            set((state) => ({
              isRunning: false,
              isPaused: false,
              currentTask: state.currentTask ? {
                ...state.currentTask,
                status: 'stopped',
                endTime: new Date().toISOString(),
              } : null,
              taskHistory: state.currentTask ? 
                [...state.taskHistory, { ...state.currentTask, status: 'stopped' }] : 
                state.taskHistory,
            }));
          } catch (error) {
            set({ error: error instanceof Error ? error.message : '停止任务失败' });
          }
        },

        pauseCrawl: async () => {
          const { currentTask } = get();
          if (!currentTask) return;

          try {
            await crawlerApi.pauseCrawl(currentTask.id);
            set({ isPaused: true });
          } catch (error) {
            set({ error: error instanceof Error ? error.message : '暂停任务失败' });
          }
        },

        resumeCrawl: async () => {
          const { currentTask } = get();
          if (!currentTask) return;

          try {
            await crawlerApi.resumeCrawl(currentTask.id);
            set({ isPaused: false });
          } catch (error) {
            set({ error: error instanceof Error ? error.message : '恢复任务失败' });
          }
        },

        // 进度更新
        updateProgress: (newProgress) => {
          set((state) => ({
            progress: { ...state.progress, ...newProgress },
            currentTask: state.currentTask ? {
              ...state.currentTask,
              progress: { ...state.currentTask.progress, ...newProgress },
            } : null,
          }));
        },

        // 数据管理
        addProducts: (newProducts, keyword) => {
          set((state) => {
            const products = [...state.products, ...newProducts];
            const productsByKeyword = { ...state.productsByKeyword };
            
            if (keyword) {
              productsByKeyword[keyword] = [
                ...(productsByKeyword[keyword] || []),
                ...newProducts,
              ];
            }

            return {
              products,
              productsByKeyword,
              currentTask: state.currentTask ? {
                ...state.currentTask,
                results: products,
              } : null,
            };
          });
        },

        updateKeywordStatus: (keyword, status, data) => {
          set((state) => {
            const keywordProgress = { ...state.progress.keywordProgress };
            
            if (!keywordProgress[keyword]) {
              keywordProgress[keyword] = {
                keyword,
                status: 'pending',
                current: 0,
                target: Math.floor(state.config.targetCount / state.config.keywords.length),
                percentage: 0,
              };
            }

            keywordProgress[keyword] = {
              ...keywordProgress[keyword],
              status: status as any,
              ...data,
            };

            return {
              progress: {
                ...state.progress,
                keywordProgress,
                currentKeyword: status === 'started' ? keyword : state.progress.currentKeyword,
              },
            };
          });
        },

        setTaskCompleted: (data) => {
          set((state) => ({
            isRunning: false,
            isPaused: false,
            currentTask: state.currentTask ? {
              ...state.currentTask,
              status: 'completed',
              endTime: new Date().toISOString(),
            } : null,
            taskHistory: state.currentTask ? 
              [...state.taskHistory, { ...state.currentTask, status: 'completed' }] : 
              state.taskHistory,
          }));
        },

        setError: (error) => {
          set({ error });
        },

        clearData: () => {
          set({
            products: [],
            productsByKeyword: {},
            statistics: {},
            progress: {
              current: 0,
              total: 0,
              percentage: 0,
              keywordProgress: {},
            },
          });
        },

        reset: () => {
          set({
            currentTask: null,
            isRunning: false,
            isPaused: false,
            products: [],
            productsByKeyword: {},
            statistics: {},
            progress: {
              current: 0,
              total: 0,
              percentage: 0,
              keywordProgress: {},
            },
            loading: false,
            error: null,
          });
        },
      }),
      {
        name: 'crawler-store',
        partialize: (state) => ({
          config: state.config,
          taskHistory: state.taskHistory,
        }),
      }
    ),
    { name: 'CrawlerStore' }
  )
);
```

### Phase 4: 核心组件实现（第6-10天）

#### 4.1 CrawlControl组件

**src/components/crawler/CrawlControl/index.tsx**
```typescript
import React, { useState, useCallback } from 'react';
import { Card, Form, Button, Space, message } from 'antd';
import { PlayCircleOutlined, PauseCircleOutlined, StopOutlined, ReloadOutlined } from '@ant-design/icons';
import { useCrawlerStore } from '@/stores/crawlerStore';
import { KeywordInput } from './KeywordInput';
import { ConfigPanel } from './ConfigPanel';
import { TaskStatus } from './TaskStatus';
import type { CrawlConfig } from '@/types';
import styles from './index.module.css';

export const CrawlControl: React.FC = () => {
  const [form] = Form.useForm();
  const {
    config,
    currentTask,
    isRunning,
    isPaused,
    loading,
    error,
    setConfig,
    startCrawl,
    pauseCrawl,
    resumeCrawl,
    stopCrawl,
    reset,
  } = useCrawlerStore();

  const [validationErrors, setValidationErrors] = useState<string[]>([]);

  // 配置变更处理
  const handleConfigChange = useCallback((values: Partial<CrawlConfig>) => {
    setConfig(values);
  }, [setConfig]);

  // 配置验证
  const validateConfig = useCallback((config: CrawlConfig): string[] => {
    const errors: string[] = [];

    if (!config.keywords || config.keywords.length === 0) {
      errors.push('请至少输入一个关键词');
    }

    if (config.targetCount <= 0) {
      errors.push('目标数量必须大于0');
    }

    if (config.targetCount > 1000) {
      errors.push('目标数量不能超过1000');
    }

    config.keywords.forEach((keyword, index) => {
      if (!keyword.trim()) {
        errors.push(`第${index + 1}个关键词不能为空`);
      }
      if (keyword.length > 50) {
        errors.push(`第${index + 1}个关键词长度不能超过50个字符`);
      }
    });

    return errors;
  }, []);

  // 开始爬取
  const handleStart = useCallback(async () => {
    try {
      const errors = validateConfig(config);
      setValidationErrors(errors);

      if (errors.length > 0) {
        message.error('配置验证失败，请检查配置');
        return;
      }

      await startCrawl(config);
      message.success('爬虫任务已启动');
    } catch (error) {
      message.error(error instanceof Error ? error.message : '启动失败');
    }
  }, [config, validateConfig, startCrawl]);

  // 暂停/恢复
  const handlePauseResume = useCallback(async () => {
    try {
      if (isPaused) {
        await resumeCrawl();
        message.success('任务已恢复');
      } else {
        await pauseCrawl();
        message.success('任务已暂停');
      }
    } catch (error) {
      message.error(error instanceof Error ? error.message : '操作失败');
    }
  }, [isPaused, pauseCrawl, resumeCrawl]);

  // 停止
  const handleStop = useCallback(async () => {
    try {
      await stopCrawl();
      message.success('任务已停止');
    } catch (error) {
      message.error(error instanceof Error ? error.message : '停止失败');
    }
  }, [stopCrawl]);

  // 重置
  const handleReset = useCallback(() => {
    reset();
    form.resetFields();
    setValidationErrors([]);
    message.success('已重置');
  }, [reset, form]);

  return (
    <Card 
      title="爬取控制" 
      className={styles.crawlControl}
      extra={<TaskStatus task={currentTask} />}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={config}
        onValuesChange={handleConfigChange}
        disabled={isRunning}
      >
        {/* 关键词输入 */}
        <KeywordInput
          value={config.keywords}
          onChange={(keywords) => handleConfigChange({ keywords })}
          error={validationErrors.find(err => err.includes('关键词'))}
        />

        {/* 配置面板 */}
        <ConfigPanel
          config={config}
          onChange={handleConfigChange}
          errors={validationErrors}
        />

        {/* 操作按钮 */}
        <Form.Item>
          <Space>
            {!isRunning ? (
              <Button
                type="primary"
                icon={<PlayCircleOutlined />}
                onClick={handleStart}
                loading={loading}
                size="large"
              >
                开始爬取
              </Button>
            ) : (
              <>
                <Button
                  icon={isPaused ? <PlayCircleOutlined /> : <PauseCircleOutlined />}
                  onClick={handlePauseResume}
                  size="large"
                >
                  {isPaused ? '恢复' : '暂停'}
                </Button>
                <Button
                  icon={<StopOutlined />}
                  onClick={handleStop}
                  danger
                  size="large"
                >
                  停止
                </Button>
              </>
            )}
            
            <Button
              icon={<ReloadOutlined />}
              onClick={handleReset}
              disabled={isRunning}
            >
              重置
            </Button>
          </Space>
        </Form.Item>

        {/* 错误显示 */}
        {error && (
          <div className={styles.error}>
            {error}
          </div>
        )}

        {/* 验证错误显示 */}
        {validationErrors.length > 0 && (
          <div className={styles.validationErrors}>
            <ul>
              {validationErrors.map((error, index) => (
                <li key={index}>{error}</li>
              ))}
            </ul>
          </div>
        )}
      </Form>
    </Card>
  );
};
```

**src/components/crawler/CrawlControl/KeywordInput.tsx**
```typescript
import React, { useState, useCallback, useMemo } from 'react';
import { Form, Select, Tag, Space, Button, Tooltip } from 'antd';
import { PlusOutlined, HistoryOutlined } from '@ant-design/icons';

interface KeywordInputProps {
  value?: string[];
  onChange?: (keywords: string[]) => void;
  error?: string;
  disabled?: boolean;
}

export const KeywordInput: React.FC<KeywordInputProps> = ({
  value = [],
  onChange,
  error,
  disabled = false,
}) => {
  const [inputValue, setInputValue] = useState('');
  const [keywordHistory, setKeywordHistory] = useState<string[]>(() => {
    return JSON.parse(localStorage.getItem('keyword-history') || '[]');
  });

  // 智能关键词解析
  const parseKeywords = useCallback((input: string): string[] => {
    if (!input.trim()) return [];

    // 支持多种分隔符：逗号、分号、换行符、空格
    const separators = /[,，;；\n\r\s]+/;
    
    return input
      .split(separators)
      .map(keyword => keyword.trim())
      .filter(keyword => keyword.length > 0)
      .filter((keyword, index, arr) => arr.indexOf(keyword) === index); // 去重
  }, []);

  // 处理输入变化
  const handleInputChange = useCallback((inputValue: string) => {
    setInputValue(inputValue);
    
    // 实时解析关键词
    const keywords = parseKeywords(inputValue);
    onChange?.(keywords);
  }, [parseKeywords, onChange]);

  // 处理选择变化
  const handleSelectChange = useCallback((selectedKeywords: string[]) => {
    onChange?.(selectedKeywords);
  }, [onChange]);

  // 添加到历史记录
  const addToHistory = useCallback((keywords: string[]) => {
    const newHistory = [...new Set([...keywords, ...keywordHistory])].slice(0, 20);
    setKeywordHistory(newHistory);
    localStorage.setItem('keyword-history', JSON.stringify(newHistory));
  }, [keywordHistory]);

  // 处理焦点失去
  const handleBlur = useCallback(() => {
    if (value.length > 0) {
      addToHistory(value);
    }
  }, [value, addToHistory]);

  // 从历史记录选择
  const handleHistorySelect = useCallback((selectedHistory: string[]) => {
    const newKeywords = [...new Set([...value, ...selectedHistory])];
    onChange?.(newKeywords);
  }, [value, onChange]);

  // 关键词建议选项
  const suggestionOptions = useMemo(() => {
    return keywordHistory
      .filter(keyword => !value.includes(keyword))
      .map(keyword => ({
        label: keyword,
        value: keyword,
      }));
  }, [keywordHistory, value]);

  return (
    <Space direction="vertical" style={{ width: '100%' }}>
      <Form.Item
        label="搜索关键词"
        validateStatus={error ? 'error' : ''}
        help={error}
        required
      >
        <Select
          mode="tags"
          placeholder="输入关键词，支持逗号、分号、换行等分隔符"
          value={value}
          onChange={handleSelectChange}
          onBlur={handleBlur}
          disabled={disabled}
          style={{ width: '100%' }}
          tokenSeparators={[',', '，', ';', '；', '\n']}
          options={suggestionOptions}
          tagRender={(props) => (
            <Tag
              color="blue"
              closable={props.closable}
              onClose={props.onClose}
              style={{ marginRight: 3 }}
            >
              {props.label}
            </Tag>
          )}
        />
      </Form.Item>

      {/* 历史记录快速选择 */}
      {keywordHistory.length > 0 && (
        <div>
          <Space wrap>
            <Tooltip title="历史关键词">
              <HistoryOutlined />
            </Tooltip>
            {keywordHistory.slice(0, 10).map(keyword => (
              <Tag
                key={keyword}
                style={{ cursor: 'pointer' }}
                onClick={() => handleHistorySelect([keyword])}
              >
                {keyword}
              </Tag>
            ))}
          </Space>
        </div>
      )}

      {/* 关键词统计 */}
      {value.length > 0 && (
        <div style={{ fontSize: '12px', color: '#666' }}>
          已输入 {value.length} 个关键词，预计爬取 {value.length * 60} 条数据
        </div>
      )}
    </Space>
  );
};
```

### Phase 5: WebSocket集成（第11-12天）

#### 5.1 WebSocket Hook实现

**src/hooks/useWebSocketHandler.ts**
```typescript
import { useEffect, useState, useCallback } from 'react';
import { useCrawlerWebSocket } from '@/services/websocket/manager';
import { useCrawlerStore } from '@/stores/crawlerStore';
import type { CrawlerWebSocketMessage } from '@/types';

export function useWebSocketHandler(taskId: string | null) {
  const crawlerStore = useCrawlerStore();
  const [connectionMetrics, setConnectionMetrics] = useState({
    connectedAt: null as Date | null,
    lastMessageAt: null as Date | null,
    messageCount: 0,
    reconnectCount: 0,
    latency: 0,
  });

  const { 
    lastJsonMessage, 
    connectionStatus, 
    isConnected, 
    sendJsonMessage 
  } = useCrawlerWebSocket(
    taskId,
    {
      onOpen: () => {
        console.log(`🔗 WebSocket连接建立: taskId=${taskId}`);
        setConnectionMetrics(prev => ({
          ...prev,
          connectedAt: new Date(),
          reconnectCount: prev.reconnectCount + 1,
        }));
      },
      onClose: () => {
        console.log(`❌ WebSocket连接关闭: taskId=${taskId}`);
      },
      onError: (error) => {
        console.error(`💥 WebSocket错误: taskId=${taskId}`, error);
        crawlerStore.setError('WebSocket连接错误，请检查网络连接');
      },
      onMessage: () => {
        setConnectionMetrics(prev => ({
          ...prev,
          lastMessageAt: new Date(),
          messageCount: prev.messageCount + 1,
        }));
      },
    }
  );

  // 处理WebSocket消息
  const handleWebSocketMessage = useCallback((message: CrawlerWebSocketMessage) => {
    console.log(`📨 处理WebSocket消息: ${message.type}`, message.data);

    switch (message.type) {
      case 'connected':
        console.log('✅ WebSocket连接确认:', message.data);
        break;

      case 'progress':
        crawlerStore.updateProgress({
          current: message.data.current,
          total: message.data.total,
          percentage: message.data.percentage,
        });
        break;

      case 'data':
        if (Array.isArray(message.data) && message.data.length > 0) {
          crawlerStore.addProducts(message.data, message.keyword);
          console.log(`📦 收到 ${message.data.length} 条新数据`, 
            message.keyword ? `关键词: ${message.keyword}` : '');
        }
        break;

      case 'keyword_started':
        if (message.keyword) {
          crawlerStore.updateKeywordStatus(message.keyword, 'started', {
            startTime: new Date().toISOString(),
          });
          console.log(`🎯 开始处理关键词: ${message.keyword}`);
        }
        break;

      case 'keyword_completed':
        if (message.keyword) {
          crawlerStore.updateKeywordStatus(message.keyword, 'completed', {
            endTime: new Date().toISOString(),
            current: message.data?.collected_count || 0,
          });
          console.log(`✅ 完成关键词: ${message.keyword}, 收集 ${message.data?.collected_count || 0} 条`);
        }
        break;

      case 'completed':
        crawlerStore.setTaskCompleted(message.data);
        console.log('🎉 任务完成:', message.data);
        break;

      case 'error':
        const errorMessage = message.data?.message || '爬虫任务出错';
        crawlerStore.setError(errorMessage);
        console.error('❌ 任务错误:', errorMessage);
        break;

      case 'heartbeat':
        console.debug('💓 心跳消息');
        break;

      case 'pong':
        if (message.timestamp) {
          const latency = Date.now() - message.timestamp;
          setConnectionMetrics(prev => ({ ...prev, latency }));
        }
        break;

      default:
        console.warn('⚠️ 未知消息类型:', message.type);
    }
  }, [crawlerStore]);

  // 处理收到的消息
  useEffect(() => {
    if (lastJsonMessage) {
      handleWebSocketMessage(lastJsonMessage as CrawlerWebSocketMessage);
    }
  }, [lastJsonMessage, handleWebSocketMessage]);

  // 发送心跳
  const sendHeartbeat = useCallback(() => {
    if (isConnected) {
      sendJsonMessage({ 
        type: 'ping', 
        timestamp: Date.now() 
      });
    }
  }, [isConnected, sendJsonMessage]);

  return {
    connectionStatus,
    isConnected,
    metrics: connectionMetrics,
    sendHeartbeat,
  };
}
```

#### 5.2 进度监控组件

**src/components/crawler/ProgressMonitor/index.tsx**
```typescript
import React from 'react';
import { Card, Progress, Row, Col, Statistic, Tag, Badge, Button, Space } from 'antd';
import { 
  ClockCircleOutlined, 
  CheckCircleOutlined, 
  SyncOutlined,
  ExclamationCircleOutlined,
  WifiOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { useCrawlerStore } from '@/stores/crawlerStore';
import { useWebSocketHandler } from '@/hooks/useWebSocketHandler';
import { KeywordProgressTabs } from './KeywordProgressTabs';
import { ConnectionStatus } from './ConnectionStatus';
import styles from './index.module.css';

export const ProgressMonitor: React.FC = () => {
  const {
    currentTask,
    progress,
    isRunning,
    isPaused,
    products,
    productsByKeyword,
  } = useCrawlerStore();

  const { 
    connectionStatus, 
    isConnected, 
    metrics, 
    sendHeartbeat 
  } = useWebSocketHandler(currentTask?.id || null);

  // 计算预计完成时间
  const estimateCompletion = () => {
    if (!isRunning || progress.current === 0) return null;

    const elapsed = Date.now() - (currentTask?.startTime ? new Date(currentTask.startTime).getTime() : Date.now());
    const rate = progress.current / elapsed; // 条/毫秒
    const remaining = progress.total - progress.current;
    const eta = remaining / rate;

    return new Date(Date.now() + eta);
  };

  const etaTime = estimateCompletion();

  // 连接状态颜色
  const getConnectionColor = () => {
    switch (connectionStatus) {
      case 'connected': return 'green';
      case 'connecting': return 'orange';
      case 'closed': return 'red';
      default: return 'gray';
    }
  };

  // 任务状态标签
  const getStatusTag = () => {
    if (!currentTask) return null;

    const statusConfig = {
      running: { color: 'processing', icon: <SyncOutlined spin />, text: '运行中' },
      paused: { color: 'warning', icon: <ExclamationCircleOutlined />, text: '已暂停' },
      completed: { color: 'success', icon: <CheckCircleOutlined />, text: '已完成' },
      failed: { color: 'error', icon: <ExclamationCircleOutlined />, text: '失败' },
      stopped: { color: 'default', icon: <ExclamationCircleOutlined />, text: '已停止' },
    };

    const config = statusConfig[currentTask.status] || statusConfig.running;

    return (
      <Tag color={config.color} icon={config.icon}>
        {config.text}
      </Tag>
    );
  };

  if (!currentTask) {
    return (
      <Card title="进度监控">
        <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>
          暂无进行中的任务
        </div>
      </Card>
    );
  }

  return (
    <div className={styles.progressMonitor}>
      {/* 总体进度卡片 */}
      <Card 
        title={
          <Space>
            进度监控
            {getStatusTag()}
          </Space>
        }
        extra={
          <ConnectionStatus
            status={connectionStatus}
            isConnected={isConnected}
            metrics={metrics}
            onReconnect={sendHeartbeat}
          />
        }
      >
        {/* 主要进度条 */}
        <div className={styles.mainProgress}>
          <Progress
            percent={Math.round(progress.percentage)}
            status={isPaused ? 'exception' : isRunning ? 'active' : 'success'}
            strokeWidth={20}
            format={(percent) => (
              <span style={{ fontSize: '16px', fontWeight: 'bold' }}>
                {percent}%
              </span>
            )}
          />
          
          <div className={styles.progressText}>
            已收集 <strong>{progress.current}</strong> / {progress.total} 条数据
            {progress.currentKeyword && (
              <span className={styles.currentKeyword}>
                当前关键词: <Tag color="blue">{progress.currentKeyword}</Tag>
              </span>
            )}
          </div>
        </div>

        {/* 统计信息 */}
        <Row gutter={16} className={styles.statistics}>
          <Col span={6}>
            <Statistic
              title="已收集"
              value={progress.current}
              suffix="条"
              valueStyle={{ color: '#3f8600' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="目标数量"
              value={progress.total}
              suffix="条"
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="完成度"
              value={progress.percentage}
              precision={1}
              suffix="%"
              valueStyle={{ color: progress.percentage >= 100 ? '#3f8600' : '#1890ff' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="预计完成"
              value={etaTime ? etaTime.toLocaleTimeString() : '--'}
              prefix={<ClockCircleOutlined />}
            />
          </Col>
        </Row>

        {/* 关键词进度 */}
        {Object.keys(progress.keywordProgress).length > 0 && (
          <KeywordProgressTabs
            keywordProgress={progress.keywordProgress}
            productsByKeyword={productsByKeyword}
          />
        )}
      </Card>

      {/* 实时统计 */}
      <Card title="实时统计" size="small" className={styles.realtimeStats}>
        <Row gutter={16}>
          <Col span={8}>
            <Statistic
              title="爬取速度"
              value={metrics.messageCount > 0 ? (progress.current / (metrics.messageCount / 60)) : 0}
              precision={1}
              suffix="条/分钟"
              valueStyle={{ fontSize: '14px' }}
            />
          </Col>
          <Col span={8}>
            <Statistic
              title="连接延迟"
              value={metrics.latency}
              suffix="ms"
              valueStyle={{ 
                fontSize: '14px',
                color: metrics.latency > 1000 ? '#cf1322' : '#3f8600'
              }}
            />
          </Col>
          <Col span={8}>
            <Statistic
              title="消息数量"
              value={metrics.messageCount}
              suffix="条"
              valueStyle={{ fontSize: '14px' }}
            />
          </Col>
        </Row>
      </Card>
    </div>
  );
};
```

### Phase 6: 最终集成和测试（第13-14天）

#### 6.1 主应用组件

**src/App.tsx**
```typescript
import React, { Suspense } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ConfigProvider, App as AntApp, Spin } from 'antd';
import { ErrorBoundary } from 'react-error-boundary';
import zhCN from 'antd/locale/zh_CN';
import { Layout } from '@/components/common/Layout';
import { ErrorFallback } from '@/components/common/ErrorBoundary';
import { CrawlerDashboard } from '@/pages/CrawlerDashboard';
import '@/styles/globals.css';

// 懒加载页面组件
const CookieManagement = React.lazy(() => import('@/pages/CookieManagement'));

function App() {
  return (
    <ConfigProvider 
      locale={zhCN}
      theme={{
        token: {
          colorPrimary: '#ff5c00',
          borderRadius: 6,
        },
      }}
    >
      <AntApp>
        <ErrorBoundary FallbackComponent={ErrorFallback}>
          <Router>
            <Layout>
              <Suspense fallback={<Spin size="large" style={{ display: 'block', margin: '200px auto' }} />}>
                <Routes>
                  <Route path="/" element={<CrawlerDashboard />} />
                  <Route path="/cookies" element={<CookieManagement />} />
                </Routes>
              </Suspense>
            </Layout>
          </Router>
        </ErrorBoundary>
      </AntApp>
    </ConfigProvider>
  );
}

export default App;
```

**src/pages/CrawlerDashboard.tsx**
```typescript
import React from 'react';
import { Row, Col, Space } from 'antd';
import { CrawlControl } from '@/components/crawler/CrawlControl';
import { ProgressMonitor } from '@/components/crawler/ProgressMonitor';
import { DataPreview } from '@/components/crawler/DataPreview';
import { useCrawlerStore } from '@/stores/crawlerStore';

export const CrawlerDashboard: React.FC = () => {
  const { currentTask } = useCrawlerStore();

  return (
    <div style={{ padding: '24px' }}>
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        {/* 控制面板 */}
        <Row gutter={24}>
          <Col span={24}>
            <CrawlControl />
          </Col>
        </Row>

        {/* 进度监控 */}
        {currentTask && (
          <Row gutter={24}>
            <Col span={24}>
              <ProgressMonitor />
            </Col>
          </Row>
        )}

        {/* 数据预览 */}
        <Row gutter={24}>
          <Col span={24}>
            <DataPreview />
          </Col>
        </Row>
      </Space>
    </div>
  );
};
```

## 🧪 测试策略

### 单元测试示例

**src/components/crawler/CrawlControl/__tests__/KeywordInput.test.tsx**
```typescript
import { render, screen, fireEvent } from '@testing-library/react';
import { KeywordInput } from '../KeywordInput';

describe('KeywordInput', () => {
  test('should parse comma-separated keywords', () => {
    const onChange = jest.fn();
    render(<KeywordInput onChange={onChange} />);
    
    const input = screen.getByRole('combobox');
    fireEvent.change(input, { target: { value: '手机,电脑,平板' } });
    
    expect(onChange).toHaveBeenCalledWith(['手机', '电脑', '平板']);
  });

  test('should handle multiple separators', () => {
    const onChange = jest.fn();
    render(<KeywordInput onChange={onChange} />);
    
    const input = screen.getByRole('combobox');
    fireEvent.change(input, { target: { value: '手机；电脑\n平板 耳机' } });
    
    expect(onChange).toHaveBeenCalledWith(['手机', '电脑', '平板', '耳机']);
  });

  test('should remove duplicates', () => {
    const onChange = jest.fn();
    render(<KeywordInput onChange={onChange} />);
    
    const input = screen.getByRole('combobox');
    fireEvent.change(input, { target: { value: '手机,手机,电脑' } });
    
    expect(onChange).toHaveBeenCalledWith(['手机', '电脑']);
  });
});
```

### E2E测试示例

**cypress/e2e/crawler-workflow.cy.ts**
```typescript
describe('Crawler Workflow', () => {
  beforeEach(() => {
    cy.visit('/');
  });

  it('should complete full crawl workflow', () => {
    // 1. 输入关键词
    cy.get('[data-testid=keyword-input]').type('手机');
    cy.get('[data-testid=target-count]').clear().type('10');
    
    // 2. 开始爬取
    cy.get('[data-testid=start-crawl-btn]').click();
    
    // 3. 等待WebSocket连接
    cy.get('[data-testid=websocket-status]').should('contain', '已连接');
    
    // 4. 监控进度
    cy.get('[data-testid=progress-bar]', { timeout: 60000 })
      .should('have.attr', 'aria-valuenow')
      .and('match', /^(100|[1-9]\d?)$/);
    
    // 5. 验证数据
    cy.get('[data-testid=product-table]').should('be.visible');
    cy.get('[data-testid=product-count]').should('not.contain', '0');
    
    // 6. 导出数据
    cy.get('[data-testid=export-excel-btn]').click();
    cy.get('[data-testid=download-link]').should('be.visible');
  });

  it('should handle WebSocket reconnection', () => {
    // 开始任务
    cy.get('[data-testid=keyword-input]').type('测试');
    cy.get('[data-testid=start-crawl-btn]').click();
    
    // 模拟网络中断
    cy.window().then((win) => {
      win.navigator.onLine = false;
      win.dispatchEvent(new Event('offline'));
    });
    
    // 检查连接状态
    cy.get('[data-testid=websocket-status]').should('contain', '连接中断');
    
    // 恢复网络
    cy.window().then((win) => {
      win.navigator.onLine = true;
      win.dispatchEvent(new Event('online'));
    });
    
    // 验证重连
    cy.get('[data-testid=websocket-status]').should('contain', '已连接');
  });
});
```

## 📋 部署清单

### 构建脚本

**package.json**
```json
{
  "scripts": {
    "dev": "vite",
    "build": "tsc && vite build",
    "preview": "vite preview",
    "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0",
    "lint:fix": "eslint . --ext ts,tsx --fix",
    "test": "vitest",
    "test:ui": "vitest --ui",
    "test:e2e": "cypress run",
    "test:e2e:open": "cypress open",
    "type-check": "tsc --noEmit"
  }
}
```

### 部署配置

**nginx.conf**
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /usr/share/nginx/html;
    index index.html;

    # 前端路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }

    # API代理
    location /api/ {
        proxy_pass http://backend:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    # WebSocket代理
    location /ws/ {
        proxy_pass http://backend:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
    }

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

## 🎯 关键检查点

### 开发完成检查

- [ ] 所有API接口调用成功
- [ ] WebSocket连接稳定，消息处理正确
- [ ] 关键词解析支持多种分隔符
- [ ] Cookie管理功能完整
- [ ] 数据预览实时更新
- [ ] 导出功能与后端一致
- [ ] 响应式设计适配各设备
- [ ] 错误处理和用户提示完善
- [ ] 性能优化（懒加载、虚拟滚动等）
- [ ] 单元测试覆盖率 ≥80%
- [ ] E2E测试通过核心流程
- [ ] 跨浏览器兼容性测试通过

### 上线前检查

- [ ] 生产环境配置正确
- [ ] 静态资源CDN配置
- [ ] HTTPS证书配置
- [ ] WebSocket WSS配置
- [ ] 监控和日志配置
- [ ] 性能优化验证
- [ ] 安全检查通过

这个实现指南提供了详细的分阶段开发计划，确保团队能够按照标准化流程构建高质量的前端应用。