<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据一致性测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-result {
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            margin-bottom: 16px;
            overflow: hidden;
        }
        .test-header {
            background: #fafafa;
            padding: 12px 16px;
            border-bottom: 1px solid #d9d9d9;
            font-weight: bold;
        }
        .test-content {
            padding: 16px;
        }
        .success {
            background: #f6ffed;
            color: #52c41a;
            border-color: #b7eb8f;
        }
        .failure {
            background: #fff2f0;
            color: #ff4d4f;
            border-color: #ffccc7;
        }
        .field-comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            margin: 16px 0;
        }
        .backend-data, .frontend-data {
            padding: 12px;
            border-radius: 4px;
            border: 1px solid #d9d9d9;
        }
        .backend-data {
            background: #e6f7ff;
        }
        .frontend-data {
            background: #f6ffed;
        }
        .field-name {
            font-weight: bold;
            margin-bottom: 8px;
            color: #1890ff;
        }
        pre {
            background: #f5f5f5;
            padding: 8px;
            border-radius: 4px;
            font-size: 12px;
            overflow-x: auto;
        }
        .stats {
            background: #e6f7ff;
            padding: 16px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #40a9ff;
        }
        .api-test {
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            margin-bottom: 16px;
            padding: 16px;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-connected { background: #52c41a; }
        .status-disconnected { background: #ff4d4f; }
        .status-connecting { background: #faad14; }
    </style>
</head>
<body>
    <h1>数据格式一致性测试</h1>
    
    <div class="stats" id="connectionStatus">
        <h3>连接状态</h3>
        <div>
            <span class="status-indicator status-disconnected" id="apiStatus"></span>
            <span>API服务器: <span id="apiStatusText">未连接</span></span>
        </div>
        <div style="margin-top: 8px;">
            <span class="status-indicator status-disconnected" id="wsStatus"></span>
            <span>WebSocket: <span id="wsStatusText">未连接</span></span>
        </div>
    </div>

    <div class="test-container">
        <h2>API 接口测试</h2>
        <button onclick="testHealthCheck()">健康检查</button>
        <button onclick="testCookieStatus()">Cookie状态</button>
        <button onclick="testProductsAPI()">产品数据API</button>
        <button onclick="testExportAPI()">导出API</button>
        <div id="apiResults"></div>
    </div>

    <div class="test-container">
        <h2>数据字段一致性测试</h2>
        <button onclick="testFieldConsistency()">测试字段一致性</button>
        <div id="fieldResults"></div>
    </div>

    <div class="test-container">
        <h2>WebSocket 消息格式测试</h2>
        <button onclick="testWebSocketConnection()">测试WebSocket连接</button>
        <button onclick="disconnectWebSocket()">断开连接</button>
        <div id="wsResults"></div>
    </div>

    <div class="test-container">
        <h2>补贴信息一致性测试</h2>
        <button onclick="testSubsidyConsistency()">测试补贴字段</button>
        <div id="subsidyResults"></div>
    </div>

    <script>
        let ws = null;
        let wsConnectAttempts = 0;
        const maxWsConnectAttempts = 3;

        // 测试后端健康检查
        async function testHealthCheck() {
            const result = document.getElementById('apiResults');
            result.innerHTML = '<div class="api-test">正在测试健康检查...</div>';

            try {
                const response = await fetch('/api/health');
                const data = await response.json();
                
                if (response.ok) {
                    updateConnectionStatus('api', true);
                    result.innerHTML = `
                        <div class="test-result success">
                            <div class="test-header">✅ 健康检查成功</div>
                            <div class="test-content">
                                <p><strong>状态:</strong> ${data.status}</p>
                                <p><strong>时间:</strong> ${data.timestamp}</p>
                                <pre>${JSON.stringify(data, null, 2)}</pre>
                            </div>
                        </div>
                    `;
                } else {
                    throw new Error(`HTTP ${response.status}: ${data.message || '未知错误'}`);
                }
            } catch (error) {
                updateConnectionStatus('api', false);
                result.innerHTML = `
                    <div class="test-result failure">
                        <div class="test-header">❌ 健康检查失败</div>
                        <div class="test-content">
                            <p><strong>错误:</strong> ${error.message}</p>
                        </div>
                    </div>
                `;
            }
        }

        // 测试Cookie状态API
        async function testCookieStatus() {
            const result = document.getElementById('apiResults');
            result.innerHTML = '<div class="api-test">正在测试Cookie状态...</div>';

            try {
                const response = await fetch('/api/cookie/status');
                const data = await response.json();
                
                result.innerHTML = `
                    <div class="test-result ${data.success ? 'success' : 'failure'}">
                        <div class="test-header">${data.success ? '✅' : '❌'} Cookie状态测试</div>
                        <div class="test-content">
                            <div class="field-comparison">
                                <div class="backend-data">
                                    <div class="field-name">后端响应格式</div>
                                    <pre>${JSON.stringify(data, null, 2)}</pre>
                                </div>
                                <div class="frontend-data">
                                    <div class="field-name">前端期望格式</div>
                                    <pre>{
  "success": boolean,
  "data": {
    "exists": boolean,
    "valid": boolean,
    "cookies": Cookie[]
  }
}</pre>
                                </div>
                            </div>
                            <p><strong>字段匹配:</strong> ${checkCookieFieldsMatch(data) ? '✅ 匹配' : '❌ 不匹配'}</p>
                        </div>
                    </div>
                `;
            } catch (error) {
                result.innerHTML = `
                    <div class="test-result failure">
                        <div class="test-header">❌ Cookie状态测试失败</div>
                        <div class="test-content">
                            <p><strong>错误:</strong> ${error.message}</p>
                        </div>
                    </div>
                `;
            }
        }

        // 测试产品数据API
        async function testProductsAPI() {
            const result = document.getElementById('apiResults');
            result.innerHTML = '<div class="api-test">正在测试产品数据API...</div>';

            try {
                const response = await fetch('/api/products?page=1&pageSize=5');
                const data = await response.json();
                
                result.innerHTML = `
                    <div class="test-result ${data.success ? 'success' : 'failure'}">
                        <div class="test-header">${data.success ? '✅' : '❌'} 产品数据API测试</div>
                        <div class="test-content">
                            <p><strong>返回数据条数:</strong> ${data.data?.products?.length || 0}</p>
                            <p><strong>总数:</strong> ${data.data?.total || 0}</p>
                            ${data.data?.products?.[0] ? `
                            <div class="field-comparison">
                                <div class="backend-data">
                                    <div class="field-name">后端产品数据样例</div>
                                    <pre>${JSON.stringify(data.data.products[0], null, 2)}</pre>
                                </div>
                                <div class="frontend-data">
                                    <div class="field-name">关键字段检查</div>
                                    <p>✅ goods_id: ${data.data.products[0].goods_id ? '存在' : '缺失'}</p>
                                    <p>✅ goods_name: ${data.data.products[0].goods_name ? '存在' : '缺失'}</p>
                                    <p>🎯 subsidy_info: ${data.data.products[0].subsidy_info !== undefined ? '存在' : '缺失'}</p>
                                    <p>💰 price: ${data.data.products[0].price !== undefined ? '存在' : '缺失'}</p>
                                </div>
                            </div>
                            ` : '<p>无产品数据</p>'}
                        </div>
                    </div>
                `;
            } catch (error) {
                result.innerHTML = `
                    <div class="test-result failure">
                        <div class="test-header">❌ 产品数据API测试失败</div>
                        <div class="test-content">
                            <p><strong>错误:</strong> ${error.message}</p>
                        </div>
                    </div>
                `;
            }
        }

        // 测试字段一致性
        function testFieldConsistency() {
            const result = document.getElementById('fieldResults');
            
            // 后端字段（基于exporter.py中的column_mapping）
            const backendFields = {
                "goods_id": "商品ID",
                "goods_name": "商品名称", 
                "keyword": "搜索关键词",
                "price": "拼团价(元)",
                "original_price": "原价(元)",
                "coupon_price": "券后价(元)",
                "market_price": "市场价(元)",
                "price_type_name": "价格类型",
                "sales": "销量",
                "comment_count": "评论数",
                "rating": "评分",
                "sales_tip": "销量提示",
                "brand_name": "品牌名称",
                "category": "商品分类",
                "subsidy_info": "补贴详情",
                "activity_type_name": "活动类型",
                "merchant_type_name": "商家类型",
                "marketing_tags": "营销标签",
                "tags": "商品标签",
                "special_text": "特殊信息",
                "image_url": "商品图片",
                "hd_thumb_url": "高清图片",
                "goods_url": "商品链接",
                "created_time": "采集时间"
            };

            // 前端字段（基于类型定义）
            const frontendFields = [
                "goods_id", "goods_name", "keyword",
                "price", "original_price", "coupon_price", "market_price", "price_type_name",
                "sales", "comment_count", "rating", "sales_tip",
                "brand_name", "category",
                "subsidy_info",
                "activity_type_name", "merchant_type_name",
                "marketing_tags", "tags", "special_text",
                "image_url", "hd_thumb_url", "goods_url",
                "created_time"
            ];

            // 检查一致性
            const missingInFrontend = Object.keys(backendFields).filter(field => !frontendFields.includes(field));
            const missingInBackend = frontendFields.filter(field => !backendFields.hasOwnProperty(field));
            const consistentFields = Object.keys(backendFields).filter(field => frontendFields.includes(field));

            result.innerHTML = `
                <div class="test-result ${missingInFrontend.length === 0 && missingInBackend.length === 0 ? 'success' : 'failure'}">
                    <div class="test-header">
                        ${missingInFrontend.length === 0 && missingInBackend.length === 0 ? '✅' : '❌'} 字段一致性检查
                    </div>
                    <div class="test-content">
                        <div class="field-comparison">
                            <div class="backend-data">
                                <div class="field-name">后端支持字段 (${Object.keys(backendFields).length}个)</div>
                                <pre>${Object.keys(backendFields).join('\n')}</pre>
                            </div>
                            <div class="frontend-data">
                                <div class="field-name">前端支持字段 (${frontendFields.length}个)</div>
                                <pre>${frontendFields.join('\n')}</pre>
                            </div>
                        </div>
                        <p><strong>一致字段:</strong> ${consistentFields.length} 个</p>
                        <p><strong>前端缺失:</strong> ${missingInFrontend.length > 0 ? missingInFrontend.join(', ') : '无'}</p>
                        <p><strong>后端缺失:</strong> ${missingInBackend.length > 0 ? missingInBackend.join(', ') : '无'}</p>
                        <p><strong>关键字段 subsidy_info:</strong> ${consistentFields.includes('subsidy_info') ? '✅ 一致' : '❌ 不一致'}</p>
                    </div>
                </div>
            `;
        }

        // 测试WebSocket连接
        function testWebSocketConnection() {
            const result = document.getElementById('wsResults');
            result.innerHTML = '<div class="api-test">正在测试WebSocket连接...</div>';

            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.close();
            }

            wsConnectAttempts++;
            const wsUrl = `ws://localhost:8000/ws/crawl/test-${Date.now()}`;
            
            try {
                ws = new WebSocket(wsUrl);
                
                ws.onopen = function(event) {
                    updateConnectionStatus('ws', true);
                    result.innerHTML = `
                        <div class="test-result success">
                            <div class="test-header">✅ WebSocket连接成功</div>
                            <div class="test-content">
                                <p><strong>连接URL:</strong> ${wsUrl}</p>
                                <p><strong>连接状态:</strong> ${ws.readyState === WebSocket.OPEN ? '已连接' : '连接中'}</p>
                                <p><strong>等待消息...</strong></p>
                            </div>
                        </div>
                    `;
                };

                ws.onmessage = function(event) {
                    try {
                        const message = JSON.parse(event.data);
                        const currentResult = result.innerHTML;
                        result.innerHTML = currentResult + `
                            <div class="test-result success">
                                <div class="test-header">📨 收到WebSocket消息</div>
                                <div class="test-content">
                                    <div class="field-comparison">
                                        <div class="backend-data">
                                            <div class="field-name">实际消息格式</div>
                                            <pre>${JSON.stringify(message, null, 2)}</pre>
                                        </div>
                                        <div class="frontend-data">
                                            <div class="field-name">前端期望格式</div>
                                            <pre>{
  "type": "status|progress|data|error|...",
  "data": {...}  // 注意：前端期望data，不是payload
}</pre>
                                        </div>
                                    </div>
                                    <p><strong>格式检查:</strong> ${checkWebSocketFormat(message) ? '✅ 格式正确' : '❌ 格式不匹配'}</p>
                                </div>
                            </div>
                        `;
                    } catch (error) {
                        result.innerHTML += `
                            <div class="test-result failure">
                                <div class="test-header">❌ 消息解析失败</div>
                                <div class="test-content">
                                    <p><strong>原始消息:</strong> ${event.data}</p>
                                    <p><strong>错误:</strong> ${error.message}</p>
                                </div>
                            </div>
                        `;
                    }
                };

                ws.onerror = function(error) {
                    updateConnectionStatus('ws', false);
                    result.innerHTML = `
                        <div class="test-result failure">
                            <div class="test-header">❌ WebSocket连接错误</div>
                            <div class="test-content">
                                <p><strong>连接URL:</strong> ${wsUrl}</p>
                                <p><strong>错误:</strong> 连接失败</p>
                                <p><strong>建议:</strong> 确保后端服务运行在8000端口</p>
                            </div>
                        </div>
                    `;
                };

                ws.onclose = function(event) {
                    updateConnectionStatus('ws', false);
                    if (wsConnectAttempts <= maxWsConnectAttempts) {
                        result.innerHTML += `
                            <div class="test-result failure">
                                <div class="test-header">❌ WebSocket连接关闭</div>
                                <div class="test-content">
                                    <p><strong>关闭码:</strong> ${event.code}</p>
                                    <p><strong>原因:</strong> ${event.reason || '未知'}</p>
                                    <p><strong>是否正常关闭:</strong> ${event.wasClean ? '是' : '否'}</p>
                                </div>
                            </div>
                        `;
                    }
                };

                // 5秒后发送测试消息
                setTimeout(() => {
                    if (ws && ws.readyState === WebSocket.OPEN) {
                        ws.send(JSON.stringify({
                            type: 'ping',
                            timestamp: new Date().toISOString()
                        }));
                    }
                }, 2000);

            } catch (error) {
                updateConnectionStatus('ws', false);
                result.innerHTML = `
                    <div class="test-result failure">
                        <div class="test-header">❌ WebSocket创建失败</div>
                        <div class="test-content">
                            <p><strong>错误:</strong> ${error.message}</p>
                        </div>
                    </div>
                `;
            }
        }

        // 断开WebSocket连接
        function disconnectWebSocket() {
            if (ws) {
                ws.close();
                ws = null;
                updateConnectionStatus('ws', false);
            }
        }

        // 测试补贴信息一致性
        function testSubsidyConsistency() {
            const result = document.getElementById('subsidyResults');
            
            // 模拟测试数据
            const testCases = [
                {
                    name: "百亿补贴商品",
                    backendData: { subsidy_info: "百亿补贴" },
                    expected: { hasSubsidy: true, displayText: "百亿补贴" }
                },
                {
                    name: "国补商品", 
                    backendData: { subsidy_info: "国补" },
                    expected: { hasSubsidy: true, displayText: "国补" }
                },
                {
                    name: "无补贴商品",
                    backendData: { subsidy_info: "" },
                    expected: { hasSubsidy: false, displayText: "" }
                },
                {
                    name: "复合补贴",
                    backendData: { subsidy_info: "百亿补贴, 限时优惠" },
                    expected: { hasSubsidy: true, displayText: "百亿补贴, 限时优惠" }
                }
            ];

            let results = testCases.map(testCase => {
                const hasSubsidy = !!(testCase.backendData.subsidy_info?.trim());
                const displayText = testCase.backendData.subsidy_info || "";
                
                const passed = hasSubsidy === testCase.expected.hasSubsidy && 
                              displayText === testCase.expected.displayText;

                return {
                    ...testCase,
                    actual: { hasSubsidy, displayText },
                    passed
                };
            });

            const passedCount = results.filter(r => r.passed).length;
            const totalCount = results.length;

            result.innerHTML = `
                <div class="test-result ${passedCount === totalCount ? 'success' : 'failure'}">
                    <div class="test-header">
                        ${passedCount === totalCount ? '✅' : '❌'} 补贴字段一致性测试 (${passedCount}/${totalCount} 通过)
                    </div>
                    <div class="test-content">
                        ${results.map(r => `
                            <div style="border: 1px solid #ddd; margin: 8px 0; padding: 12px; border-radius: 4px; ${r.passed ? 'background: #f6ffed;' : 'background: #fff2f0;'}">
                                <h4>${r.passed ? '✅' : '❌'} ${r.name}</h4>
                                <div class="field-comparison">
                                    <div class="backend-data">
                                        <div class="field-name">后端数据</div>
                                        <pre>${JSON.stringify(r.backendData, null, 2)}</pre>
                                    </div>
                                    <div class="frontend-data">
                                        <div class="field-name">前端处理结果</div>
                                        <p>hasSubsidy: ${r.actual.hasSubsidy}</p>
                                        <p>displayText: "${r.actual.displayText}"</p>
                                    </div>
                                </div>
                                <p><strong>期望:</strong> hasSubsidy=${r.expected.hasSubsidy}, displayText="${r.expected.displayText}"</p>
                                <p><strong>实际:</strong> hasSubsidy=${r.actual.hasSubsidy}, displayText="${r.actual.displayText}"</p>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        }

        // 辅助函数
        function checkCookieFieldsMatch(data) {
            return data.hasOwnProperty('success') && 
                   data.data && 
                   data.data.hasOwnProperty('exists') && 
                   data.data.hasOwnProperty('valid') && 
                   Array.isArray(data.data.cookies);
        }

        function checkWebSocketFormat(message) {
            return message.hasOwnProperty('type') && 
                   (message.hasOwnProperty('data') || message.hasOwnProperty('payload'));
        }

        function updateConnectionStatus(type, connected) {
            const statusId = type + 'Status';
            const textId = type + 'StatusText';
            const statusElement = document.getElementById(statusId);
            const textElement = document.getElementById(textId);
            
            if (statusElement && textElement) {
                statusElement.className = 'status-indicator ' + (connected ? 'status-connected' : 'status-disconnected');
                textElement.textContent = connected ? '已连接' : '未连接';
            }
        }

        // 页面加载时执行
        window.onload = function() {
            console.log('数据一致性测试页面已加载');
            
            // 自动执行健康检查
            setTimeout(() => {
                testHealthCheck();
            }, 1000);
        };

        // 页面卸载时清理
        window.onbeforeunload = function() {
            disconnectWebSocket();
        };
    </script>
</body>
</html>