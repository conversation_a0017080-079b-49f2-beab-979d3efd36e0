// 搜索配置相关类型定义
export interface SearchConfig {
  keywords: string[];
  targetCount: number; // 每个关键词的目标数量
  sortMethod: 'sales' | 'price_asc' | 'price_desc' | 'rating' | 'default'; // 改latest为default(综合排序)
  headless: boolean;
  enableFilter: boolean;
  // 过滤条件
  priceRange?: [number, number];
  minRating?: number;
  minSoldCount?: number;
  excludeKeywords?: string[];
}

// 关键词输入组件的props
export interface KeywordInputProps {
  value: string[];
  onChange: (keywords: string[]) => void;
  placeholder?: string;
  maxCount?: number;
  disabled?: boolean;
}

// 参数设置组件的props
export interface ParameterSettingsProps {
  config: Pick<SearchConfig, 'targetCount' | 'sortMethod' | 'headless' | 'enableFilter'>;
  onChange: (config: Partial<SearchConfig>) => void;
  disabled?: boolean;
}

// 高级筛选组件的props
export interface AdvancedFiltersProps {
  filters: Pick<SearchConfig, 'priceRange' | 'minRating' | 'minSoldCount' | 'excludeKeywords'>;
  onChange: (filters: Partial<SearchConfig>) => void;
  disabled?: boolean;
}

// 搜索统计数据
export interface SearchStats {
  totalKeywords: number;
  estimatedResults: number;
  estimatedTime: number; // 预估时间（分钟）
  previousResults?: {
    keywords: string[];
    resultCount: number;
    timestamp: string;
  }[];
}

// 关键词历史记录
export interface KeywordHistory {
  keyword: string;
  usedCount: number;
  lastUsed: string;
  category?: string;
}

// 配置预设
export interface ConfigPreset {
  id: string;
  name: string;
  config: SearchConfig;
  createdAt: string;
  updatedAt: string;
}

// 排序选项
export interface SortOption {
  value: SearchConfig['sortMethod'];
  label: string;
  description: string;
}

// 关键词模板
export interface KeywordTemplate {
  id: string;
  name: string;
  keywords: string[];
  category: string;
  description?: string;
}