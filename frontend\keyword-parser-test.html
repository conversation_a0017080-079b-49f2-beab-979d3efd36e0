<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>关键词解析测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-case {
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            margin-bottom: 16px;
            overflow: hidden;
        }
        .test-header {
            background: #fafafa;
            padding: 12px 16px;
            border-bottom: 1px solid #d9d9d9;
            font-weight: bold;
        }
        .test-input {
            padding: 12px 16px;
            background: #f8f8f8;
            border-bottom: 1px solid #d9d9d9;
        }
        .test-expected {
            padding: 12px 16px;
            background: #f6ffed;
            border-bottom: 1px solid #d9d9d9;
        }
        .test-result {
            padding: 12px 16px;
        }
        .test-result.success {
            background: #f6ffed;
            color: #52c41a;
        }
        .test-result.failure {
            background: #fff2f0;
            color: #ff4d4f;
        }
        .keyword-tag {
            display: inline-block;
            background: #1890ff;
            color: white;
            padding: 2px 8px;
            border-radius: 4px;
            margin: 2px;
            font-size: 12px;
        }
        .expected-tag {
            background: #52c41a;
        }
        .result-tag {
            background: #ff7a45;
        }
        textarea {
            width: 100%;
            height: 100px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            padding: 8px;
            font-size: 14px;
        }
        button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
        }
        button:hover {
            background: #40a9ff;
        }
        .stats {
            background: #e6f7ff;
            padding: 16px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <h1>关键词解析准确性测试</h1>
    
    <div class="stats" id="stats">
        测试统计将显示在这里
    </div>

    <div class="test-container">
        <h2>自定义测试</h2>
        <textarea id="customInput" placeholder="请输入要测试的关键词文本，支持各种分隔符..."></textarea>
        <button onclick="testCustomInput()">测试解析</button>
        <div id="customResult"></div>
    </div>

    <div class="test-container">
        <h2>预设测试用例</h2>
        <div id="testResults"></div>
    </div>

    <script>
        // 多种分隔符的正则表达式 - 复制自前端代码
        const SEPARATORS_REGEX = /[,，;；\s]+/;

        /**
         * 智能解析关键词 - 复制自前端代码
         * 支持多种分隔符：中文逗号、英文逗号、空格、分号、换行符
         */
        function parseKeywords(input) {
            if (!input || typeof input !== 'string') {
                return [];
            }

            return input
                .split(SEPARATORS_REGEX)
                .map(keyword => keyword.trim())
                .filter(keyword => keyword.length > 0)
                .filter((keyword, index, array) => array.indexOf(keyword) === index); // 去重
        }

        // 测试用例
        const testCases = [
            {
                name: '英文逗号分隔',
                input: '手机,耳机,充电器,数据线',
                expected: ['手机', '耳机', '充电器', '数据线']
            },
            {
                name: '中文逗号分隔',
                input: '手机，耳机，充电器，数据线',
                expected: ['手机', '耳机', '充电器', '数据线']
            },
            {
                name: '空格分隔',
                input: '手机 耳机 充电器 数据线',
                expected: ['手机', '耳机', '充电器', '数据线']
            },
            {
                name: '英文分号分隔',
                input: '手机;耳机;充电器;数据线',
                expected: ['手机', '耳机', '充电器', '数据线']
            },
            {
                name: '中文分号分隔',
                input: '手机；耳机；充电器；数据线',
                expected: ['手机', '耳机', '充电器', '数据线']
            },
            {
                name: '换行符分隔',
                input: '手机\n耳机\n充电器\n数据线',
                expected: ['手机', '耳机', '充电器', '数据线']
            },
            {
                name: '混合分隔符',
                input: '手机,耳机；充电器 数据线\n移动电源，手机壳',
                expected: ['手机', '耳机', '充电器', '数据线', '移动电源', '手机壳']
            },
            {
                name: '多余空格处理',
                input: '  手机  ,  耳机  ;  充电器  ',
                expected: ['手机', '耳机', '充电器']
            },
            {
                name: '重复关键词去重',
                input: '手机,手机,耳机,手机,充电器',
                expected: ['手机', '耳机', '充电器']
            },
            {
                name: '空字符串和空关键词',
                input: '手机,,耳机, ,充电器,',
                expected: ['手机', '耳机', '充电器']
            },
            {
                name: '单个关键词',
                input: '手机',
                expected: ['手机']
            },
            {
                name: '空输入',
                input: '',
                expected: []
            },
            {
                name: '只有分隔符',
                input: ',;， ；',
                expected: []
            },
            {
                name: '特殊字符混合',
                input: 'iPhone 13,华为P50；小米12 OPPO R15\nvivo X70',
                expected: ['iPhone', '13', '华为P50', '小米12', 'OPPO', 'R15', 'vivo', 'X70']
            },
            {
                name: '长关键词',
                input: '苹果iPhone13ProMax手机,华为Mate40Pro',
                expected: ['苹果iPhone13ProMax手机', '华为Mate40Pro']
            }
        ];

        // 运行单个测试用例
        function runTestCase(testCase) {
            const result = parseKeywords(testCase.input);
            const success = JSON.stringify(result) === JSON.stringify(testCase.expected);
            
            return {
                ...testCase,
                result,
                success
            };
        }

        // 运行所有测试用例
        function runAllTests() {
            const results = testCases.map(runTestCase);
            const successCount = results.filter(r => r.success).length;
            const totalCount = results.length;
            
            // 更新统计信息
            document.getElementById('stats').innerHTML = `
                <strong>测试统计：</strong>
                总测试用例：${totalCount} | 
                通过：${successCount} | 
                失败：${totalCount - successCount} | 
                成功率：${((successCount / totalCount) * 100).toFixed(1)}%
            `;
            
            // 显示测试结果
            const resultsHtml = results.map(testResult => `
                <div class="test-case">
                    <div class="test-header">
                        ${testResult.name} 
                        ${testResult.success ? '✅' : '❌'}
                    </div>
                    <div class="test-input">
                        <strong>输入：</strong>${testResult.input || '(空字符串)'}
                    </div>
                    <div class="test-expected">
                        <strong>期望：</strong>
                        ${testResult.expected.map(k => `<span class="keyword-tag expected-tag">${k}</span>`).join('')}
                        ${testResult.expected.length === 0 ? '(空数组)' : ''}
                    </div>
                    <div class="test-result ${testResult.success ? 'success' : 'failure'}">
                        <strong>实际：</strong>
                        ${testResult.result.map(k => `<span class="keyword-tag result-tag">${k}</span>`).join('')}
                        ${testResult.result.length === 0 ? '(空数组)' : ''}
                        ${!testResult.success ? '<br><strong>❌ 解析结果不匹配</strong>' : '<br><strong>✅ 解析正确</strong>'}
                    </div>
                </div>
            `).join('');
            
            document.getElementById('testResults').innerHTML = resultsHtml;
        }

        // 测试自定义输入
        function testCustomInput() {
            const input = document.getElementById('customInput').value;
            const result = parseKeywords(input);
            
            const resultHtml = `
                <div class="test-case" style="margin-top: 16px;">
                    <div class="test-header">自定义测试结果</div>
                    <div class="test-input">
                        <strong>输入：</strong>${input || '(空字符串)'}
                    </div>
                    <div class="test-result success">
                        <strong>解析结果：</strong>
                        ${result.map(k => `<span class="keyword-tag">${k}</span>`).join('')}
                        ${result.length === 0 ? '(空数组)' : ''}
                        <br><strong>共解析出 ${result.length} 个关键词</strong>
                    </div>
                </div>
            `;
            
            document.getElementById('customResult').innerHTML = resultHtml;
        }

        // 页面加载时运行测试
        window.onload = function() {
            runAllTests();
        };
    </script>
</body>
</html>