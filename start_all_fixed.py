#!/usr/bin/env python3
"""
拼多多爬虫系统一键启动脚本（修复版）
同时启动前端和后端服务
"""

import os
import sys
import subprocess
import platform
import time
import threading
import webbrowser
from pathlib import Path

class ColorPrint:
    """彩色输出工具类"""
    HEADER = '\033[95m'
    OKBLUE = '\033[94m'
    OKCYAN = '\033[96m'
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    
    @staticmethod
    def success(msg):
        print(f"{ColorPrint.OKGREEN}✅ {msg}{ColorPrint.ENDC}")
    
    @staticmethod
    def error(msg):
        print(f"{ColorPrint.FAIL}❌ {msg}{ColorPrint.ENDC}")
    
    @staticmethod
    def warning(msg):
        print(f"{ColorPrint.WARNING}⚠️  {msg}{ColorPrint.ENDC}")
    
    @staticmethod
    def info(msg):
        print(f"{ColorPrint.OKCYAN}ℹ️  {msg}{ColorPrint.ENDC}")
    
    @staticmethod
    def header(msg):
        print(f"{ColorPrint.HEADER}{ColorPrint.BOLD}{msg}{ColorPrint.ENDC}")

def run_command(cmd, description=""):
    """运行命令的通用函数"""
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, shell=True)
        return result.returncode == 0, result.stdout.strip(), result.stderr.strip()
    except Exception as e:
        return False, "", str(e)

def check_environment():
    """检查运行环境"""
    print("\n📋 环境检查")
    print("-" * 40)
    
    # 检查Python
    python_version = f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
    ColorPrint.success(f"Python {python_version}")
    
    # 检查Node.js
    success, output, error = run_command('node --version')
    if success and output:
        ColorPrint.success(f"Node.js {output}")
    else:
        ColorPrint.error("Node.js未安装或无法访问")
        ColorPrint.info("请从 https://nodejs.org/ 下载安装Node.js")
        return False
    
    # 检查npm - 使用最简单的方式
    success, output, error = run_command('npm --version')
    if success and output:
        ColorPrint.success(f"npm {output}")
    else:
        # 在Windows上，npm通常随Node.js一起安装
        ColorPrint.warning("npm未检测到，但可能已安装")
        ColorPrint.info("尝试继续运行...")
    
    return True

def start_backend():
    """在新线程中启动后端"""
    try:
        ColorPrint.info("正在启动后端服务...")
        
        # 切换到项目根目录
        project_dir = Path(__file__).parent
        os.chdir(project_dir)
        
        # 检查backend目录是否存在
        backend_dir = project_dir / 'backend'
        if not backend_dir.exists():
            ColorPrint.error(f"后端目录不存在: {backend_dir}")
            return
        
        # 检查api_server.py是否存在
        api_file = backend_dir / 'api_server.py'
        if not api_file.exists():
            ColorPrint.error(f"API服务器文件不存在: {api_file}")
            return
        
        ColorPrint.success("后端文件检查通过")
        
        # 启动uvicorn
        cmd = f'{sys.executable} -m uvicorn backend.api_server:app --host 0.0.0.0 --port 8001 --reload'
        
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1,
            universal_newlines=True,
            shell=True
        )
        
        # 输出后端日志（带前缀）
        for line in process.stdout:
            print(f"[后端] {line}", end='')
        
    except Exception as e:
        ColorPrint.error(f"后端启动失败: {e}")

def start_frontend():
    """在新线程中启动前端"""
    try:
        ColorPrint.info("正在启动前端服务...")
        
        project_dir = Path(__file__).parent
        frontend_dir = project_dir / 'frontend'
        
        if not frontend_dir.exists():
            ColorPrint.error(f"前端目录不存在: {frontend_dir}")
            return
        
        # 检查依赖
        node_modules = frontend_dir / 'node_modules'
        if not node_modules.exists():
            ColorPrint.warning("前端依赖未安装，正在安装...")
            os.chdir(frontend_dir)
            
            # 使用简单的npm install命令
            ColorPrint.info("运行: npm install")
            result = subprocess.run('npm install', shell=True)
            if result.returncode == 0:
                ColorPrint.success("前端依赖安装完成")
            else:
                ColorPrint.error("前端依赖安装失败，请手动运行: cd frontend && npm install")
                return
        
        # 切换到前端目录
        os.chdir(frontend_dir)
        
        # 启动开发服务器
        ColorPrint.info("运行: npm run dev")
        cmd = 'npm run dev'
        
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1,
            universal_newlines=True,
            shell=True
        )
        
        # 输出前端日志（带前缀）
        for line in process.stdout:
            print(f"[前端] {line}", end='')
        
    except Exception as e:
        ColorPrint.error(f"前端启动失败: {e}")

def main():
    """主函数"""
    ColorPrint.header("\n🚀 拼多多爬虫系统启动器")
    ColorPrint.header("=" * 50)
    
    # 环境检查
    if not check_environment():
        ColorPrint.error("\n环境检查失败，请安装必要的依赖")
        sys.exit(1)
    
    ColorPrint.success("\n环境检查通过")
    
    # 显示信息
    print("\n" + "=" * 50)
    print("📌 服务地址：")
    print("   前端: http://localhost:5173")
    print("   后端API: http://localhost:8001")
    print("   API文档: http://localhost:8001/docs")
    print("=" * 50)
    
    # 创建线程启动服务
    backend_thread = threading.Thread(target=start_backend, daemon=True)
    frontend_thread = threading.Thread(target=start_frontend, daemon=True)
    
    # 启动线程
    backend_thread.start()
    time.sleep(2)  # 让后端先启动
    frontend_thread.start()
    
    # 等待服务启动
    print("\n⏳ 等待服务启动...")
    time.sleep(5)
    
    # 自动打开浏览器
    try:
        ColorPrint.info("正在打开浏览器...")
        webbrowser.open('http://localhost:5173')
    except:
        ColorPrint.warning("无法自动打开浏览器，请手动访问: http://localhost:5173")
    
    print("\n" + "=" * 50)
    ColorPrint.success("✨ 系统已启动！")
    print("按 Ctrl+C 停止所有服务")
    print("=" * 50 + "\n")
    
    # 保持主线程运行
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n")
        ColorPrint.warning("正在停止服务...")
        ColorPrint.success("服务已停止")
        sys.exit(0)

if __name__ == "__main__":
    main()