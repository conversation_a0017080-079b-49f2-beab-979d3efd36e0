/**
 * 数据预览工具函数
 */

import type { Product, DataPreviewStats, ColumnConfig } from '../../types';

// Define types locally since they are only used in this file
interface PriceStats {
  min: number;
  max: number;
  avg: number;
  median: number;
}

interface SalesStats {
  total: number;
  avg: number;
  median: number;
  topProducts: Product[];
}

interface BrandStats {
  total: number;
  distribution: Array<{
    brand: string;
    count: number;
    percentage: number;
  }>;
}

interface SubsidyStats {
  totalWithSubsidy: number;
  subsidyPercentage: number;
  govSubsidyCount: number;
  govSubsidyPercentage: number;
  subsidyTypes: Array<{
    type: string;
    count: number;
  }>;
}

/**
 * 计算价格统计
 */
export const calculatePriceStats = (products: Product[]): PriceStats => {
  const prices = products
    .map(p => Number(p.price) || 0)
    .filter(p => p > 0)
    .sort((a, b) => a - b);

  if (prices.length === 0) {
    return { min: 0, max: 0, avg: 0, median: 0 };
  }

  const min = prices[0];
  const max = prices[prices.length - 1];
  const avg = prices.reduce((sum, price) => sum + price, 0) / prices.length;
  const median = prices.length % 2 === 0 
    ? (prices[prices.length / 2 - 1] + prices[prices.length / 2]) / 2
    : prices[Math.floor(prices.length / 2)];

  return { min, max, avg, median };
};

/**
 * 计算销量统计
 */
export const calculateSalesStats = (products: Product[]): SalesStats => {
  const salesData = products.map(p => {
    let sales = 0;
    if (typeof p.sales === 'number') {
      sales = p.sales;
    } else if (typeof p.sales === 'string') {
      const salesStr = p.sales.toString();
      if (salesStr.includes('万')) {
        const match = salesStr.match(/(\d+(?:\.\d+)?)\s*万/);
        if (match) {
          sales = parseFloat(match[1]) * 10000;
        }
      } else {
        sales = parseInt(salesStr.replace(/[^\d]/g, '')) || 0;
      }
    }
    return { product: p, sales };
  }).filter(item => item.sales > 0);

  if (salesData.length === 0) {
    return { total: 0, avg: 0, median: 0, topProducts: [] };
  }

  const salesValues = salesData.map(item => item.sales).sort((a, b) => a - b);
  const total = salesValues.reduce((sum, sales) => sum + sales, 0);
  const avg = total / salesValues.length;
  const median = salesValues.length % 2 === 0 
    ? (salesValues[salesValues.length / 2 - 1] + salesValues[salesValues.length / 2]) / 2
    : salesValues[Math.floor(salesValues.length / 2)];

  // 获取销量前10的商品
  const topProducts = salesData
    .sort((a, b) => b.sales - a.sales)
    .slice(0, 10)
    .map(item => item.product);

  return { total, avg, median, topProducts };
};

/**
 * 计算品牌统计
 */
export const calculateBrandStats = (products: Product[]): BrandStats => {
  const brandCounts: Record<string, number> = {};
  let totalWithBrand = 0;

  products.forEach(product => {
    const brand = product.brand_name?.trim();
    if (brand && brand !== '未知' && brand !== '') {
      brandCounts[brand] = (brandCounts[brand] || 0) + 1;
      totalWithBrand++;
    }
  });

  const distribution = Object.entries(brandCounts)
    .map(([brand, count]) => ({
      brand,
      count,
      percentage: (count / totalWithBrand) * 100
    }))
    .sort((a, b) => b.count - a.count);

  return {
    total: Object.keys(brandCounts).length,
    distribution
  };
};

/**
 * 计算补贴统计
 */
export const calculateSubsidyStats = (products: Product[]): SubsidyStats => {
  let totalWithSubsidy = 0;
  let govSubsidyCount = 0;
  const subsidyTypes: Record<string, number> = {};

  products.forEach(product => {
    const subsidyInfo = product.subsidy_info?.trim();
    
    if (subsidyInfo && subsidyInfo !== '') {
      totalWithSubsidy++;
      
      // 统计补贴类型
      if (subsidyInfo.includes('百亿补贴')) {
        subsidyTypes['百亿补贴'] = (subsidyTypes['百亿补贴'] || 0) + 1;
      }
      if (subsidyInfo.includes('国补') || subsidyInfo.includes('政府补贴')) {
        subsidyTypes['政府补贴'] = (subsidyTypes['政府补贴'] || 0) + 1;
        govSubsidyCount++;
      }
      if (subsidyInfo.includes('优惠券')) {
        subsidyTypes['优惠券'] = (subsidyTypes['优惠券'] || 0) + 1;
      }
      if (subsidyInfo.includes('满减')) {
        subsidyTypes['满减活动'] = (subsidyTypes['满减活动'] || 0) + 1;
      }
    }
  });

  const subsidyPercentage = products.length > 0 ? (totalWithSubsidy / products.length) * 100 : 0;
  const govSubsidyPercentage = products.length > 0 ? (govSubsidyCount / products.length) * 100 : 0;

  const subsidyTypesArray = Object.entries(subsidyTypes)
    .map(([type, count]) => ({ type, count }))
    .sort((a, b) => b.count - a.count);

  return {
    totalWithSubsidy,
    subsidyPercentage,
    govSubsidyCount,
    govSubsidyPercentage,
    subsidyTypes: subsidyTypesArray
  };
};

/**
 * 生成完整的数据统计
 */
export const generateDataStats = (products: Product[]): DataPreviewStats => {
  const salesStats = calculateSalesStats(products);
  const brandStats = calculateBrandStats(products);
  const subsidyStats = calculateSubsidyStats(products);

  // 按关键词分组统计
  const keywordGroups: Record<string, Product[]> = {};
  products.forEach(product => {
    const keyword = product.keyword || '未知';
    if (!keywordGroups[keyword]) {
      keywordGroups[keyword] = [];
    }
    keywordGroups[keyword].push(product);
  });

  const keywordStats = Object.entries(keywordGroups).map(([keyword, products]) => {
    const prices = products.map(p => Number(p.price) || 0).filter(p => p > 0);
    const avgPrice = prices.length > 0 ? prices.reduce((sum, p) => sum + p, 0) / prices.length : 0;
    const priceRange: [number, number] = prices.length > 0 
      ? [Math.min(...prices), Math.max(...prices)]
      : [0, 0];

    return {
      keyword,
      count: products.length,
      avgPrice,
      priceRange
    };
  });

  // 价格分布
  const priceRanges = [
    { range: '0-100元', min: 0, max: 100 },
    { range: '100-500元', min: 100, max: 500 },
    { range: '500-1000元', min: 500, max: 1000 },
    { range: '1000-3000元', min: 1000, max: 3000 },
    { range: '3000元以上', min: 3000, max: Infinity }
  ];

  const priceDistribution = priceRanges.map(range => {
    const count = products.filter(p => {
      const price = Number(p.price) || 0;
      return price >= range.min && price < range.max;
    }).length;
    
    return {
      range: range.range,
      count,
      percentage: products.length > 0 ? (count / products.length) * 100 : 0
    };
  });

  const brandDistribution = brandStats.distribution.slice(0, 10); // 只取前10个品牌

  return {
    totalProducts: products.length,
    keywordStats,
    priceDistribution,
    brandDistribution,
    subsidyStats: {
      totalWithSubsidy: subsidyStats.totalWithSubsidy,
      subsidyPercentage: subsidyStats.subsidyPercentage,
      govSubsidyCount: subsidyStats.govSubsidyCount,
      govSubsidyPercentage: subsidyStats.govSubsidyPercentage
    },
    salesStats: {
      totalSales: salesStats.total,
      avgSales: salesStats.avg,
      topProducts: salesStats.topProducts
    }
  };
};

/**
 * 默认列配置
 */
export const getDefaultColumns = (): ColumnConfig[] => [
  {
    key: 'goods_id',
    title: '商品ID',
    dataIndex: 'goods_id',
    width: 120,
    fixed: 'left',
    sorter: false,
    render: 'text',
    visible: true
  },
  {
    key: 'goods_name',
    title: '商品名称',
    dataIndex: 'goods_name',
    width: 300,
    fixed: 'left',
    sorter: false,
    render: 'text',
    visible: true
  },
  {
    key: 'keyword',
    title: '搜索关键词',
    dataIndex: 'keyword',
    width: 120,
    sorter: false,
    render: 'text',
    visible: true
  },
  {
    key: 'price',
    title: '拼团价',
    dataIndex: 'price',
    width: 100,
    sorter: true,
    render: 'price',
    visible: true
  },
  {
    key: 'original_price',
    title: '原价',
    dataIndex: 'original_price',
    width: 100,
    sorter: true,
    render: 'price',
    visible: true
  },
  {
    key: 'sales',
    title: '销量',
    dataIndex: 'sales',
    width: 100,
    sorter: true,
    render: 'text',
    visible: true
  },
  {
    key: 'rating',
    title: '评分',
    dataIndex: 'rating',
    width: 80,
    sorter: true,
    render: 'text',
    visible: true
  },
  {
    key: 'brand_name',
    title: '品牌',
    dataIndex: 'brand_name',
    width: 120,
    sorter: false,
    render: 'text',
    visible: true
  },
  {
    key: 'subsidy_info',
    title: '补贴详情',
    dataIndex: 'subsidy_info',
    width: 150,
    sorter: false,
    render: 'subsidy',
    visible: true
  },
  {
    key: 'marketing_tags',
    title: '营销标签',
    dataIndex: 'marketing_tags',
    width: 200,
    sorter: false,
    render: 'tag',
    visible: true
  },
  {
    key: 'image_url',
    title: '商品图片',
    dataIndex: 'image_url',
    width: 120,
    sorter: false,
    render: 'image',
    visible: true
  },
  {
    key: 'goods_url',
    title: '商品链接',
    dataIndex: 'goods_url',
    width: 120,
    sorter: false,
    render: 'link',
    visible: false
  },
  {
    key: 'created_time',
    title: '采集时间',
    dataIndex: 'created_time',
    width: 150,
    sorter: true,
    render: 'text',
    visible: true
  }
];

/**
 * 格式化价格显示
 */
export const formatPrice = (price: number | string | undefined): string => {
  if (!price) return '-';
  const numPrice = typeof price === 'string' ? parseFloat(price) : price;
  if (isNaN(numPrice)) return '-';
  return `¥${numPrice.toFixed(2)}`;
};

/**
 * 格式化销量显示
 */
export const formatSales = (sales: number | string | undefined): string => {
  if (!sales) return '-';
  
  let numSales = 0;
  if (typeof sales === 'number') {
    numSales = sales;
  } else {
    const salesStr = sales.toString();
    if (salesStr.includes('万')) {
      const match = salesStr.match(/(\d+(?:\.\d+)?)\s*万/);
      if (match) {
        numSales = parseFloat(match[1]) * 10000;
      }
    } else {
      numSales = parseInt(salesStr.replace(/[^\d]/g, '')) || 0;
    }
  }

  if (numSales >= 10000) {
    return `${(numSales / 10000).toFixed(1)}万`;
  } else if (numSales >= 1000) {
    return `${(numSales / 1000).toFixed(1)}k`;
  }
  return numSales.toString();
};

/**
 * 格式化评分显示
 */
export const formatRating = (rating: number | string | undefined): string => {
  if (!rating) return '-';
  const numRating = typeof rating === 'string' ? parseFloat(rating) : rating;
  if (isNaN(numRating)) return '-';
  return `${numRating.toFixed(1)}分`;
};

/**
 * 生成导出文件名
 */
export const generateExportFilename = (format: 'excel' | 'csv', keywords?: string[]): string => {
  const timestamp = new Date().toISOString().slice(0, 19).replace(/[-:T]/g, '').slice(0, 12);
  const keywordStr = keywords && keywords.length > 0 ? keywords.join('_') : '全部';
  const extension = format === 'excel' ? 'xlsx' : 'csv';
  
  return `拼多多商品数据_${keywordStr}_${timestamp}.${extension}`;
};