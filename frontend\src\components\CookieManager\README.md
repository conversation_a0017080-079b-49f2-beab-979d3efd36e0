# Cookie 管理组件

这是一个完整的Cookie管理解决方案，为拼多多爬虫系统提供Cookie导入、验证、展示和管理功能。

## 功能特性

### 🔄 三步式流程
1. **导入Cookie** - 支持多种格式的Cookie导入
2. **验证Cookie** - 自动验证Cookie有效性和完整性
3. **保存使用** - 将验证通过的Cookie保存到系统

### 📁 支持的导入格式
- **JSON格式** - 标准的JSON Cookie数组
- **Netscape格式** - 浏览器原生Cookie文件格式
- **浏览器导出** - 从浏览器开发者工具导出的格式
- **Cookie字符串** - 简单的键值对字符串

### ✅ 智能验证
- **格式检测** - 自动识别Cookie格式并解析
- **必需字段验证** - 检查PDDAccessToken、pdd_user_id等关键Cookie
- **过期时间检查** - 验证Cookie是否在有效期内
- **安全性检查** - 检查Secure、HttpOnly、SameSite等安全属性

### 📊 直观展示
- **表格展示** - 清晰展示所有Cookie信息
- **状态指示** - 用颜色和图标标识Cookie状态
- **统计面板** - 显示总数、有效数、过期数等统计信息
- **敏感信息保护** - Cookie值默认隐藏，可选择显示

## 组件结构

```
CookieManager/
├── index.tsx          # 主组件 - 协调整个流程
├── CookieImport.tsx   # 导入组件 - 处理文件上传和文本输入
├── CookieDisplay.tsx  # 展示组件 - 表格显示Cookie信息
├── CookieValidator.tsx # 验证组件 - 检查Cookie有效性
├── types.ts           # 类型定义
├── utils.ts           # 工具函数
└── README.md          # 说明文档
```

## 使用方法

### 基本使用

```tsx
import CookieManager from '@/components/CookieManager';

function App() {
  const handleCookieStatusChange = (status: CookieStatus) => {
    console.log('Cookie状态变化:', status);
  };

  return (
    <CookieManager 
      onCookieStatusChange={handleCookieStatusChange}
    />
  );
}
```

### 单独使用组件

```tsx
import { CookieImport, CookieDisplay, CookieValidator } from '@/components/CookieManager';

// 仅导入功能
<CookieImport 
  onImport={handleImport}
  loading={loading}
/>

// 仅展示功能
<CookieDisplay 
  cookies={cookies}
  onRefresh={handleRefresh}
  onClear={handleClear}
/>

// 仅验证功能
<CookieValidator 
  cookies={cookies}
  onValidate={handleValidate}
/>
```

## API接口

组件依赖以下后端API接口：

- `GET /api/cookie/status` - 获取Cookie状态
- `POST /api/cookie/import` - 导入Cookie
- `POST /api/cookie/validate` - 验证Cookie
- `POST /api/cookie/save` - 保存Cookie
- `DELETE /api/cookie/clear` - 清除Cookie
- `GET /api/cookie/export` - 导出Cookie

## 数据格式

### Cookie对象
```typescript
interface Cookie {
  name: string;           // Cookie名称
  value: string;          // Cookie值
  domain: string;         // 域名
  path?: string;          // 路径
  expires?: number;       // 过期时间（Unix时间戳）
  httpOnly?: boolean;     // 仅HTTP访问
  secure?: boolean;       // 仅HTTPS传输
  sameSite?: 'Strict' | 'Lax' | 'None'; // SameSite策略
}
```

### 导入数据格式
```typescript
interface CookieImportData {
  cookies?: Cookie[];     // Cookie数组
  cookieString?: string;  // Cookie字符串
}
```

## 如何获取Cookie

### 方法1：浏览器开发者工具
1. 打开拼多多网站并登录
2. 按F12打开开发者工具
3. 切换到"应用程序"或"Application"标签
4. 在左侧选择"Cookies" > "https://yangkeduo.com"
5. 复制所有Cookie信息

### 方法2：浏览器扩展
使用Cookie导出扩展，如"EditThisCookie"等

### 方法3：抓包工具
使用Fiddler、Charles等工具抓取请求头中的Cookie

## 重要提示

1. **安全性** - Cookie包含敏感信息，请妥善保管
2. **有效期** - 定期检查和更新过期的Cookie
3. **必需字段** - 确保包含PDDAccessToken和pdd_user_id
4. **域名匹配** - Cookie域名应为.yangkeduo.com或子域名

## 故障排除

### 常见问题

**Q: 导入后显示"Cookie验证失败"**
A: 检查是否包含必需的PDDAccessToken和pdd_user_id字段

**Q: Cookie显示已过期**
A: 重新登录拼多多网站获取新的Cookie

**Q: 文件上传失败**
A: 检查文件格式是否正确，建议使用JSON格式

**Q: 爬虫仍然无法工作**
A: 确认Cookie在浏览器中能正常访问拼多多，检查网络连接

### 调试模式

在浏览器开发者工具中查看Console输出，组件会打印详细的调试信息。

## 更新历史

- v1.0.0 - 初始版本，支持基本的Cookie管理功能
- v1.1.0 - 添加智能格式检测和验证功能
- v1.2.0 - 优化用户界面和交互体验