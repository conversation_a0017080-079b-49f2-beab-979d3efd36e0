import axios from 'axios';
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import type { ApiResponse, Product, SearchConfig, CrawlStatus, Statistics, Cookie, CookieStatus, ExportRequest, ExportProgress, DataPreviewStats } from '@/types';

class ApiService {
  private client: AxiosInstance;

  constructor() {
    // 使用环境变量或默认值
    const baseURL = import.meta.env.VITE_API_BASE_URL ? `${import.meta.env.VITE_API_BASE_URL}/api` : '/api';
    const timeout = Number(import.meta.env.VITE_API_TIMEOUT) || 30000;
    
    this.client = axios.create({
      baseURL,
      timeout,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // 请求拦截器
    this.client.interceptors.request.use(
      (config) => {
        console.log('API Request:', config.method?.toUpperCase(), config.url);
        return config;
      },
      (error) => {
        console.error('API Request Error:', error);
        return Promise.reject(error);
      }
    );

    // 响应拦截器
    this.client.interceptors.response.use(
      (response: AxiosResponse) => {
        console.log('API Response:', response.status, response.config.url);
        return response;
      },
      (error) => {
        console.error('API Response Error:', error);
        const message = error.response?.data?.message || error.message || '请求失败';
        return Promise.reject(new Error(message));
      }
    );
  }

  // 通用请求方法
  private async request<T>(config: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      const response = await this.client.request<ApiResponse<T>>(config);
      return response.data;
    } catch (error) {
      console.error('API Request failed:', error);
      throw error;
    }
  }

  // GET 请求
  async get<T>(url: string, params?: any): Promise<ApiResponse<T>> {
    return this.request<T>({
      method: 'GET',
      url,
      params,
    });
  }

  // POST 请求
  async post<T>(url: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>({
      method: 'POST',
      url,
      data,
    });
  }

  // PUT 请求
  async put<T>(url: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>({
      method: 'PUT',
      url,
      data,
    });
  }

  // DELETE 请求
  async delete<T>(url: string): Promise<ApiResponse<T>> {
    return this.request<T>({
      method: 'DELETE',
      url,
    });
  }

  // 启动爬虫
  async startCrawl(config: SearchConfig): Promise<ApiResponse<{ task_id: string }>> {
    return this.request({
      method: 'POST',
      url: '/crawl/start',
      data: config,
    });
  }

  // 停止爬虫
  async stopCrawl(): Promise<ApiResponse<void>> {
    return this.request({
      method: 'POST',
      url: '/crawl/stop',
    });
  }

  // 获取爬虫状态
  async getCrawlStatus(): Promise<ApiResponse<CrawlStatus>> {
    return this.request({
      method: 'GET',
      url: '/crawl/status',
    });
  }

  // 获取产品数据
  async getProducts(params?: {
    page?: number;
    pageSize?: number;
    keyword?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }): Promise<ApiResponse<{ products: Product[]; total: number }>> {
    return this.request({
      method: 'GET',
      url: '/products',
      params,
    });
  }

  // 导出数据
  async exportProducts(format: 'excel' | 'csv' | 'json'): Promise<Blob> {
    const response = await this.client.request({
      method: 'GET',
      url: `/export/${format}`,
      responseType: 'blob',
    });
    return response.data;
  }

  // 获取统计数据
  async getStatistics(): Promise<ApiResponse<Statistics>> {
    return this.request({
      method: 'GET',
      url: '/statistics',
    });
  }

  // 清除数据
  async clearData(): Promise<ApiResponse<void>> {
    return this.request({
      method: 'DELETE',
      url: '/data/clear',
    });
  }

  // 健康检查
  async healthCheck(): Promise<ApiResponse<{ status: string; timestamp: string }>> {
    return this.request({
      method: 'GET',
      url: '/health',
    });
  }

  // Cookie 相关 API
  
  // 获取Cookie状态
  async getCookieStatus(): Promise<ApiResponse<CookieStatus>> {
    return this.request({
      method: 'GET',
      url: '/cookie/status',
    });
  }

  // 验证Cookie
  async validateCookie(cookies: Cookie[]): Promise<ApiResponse<{ valid: boolean; message: string }>> {
    return this.request({
      method: 'POST',
      url: '/cookie/validate',
      data: { cookies },
    });
  }

  // 保存Cookie
  async saveCookie(cookies: Cookie[]): Promise<ApiResponse<{ success: boolean; message: string }>> {
    return this.request({
      method: 'POST',
      url: '/cookie/save',
      data: { cookies },
    });
  }

  // 导入Cookie
  async importCookie(data: { cookies?: Cookie[]; cookieString?: string }): Promise<ApiResponse<{ success: boolean; message: string }>> {
    return this.request({
      method: 'POST',
      url: '/cookie/import',
      data,
    });
  }

  // 清除Cookie
  async clearCookie(): Promise<ApiResponse<{ success: boolean; message: string }>> {
    return this.request({
      method: 'DELETE',
      url: '/cookie/clear',
    });
  }

  // 导出Cookie
  async exportCookie(): Promise<ApiResponse<{ cookies: Cookie[] }>> {
    return this.request({
      method: 'GET',
      url: '/cookie/export',
    });
  }

  // 数据预览相关 API

  // 获取预览数据
  async getPreviewData(taskId: string, params?: {
    page?: number;
    pageSize?: number;
    keyword?: string;
  }): Promise<ApiResponse<{ 
    products: Product[]; 
    total: number; 
    stats: DataPreviewStats 
  }>> {
    return this.request({
      method: 'GET',
      url: `/crawl/${taskId}/preview`,
      params,
    });
  }

  // 获取预览统计
  async getPreviewStats(taskId: string): Promise<ApiResponse<DataPreviewStats>> {
    return this.request({
      method: 'GET',
      url: `/crawl/${taskId}/stats`,
    });
  }

  // 数据导出相关 API

  // 导出Excel格式
  async exportExcel(taskId: string, request: ExportRequest): Promise<ApiResponse<{ 
    exportId: string; 
    message: string 
  }>> {
    return this.request({
      method: 'POST',
      url: `/export/${taskId}`,
      data: request,
    });
  }

  // 导出CSV格式
  async exportCSV(taskId: string, request: ExportRequest): Promise<ApiResponse<{ 
    exportId: string; 
    message: string 
  }>> {
    return this.request({
      method: 'POST',
      url: `/export/${taskId}/csv`,
      data: request,
    });
  }

  // 获取导出进度
  async getExportProgress(exportId: string): Promise<ApiResponse<ExportProgress>> {
    return this.request({
      method: 'GET',
      url: `/export/progress/${exportId}`,
    });
  }

  // 下载导出文件
  async downloadExportFile(taskId: string, filename?: string): Promise<Blob> {
    const response = await this.client.request({
      method: 'GET',
      url: `/export/${taskId}/download`,
      params: filename ? { filename } : {},
      responseType: 'blob',
    });
    return response.data;
  }

  // 删除导出文件
  async deleteExportFile(taskId: string): Promise<ApiResponse<{ success: boolean }>> {
    return this.request({
      method: 'DELETE',
      url: `/export/${taskId}`,
    });
  }

  // 获取导出历史
  async getExportHistory(taskId?: string): Promise<ApiResponse<{
    exports: Array<{
      id: string;
      taskId: string;
      format: string;
      filename: string;
      status: string;
      createdAt: string;
      fileSize?: number;
    }>;
  }>> {
    return this.request({
      method: 'GET',
      url: '/export/history',
      params: taskId ? { taskId } : {},
    });
  }
}

export const apiService = new ApiService();
export const apiClient = apiService; // 为了保持兼容性
export default apiService;