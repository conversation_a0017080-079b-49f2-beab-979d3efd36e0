import React, { useMemo } from 'react';
import {
  Card,
  Table,
  Tag,
  Button,
  Space,
  Typo<PERSON>,
  <PERSON><PERSON><PERSON>,
  Badge,
  Al<PERSON>,
  Row,
  Col,
  Statistic,
  Popconfirm
} from 'antd';
import {
  ReloadOutlined,
  DeleteOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ClockCircleOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import type { CookieDisplayProps, Cookie } from './types';
import { formatExpiryTime, getCookieStatusColor, validatePDDCookies } from './utils';

const { Text, Paragraph } = Typography;

const CookieDisplay: React.FC<CookieDisplayProps> = ({ 
  cookies, 
  loading = false, 
  onRefresh, 
  onClear 
}) => {
  // 计算统计信息
  const statistics = useMemo(() => {
    const total = cookies.length;
    const currentTime = Math.floor(Date.now() / 1000);
    
    const valid = cookies.filter(cookie => 
      !cookie.expires || cookie.expires > currentTime
    ).length;
    
    const expired = cookies.filter(cookie => 
      cookie.expires && cookie.expires <= currentTime
    ).length;
    
    const expiringSoon = cookies.filter(cookie => {
      if (!cookie.expires) return false;
      const timeLeft = cookie.expires - currentTime;
      return timeLeft > 0 && timeLeft < 24 * 60 * 60; // 24小时内过期
    }).length;

    const important = cookies.filter(cookie => 
      ['PDDAccessToken', 'pdd_user_id'].includes(cookie.name)
    ).length;

    return { total, valid, expired, expiringSoon, important };
  }, [cookies]);

  // 验证拼多多Cookie
  const pddValidation = useMemo(() => {
    return validatePDDCookies(cookies);
  }, [cookies]);

  // 表格列定义
  const columns: ColumnsType<Cookie> = [
    {
      title: 'Cookie名称',
      dataIndex: 'name',
      key: 'name',
      width: 180,
      fixed: 'left',
      render: (name: string) => {
        const isImportant = ['PDDAccessToken', 'pdd_user_id'].includes(name);
        return (
          <Space>
            <Text strong={isImportant} style={{ color: isImportant ? '#1890ff' : undefined }}>
              {name}
            </Text>
            {isImportant && (
              <Tooltip title="重要Cookie">
                <Tag color="blue">重要</Tag>
              </Tooltip>
            )}
          </Space>
        );
      }
    },
    {
      title: 'Cookie值',
      dataIndex: 'value',
      key: 'value',
      width: 200,
      render: (value: string) => {
        const [showValue, setShowValue] = React.useState(false);
        
        return (
          <Space>
            <Text 
              ellipsis 
              style={{ maxWidth: 120 }} 
              title={showValue ? value : undefined}
            >
              {showValue ? value : '*'.repeat(Math.min(value.length, 20))}
            </Text>
            <Button
              type="text"
              size="small"
              icon={showValue ? <EyeInvisibleOutlined /> : <EyeOutlined />}
              onClick={() => setShowValue(!showValue)}
            />
          </Space>
        );
      }
    },
    {
      title: '域名',
      dataIndex: 'domain',
      key: 'domain',
      width: 150,
      render: (domain: string) => (
        <Text type="secondary">{domain}</Text>
      )
    },
    {
      title: '路径',
      dataIndex: 'path',
      key: 'path',
      width: 100,
      render: (path: string) => (
        <Text type="secondary">{path || '/'}</Text>
      )
    },
    {
      title: '过期时间',
      dataIndex: 'expires',
      key: 'expires',
      width: 120,
      render: (expires: number) => {
        const statusColor = getCookieStatusColor({ expires } as Cookie);
        const timeText = formatExpiryTime(expires);
        
        return (
          <Tag color={statusColor} icon={<ClockCircleOutlined />}>
            {timeText}
          </Tag>
        );
      }
    },
    {
      title: '安全性',
      key: 'security',
      width: 120,
      render: (_, record: Cookie) => (
        <Space size="small">
          {record.secure && (
            <Tooltip title="仅HTTPS传输">
              <Tag color="green">Secure</Tag>
            </Tooltip>
          )}
          {record.httpOnly && (
            <Tooltip title="仅HTTP访问">
              <Tag color="orange">HttpOnly</Tag>
            </Tooltip>
          )}
          {record.sameSite && (
            <Tooltip title={`SameSite策略: ${record.sameSite}`}>
              <Tag color="blue">{record.sameSite}</Tag>
            </Tooltip>
          )}
        </Space>
      )
    },
    {
      title: '状态',
      key: 'status',
      width: 80,
      render: (_, record: Cookie) => {
        const currentTime = Math.floor(Date.now() / 1000);
        
        let status = 'normal';
        let icon = <CheckCircleOutlined />;
        let color = 'success';
        
        if (record.expires && record.expires <= currentTime) {
          status = 'expired';
          icon = <ExclamationCircleOutlined />;
          color = 'error';
        } else if (record.expires && (record.expires - currentTime) < 24 * 60 * 60) {
          status = 'warning';
          icon = <ClockCircleOutlined />;
          color = 'warning';
        }
        
        return (
          <Badge 
            status={color as any} 
            text={
              <Tooltip title={
                status === 'expired' ? '已过期' :
                status === 'warning' ? '即将过期' : '正常'
              }>
                {icon}
              </Tooltip>
            }
          />
        );
      }
    }
  ];

  // 渲染验证状态
  const renderValidationStatus = () => {
    if (cookies.length === 0) {
      return (
        <Alert
          message="没有Cookie数据"
          description="请先导入Cookie数据以开始使用爬虫功能。"
          type="info"
          showIcon
          icon={<InfoCircleOutlined />}
          style={{ marginBottom: 16 }}
        />
      );
    }

    if (pddValidation.valid) {
      return (
        <Alert
          message="Cookie状态正常"
          description="检测到所有必需的拼多多Cookie，爬虫可以正常使用。"
          type="success"
          showIcon
          icon={<CheckCircleOutlined />}
          style={{ marginBottom: 16 }}
        />
      );
    }

    return (
      <Alert
        message="Cookie状态异常"
        description={
          <Space direction="vertical" size="small">
            {pddValidation.missing.length > 0 && (
              <Text>
                缺少必需的Cookie: <Text code>{pddValidation.missing.join(', ')}</Text>
              </Text>
            )}
            {pddValidation.expired.length > 0 && (
              <Text>
                已过期的Cookie: <Text code>{pddValidation.expired.join(', ')}</Text>
              </Text>
            )}
            <Text type="secondary">
              建议重新导入完整且有效的Cookie数据
            </Text>
          </Space>
        }
        type="error"
        showIcon
        icon={<ExclamationCircleOutlined />}
        style={{ marginBottom: 16 }}
      />
    );
  };

  return (
    <Card 
      title="Cookie 管理" 
      extra={
        <Space>
          {onRefresh && (
            <Button 
              icon={<ReloadOutlined />} 
              onClick={onRefresh}
              loading={loading}
            >
              刷新
            </Button>
          )}
          {onClear && cookies.length > 0 && (
            <Popconfirm
              title="确认清除Cookie"
              description="此操作将清除所有Cookie数据，确定继续吗？"
              onConfirm={onClear}
              okText="确定"
              cancelText="取消"
            >
              <Button 
                danger 
                icon={<DeleteOutlined />}
                loading={loading}
              >
                清除全部
              </Button>
            </Popconfirm>
          )}
        </Space>
      }
    >
      {/* 验证状态 */}
      {renderValidationStatus()}

      {/* 统计信息 */}
      {cookies.length > 0 && (
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={6}>
            <Statistic
              title="总数"
              value={statistics.total}
              prefix={<InfoCircleOutlined />}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="有效"
              value={statistics.valid}
              valueStyle={{ color: '#3f8600' }}
              prefix={<CheckCircleOutlined />}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="已过期"
              value={statistics.expired}
              valueStyle={{ color: '#cf1322' }}
              prefix={<ExclamationCircleOutlined />}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="即将过期"
              value={statistics.expiringSoon}
              valueStyle={{ color: '#fa8c16' }}
              prefix={<ClockCircleOutlined />}
            />
          </Col>
        </Row>
      )}

      {/* Cookie表格 */}
      <Table
        columns={columns}
        dataSource={cookies}
        rowKey="name"
        loading={loading}
        scroll={{ x: 1000 }}
        pagination={{
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => 
            `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          defaultPageSize: 10,
          pageSizeOptions: ['10', '20', '50', '100']
        }}
        size="small"
        locale={{
          emptyText: '暂无Cookie数据'
        }}
      />

      {/* 说明信息 */}
      {cookies.length > 0 && (
        <div style={{ marginTop: 16 }}>
          <Paragraph type="secondary" style={{ fontSize: '12px' }}>
            <Text strong>说明：</Text>
            <ul style={{ marginBottom: 0, paddingLeft: 16 }}>
              <li>重要Cookie（PDDAccessToken、pdd_user_id）用于身份验证，缺失或过期会影响爬虫功能</li>
              <li>点击眼睛图标可以查看Cookie值的明文内容</li>
              <li>建议定期检查Cookie的有效期，及时更新过期的Cookie</li>
            </ul>
          </Paragraph>
        </div>
      )}
    </Card>
  );
};

export default CookieDisplay;