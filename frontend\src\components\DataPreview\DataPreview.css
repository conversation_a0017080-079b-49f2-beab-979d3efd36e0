/* DataPreview 组件样式 */

/* 表格行样式 */
.data-preview .table-row-light {
  background-color: #fafafa;
}

.data-preview .table-row-dark {
  background-color: #ffffff;
}

.data-preview .table-row-light:hover,
.data-preview .table-row-dark:hover {
  background-color: #e6f7ff !important;
}

/* 补贴字段高亮样式 */
.data-preview .subsidy-highlight {
  color: #f5222d;
  font-weight: bold;
}

.data-preview .subsidy-info-cell {
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 商品图片样式 */
.data-preview .product-image {
  border-radius: 4px;
  border: 1px solid #d9d9d9;
  transition: all 0.3s ease;
}

.data-preview .product-image:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
}

/* 标签样式 */
.data-preview .tag-container {
  max-width: 200px;
  overflow: hidden;
}

.data-preview .tag-wrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

/* 价格显示样式 */
.data-preview .price-cell {
  font-weight: bold;
  color: #f5222d;
}

.data-preview .price-original {
  text-decoration: line-through;
  color: #999;
  font-size: 12px;
  margin-left: 8px;
}

/* 销量显示样式 */
.data-preview .sales-cell {
  color: #52c41a;
  font-weight: bold;
}

/* 评分显示样式 */
.data-preview .rating-cell {
  color: #faad14;
}

/* 链接按钮样式 */
.data-preview .link-button {
  padding: 2px 6px;
  font-size: 12px;
}

/* 数据统计卡片样式 */
.data-preview .stats-card {
  margin-bottom: 16px;
}

.data-preview .stats-card .ant-card-head {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.data-preview .stats-card .ant-card-head-title {
  color: white;
}

/* 进度条样式 */
.data-preview .progress-container {
  margin-bottom: 8px;
}

.data-preview .progress-label {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  font-size: 12px;
  color: #666;
}

/* 导出管理样式 */
.data-preview .export-overview {
  background-color: #fafafa;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 16px;
}

.data-preview .export-progress {
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 16px;
}

.data-preview .export-success {
  color: #52c41a;
  font-weight: bold;
  margin-bottom: 8px;
}

/* 商品详情弹窗样式 */
.data-preview .product-detail-modal .ant-modal-body {
  max-height: 70vh;
  overflow-y: auto;
}

.data-preview .product-image-large {
  border-radius: 8px;
  border: 1px solid #d9d9d9;
  object-fit: cover;
}

/* 过滤器面板样式 */
.data-preview .filter-panel {
  padding: 16px;
  min-width: 300px;
}

.data-preview .filter-panel .ant-form-item {
  margin-bottom: 16px;
}

/* 列设置面板样式 */
.data-preview .column-settings {
  max-height: 400px;
  overflow-y: auto;
  padding: 16px;
}

.data-preview .column-setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding: 4px 0;
}

/* 响应式适配 */
@media (max-width: 768px) {
  .data-preview .ant-table-wrapper {
    overflow-x: auto;
  }
  
  .data-preview .stats-card {
    margin-bottom: 12px;
  }
  
  .data-preview .export-overview {
    padding: 12px;
  }
  
  .data-preview .filter-panel {
    min-width: 280px;
  }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
  .data-preview .table-row-light {
    background-color: #1f1f1f;
  }
  
  .data-preview .table-row-dark {
    background-color: #141414;
  }
  
  .data-preview .export-overview {
    background-color: #1f1f1f;
    border: 1px solid #303030;
  }
  
  .data-preview .export-progress {
    background-color: #162312;
    border-color: #389e0d;
  }
}

/* 动画效果 */
.data-preview .fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 加载状态样式 */
.data-preview .loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 80px 0;
}

.data-preview .empty-container {
  text-align: center;
  padding: 40px 0;
}

/* 工具栏样式 */
.data-preview .toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 0 4px;
}

.data-preview .toolbar-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.data-preview .toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 批量操作样式 */
.data-preview .batch-actions {
  background-color: #e6f7ff;
  border: 1px solid #91d5ff;
  border-radius: 6px;
  padding: 8px 16px;
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.data-preview .batch-info {
  color: #1890ff;
  font-weight: 500;
}

.data-preview .batch-buttons {
  display: flex;
  gap: 8px;
}

/* 高亮搜索结果 */
.data-preview .search-highlight {
  background-color: #fff566;
  padding: 0 2px;
  border-radius: 2px;
}

/* 补贴标签特殊样式 */
.data-preview .subsidy-tag-billion {
  background-color: #ff4d4f;
  border-color: #ff4d4f;
  color: white;
}

.data-preview .subsidy-tag-gov {
  background-color: #1890ff;
  border-color: #1890ff;
  color: white;
}

.data-preview .subsidy-tag-other {
  background-color: #52c41a;
  border-color: #52c41a;
  color: white;
}

/* 表格头部固定样式优化 */
.data-preview .ant-table-thead > tr > th {
  background-color: #1f4e79;
  color: white;
  font-weight: bold;
  border-bottom: 2px solid #1f4e79;
}

.data-preview .ant-table-thead > tr > th::before {
  display: none;
}

/* 表格边框优化 */
.data-preview .ant-table-bordered .ant-table-tbody > tr > td {
  border-right: 1px solid #f0f0f0;
}

.data-preview .ant-table-bordered .ant-table-tbody > tr:last-child > td {
  border-bottom: 1px solid #f0f0f0;
}