# WebSocket 实时通信功能测试指南

## 前置条件

1. 后端API服务器已启动 (端口8000)
2. 前端开发服务器已启动 (端口3000)
3. WebSocket端点 `/ws/crawl/{task_id}` 已实现

## 测试步骤

### 1. 基础连接测试

**目标**: 验证WebSocket连接能否正常建立和断开

**步骤**:
1. 打开浏览器，访问 `http://localhost:3000`
2. 进入爬虫控制台页面
3. 观察页面顶部的连接状态提示
4. 预期结果: 显示"已连接"状态

**验证点**:
- [ ] 连接状态显示为"已连接"
- [ ] 连接指示器为绿色
- [ ] 浏览器开发者工具网络选项卡显示WebSocket连接

### 2. 心跳检测测试

**目标**: 验证心跳机制是否正常工作

**步骤**:
1. 建立WebSocket连接
2. 等待30秒以上
3. 在浏览器开发者工具中观察WebSocket消息
4. 预期结果: 每30秒发送一次ping消息，收到pong响应

**验证点**:
- [ ] 定期发送ping消息
- [ ] 收到pong响应
- [ ] 延迟时间计算正确
- [ ] 心跳时间更新

### 3. 爬虫启动测试

**目标**: 验证启动爬虫时的WebSocket通信

**步骤**:
1. 填写爬虫配置表单
2. 点击"开始爬取"按钮
3. 观察实时监控标签页
4. 预期结果: 收到连接成功消息，切换到监控页面

**验证点**:
- [ ] 自动切换到实时监控标签页
- [ ] 显示任务ID
- [ ] WebSocket URL包含正确的任务ID
- [ ] 连接状态保持稳定

### 4. 进度更新测试

**目标**: 验证实时进度更新功能

**模拟数据**:
```json
{
  "type": "progress",
  "payload": {
    "currentPage": 5,
    "totalPages": 50,
    "collectedCount": 100,
    "failedCount": 2
  },
  "timestamp": "2024-08-01T10:00:00.000Z"
}
```

**验证点**:
- [ ] 进度条实时更新
- [ ] 统计数字实时更新
- [ ] 成功率计算正确
- [ ] 速度计算显示

### 5. 关键词状态测试

**目标**: 验证关键词处理状态显示

**模拟数据**:
```json
{
  "type": "keyword_started",
  "payload": {
    "keyword": "手机"
  },
  "timestamp": "2024-08-01T10:00:00.000Z"
}
```

**验证点**:
- [ ] 当前关键词显示更新
- [ ] 关键词状态动画效果
- [ ] 关键词标签颜色正确

### 6. 数据接收测试

**目标**: 验证商品数据实时接收

**模拟数据**:
```json
{
  "type": "data",
  "payload": {
    "products": [
      {
        "id": "1",
        "title": "测试商品",
        "price": 99.99,
        "imageUrl": "http://example.com/image.jpg",
        "shopName": "测试店铺",
        "soldCount": 100
      }
    ]
  },
  "timestamp": "2024-08-01T10:00:00.000Z"
}
```

**验证点**:
- [ ] 商品数据添加到列表
- [ ] 统计数量更新
- [ ] 数据显示正确

### 7. 错误处理测试

**目标**: 验证错误情况的处理

**步骤**:
1. 模拟网络断开
2. 模拟服务器错误
3. 模拟消息格式错误

**验证点**:
- [ ] 连接断开时显示错误状态
- [ ] 自动重连机制工作
- [ ] 错误消息显示用户友好
- [ ] 重连按钮功能正常

### 8. 任务完成测试

**目标**: 验证任务完成时的处理

**模拟数据**:
```json
{
  "type": "completed",
  "payload": {
    "totalProcessed": 1000,
    "successCount": 980,
    "failedCount": 20
  },
  "timestamp": "2024-08-01T10:00:00.000Z"
}
```

**验证点**:
- [ ] 进度条显示完成状态
- [ ] 爬虫状态更新为停止
- [ ] 最终统计数据显示
- [ ] 成功提示消息

## 性能测试

### 1. 高频消息测试

**目标**: 验证高频消息下的性能表现

**步骤**:
1. 模拟每秒10条消息的频率
2. 持续发送5分钟
3. 观察浏览器性能

**验证点**:
- [ ] 页面响应保持流畅
- [ ] 内存使用稳定
- [ ] CPU使用合理
- [ ] 没有内存泄漏

### 2. 长时间连接测试

**目标**: 验证长时间连接的稳定性

**步骤**:
1. 建立WebSocket连接
2. 保持连接2小时以上
3. 定期发送消息

**验证点**:
- [ ] 连接保持稳定
- [ ] 心跳机制正常
- [ ] 没有自动断开
- [ ] 消息处理正常

## 浏览器兼容性测试

测试以下浏览器的兼容性:
- [ ] Chrome (最新版本)
- [ ] Firefox (最新版本)
- [ ] Safari (最新版本)
- [ ] Edge (最新版本)

## 自动化测试

### WebSocket连接测试脚本

```javascript
// 在浏览器控制台运行
const testWebSocket = () => {
  const ws = new WebSocket('ws://localhost:8000/ws/crawl/test');
  
  ws.onopen = () => {
    console.log('✅ WebSocket连接成功');
    ws.send(JSON.stringify({
      type: 'ping',
      timestamp: new Date().toISOString()
    }));
  };
  
  ws.onmessage = (event) => {
    const message = JSON.parse(event.data);
    console.log('📨 收到消息:', message);
  };
  
  ws.onerror = (error) => {
    console.error('❌ WebSocket错误:', error);
  };
  
  ws.onclose = () => {
    console.log('🔌 WebSocket连接关闭');
  };
  
  return ws;
};

// 运行测试
const ws = testWebSocket();
```

### 消息发送测试脚本

```javascript
// 模拟发送不同类型的消息
const sendTestMessages = (ws) => {
  const messages = [
    {
      type: 'connected',
      payload: {},
      timestamp: new Date().toISOString()
    },
    {
      type: 'progress',
      payload: {
        currentPage: 10,
        totalPages: 100,
        collectedCount: 200,
        failedCount: 5
      },
      timestamp: new Date().toISOString()
    },
    {
      type: 'keyword_started',
      payload: {
        keyword: '测试关键词'
      },
      timestamp: new Date().toISOString()
    }
  ];
  
  messages.forEach((message, index) => {
    setTimeout(() => {
      ws.send(JSON.stringify(message));
      console.log('📤 发送消息:', message.type);
    }, index * 1000);
  });
};
```

## 问题排查指南

### 连接失败
1. 检查后端服务是否运行在8000端口
2. 检查防火墙设置
3. 验证WebSocket端点路径是否正确
4. 查看浏览器控制台错误信息

### 消息不更新
1. 检查WebSocket连接状态
2. 验证消息格式是否正确
3. 检查事件监听器是否正确绑定
4. 查看Redux/Zustand状态更新

### 性能问题
1. 检查消息发送频率
2. 验证是否有内存泄漏
3. 检查组件是否有不必要的重新渲染
4. 优化数据处理逻辑

### 重连问题
1. 检查重连配置参数
2. 验证重连逻辑
3. 检查网络状态
4. 查看重连尝试次数限制

## 测试报告模板

```
# WebSocket实时通信功能测试报告

## 测试环境
- 浏览器: Chrome 91.0
- 操作系统: Windows 10
- 前端版本: v1.0.0
- 后端版本: v1.0.0

## 测试结果
### 基础功能 ✅
- [x] WebSocket连接建立
- [x] 心跳检测
- [x] 消息发送接收
- [x] 自动重连

### 实时更新 ✅
- [x] 进度条更新
- [x] 统计数据更新
- [x] 关键词状态更新
- [x] 连接状态显示

### 错误处理 ✅
- [x] 连接断开处理
- [x] 消息格式错误处理
- [x] 网络异常处理
- [x] 用户友好错误提示

### 性能表现 ✅
- [x] 高频消息处理
- [x] 长时间连接稳定性
- [x] 内存使用合理
- [x] CPU使用正常

## 发现的问题
1. 问题描述
2. 重现步骤
3. 预期结果
4. 实际结果
5. 严重程度

## 改进建议
1. 建议内容
2. 实施优先级
3. 预期效果

## 测试结论
总体评价: ✅ 通过 / ❌ 不通过
建议发布: ✅ 是 / ❌ 否
```