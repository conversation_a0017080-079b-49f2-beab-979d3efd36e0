# 拼多多爬虫前端项目技术需求规格

## 项目背景

基于现有的Python后端爬虫系统（PDDCrawler），开发功能完全对等的React前端界面。后端已提供完整的RESTful API（基于FastAPI）和WebSocket支持，前端需要实现与后端的完整集成，确保运行结果的100%一致性。

## 技术栈需求

### 核心技术栈
- **框架**：React 18.2+ 配合 TypeScript 5.0+
- **构建工具**：Vite 4.0+ （快速开发构建）
- **UI库**：Ant Design 5.0+ （企业级UI组件）
- **HTTP客户端**：Axios 1.0+ （API请求处理）
- **WebSocket**：react-use-websocket 4.0+ （实时通信）
- **状态管理**：Zustand 4.0+ 或 React Context API
- **路由**：React Router 6.0+
- **样式方案**：Ant Design + CSS Modules / Styled Components

### 开发工具需求
- **代码质量**：ESLint + Prettier + Husky
- **类型检查**：TypeScript 严格模式
- **测试框架**：Vitest + React Testing Library + Cypress
- **包管理**：npm 或 yarn
- **版本控制**：Git 配合 conventional commits

## 功能需求

### FR-001: Cookie管理系统
**优先级：** P0（最高）  
**描述：** 实现简化的三步Cookie管理操作

**详细需求：**
1. **Cookie导入功能**
   - 支持JSON文件上传导入
   - 支持浏览器导出格式解析
   - 支持Cookie字符串粘贴导入
   - 智能格式识别和转换
   - 导入过程错误处理和提示

2. **Cookie状态管理**
   - 实时显示Cookie有效性状态
   - 显示关键Cookie的过期时间
   - 提供Cookie健康检查功能
   - 支持Cookie的增删改操作
   - 自动检测必要Cookie字段（PDDAccessToken, pdd_user_id等）

3. **Cookie存储同步**
   - 自动保存到后端两种格式：browser_data/cookies/pdd_cookies.json 和 config/cookies.json
   - 保持与后端Cookie管理器的完全兼容
   - 支持Cookie导出功能
   - 实现Cookie备份和恢复

**验收标准：**
- Cookie导入成功率≥95%
- Cookie状态检查准确率100%
- 与后端Cookie格式完全兼容
- 支持≥3种导入格式
- 操作流程≤3步完成

**API集成：**
- GET /api/cookie/status - 获取Cookie状态
- POST /api/cookie/validate - 验证Cookie有效性
- POST /api/cookie/save - 保存Cookie
- POST /api/cookie/import - 导入Cookie
- GET /api/cookie/export - 导出Cookie
- DELETE /api/cookie/clear - 清除Cookie

### FR-002: 多关键词智能处理
**优先级：** P0（最高）  
**描述：** 支持多种分隔符的关键词输入和智能处理

**详细需求：**
1. **关键词输入解析**
   - 支持逗号、分号、换行符、空格等分隔符
   - 智能识别中英文分词
   - 自动去除空白和重复项
   - 关键词格式验证和规范化
   - 实时显示解析结果

2. **关键词管理**
   - 关键词历史记录保存
   - 常用关键词模板
   - 关键词组合导入导出
   - 批量关键词操作
   - 关键词建议和自动补全

3. **关键词处理策略**
   - 与后端keywords字段完全匹配（字符串数组格式）
   - 支持单关键词和多关键词模式
   - 关键词顺序处理逻辑
   - 关键词进度跟踪显示

**验收标准：**
- 支持≥5种分隔符格式
- 关键词解析准确率≥99%
- 与后端keywords格式100%兼容
- 支持≥100个关键词批量处理
- 关键词历史记录≥50条

**技术实现：**
- 使用正则表达式实现智能分词
- Ant Design Select组件（mode="tags"）
- localStorage持久化历史记录
- 防抖优化实时解析性能

### FR-003: 爬虫任务管理
**优先级：** P0（最高）  
**描述：** 完整的爬虫任务生命周期管理

**详细需求：**
1. **任务配置**
   - 关键词配置（必填）
   - 目标数量配置（targetCount，默认60）
   - 排序方式配置（sortMethod，可选）
   - 最大页数配置（maxPages，默认5）
   - 无头模式配置（headless，默认true）
   - 商品筛选配置（enableFilter，默认false）

2. **任务控制**
   - 启动任务（/api/crawl/start）
   - 暂停任务（/api/crawl/{task_id}/pause）
   - 恢复任务（/api/crawl/{task_id}/resume）
   - 停止任务（/api/crawl/{task_id}/stop）
   - 任务状态查询（/api/crawl/{task_id}/status）

3. **任务监控**
   - 任务状态实时显示（running/paused/stopped/completed/failed）
   - 任务进度实时更新
   - 任务配置信息展示
   - 任务历史记录管理
   - 多任务并发支持

**验收标准：**
- 任务启动成功率≥95%
- 任务状态同步延迟≤2秒
- 支持≥5个并发任务
- 任务操作响应时间≤500ms
- 任务历史记录≥100条

**API集成：**
- POST /api/crawl/start - 启动爬虫任务
- POST /api/crawl/{task_id}/pause - 暂停任务
- POST /api/crawl/{task_id}/resume - 恢复任务
- POST /api/crawl/{task_id}/stop - 停止任务
- GET /api/crawl/{task_id}/status - 获取任务状态

### FR-004: WebSocket实时通信
**优先级：** P0（最高）  
**描述：** 基于react-use-websocket的稳定实时数据传输

**详细需求：**
1. **连接管理**
   - 自动建立WebSocket连接（/ws/crawl/{task_id}）
   - 连接断开自动重连（指数退避算法）
   - 连接状态监控和显示
   - 连接健康检查（心跳机制）
   - 多任务连接池管理

2. **消息处理**
   - 连接确认消息（type: "connected"）
   - 进度更新消息（type: "progress"）
   - 数据推送消息（type: "data"）
   - 任务完成消息（type: "completed"）
   - 错误消息处理（type: "error"）
   - 关键词级别消息（type: "keyword_started/completed"）

3. **数据同步**
   - 实时进度同步（当前数量/目标数量/百分比）
   - 实时商品数据推送
   - 关键词处理状态同步
   - 任务状态变更通知
   - 错误和异常通知

**验收标准：**
- WebSocket连接成功率≥95%
- 断线重连成功率≥90%
- 消息延迟≤2秒
- 数据同步准确率100%
- 支持≥5个并发连接

**技术实现：**
- react-use-websocket库
- 消息队列和缓冲机制
- 连接状态管理
- 重连策略实现
- 消息类型定义和处理

### FR-005: 数据预览和管理
**优先级：** P1（高）  
**描述：** 实时数据预览和完整的数据管理功能

**详细需求：**
1. **实时数据预览**
   - 数据表格实时更新显示
   - 支持分页、排序、筛选功能
   - 商品图片缩略图显示
   - 关键字段高亮显示
   - 数据统计信息实时更新

2. **数据表格功能**
   - 所有字段完整显示（对应后端column_mapping）
   - 支持字段显示/隐藏配置
   - 支持表格数据导出预览
   - 支持数据行选择和批量操作
   - 响应式表格设计

3. **数据统计**
   - 实时统计总数据量
   - 按关键词统计数据分布
   - 价格区间分布统计
   - 品牌分布统计
   - 数据质量分析

**验收标准：**
- 数据显示延迟≤1秒
- 表格操作响应≤200ms
- 支持≥10000条数据流畅显示
- 字段显示100%与后端匹配
- 统计准确率100%

**API集成：**
- GET /api/crawl/{task_id}/preview - 获取预览数据

### FR-006: 数据导出系统
**优先级：** P0（最高）  
**描述：** 与后端完全一致的数据导出功能

**详细需求：**
1. **导出格式支持**
   - Excel格式导出（.xlsx）
   - CSV格式导出（.csv，UTF-8-BOM编码）
   - 导出格式与后端config/settings.yaml中的export配置完全一致
   - 字段映射与后端column_mapping 100%匹配

2. **导出功能**
   - 支持完整数据导出
   - 支持筛选数据导出
   - 支持批量任务导出
   - 导出进度显示
   - 导出文件下载管理

3. **字段完整性**
   - 特别确保"补贴详情"（subsidy_info）字段正确导出
   - 所有32个字段完整导出：goods_id, goods_name, price, original_price, coupon_price, market_price, sales, sales_tip, comment_count, rating, goods_url, image_url, hd_thumb_url, brand_name, category, tags, marketing_tags, special_text, subsidy_info, activity_type_name, merchant_type_name, keyword, created_time
   - 字段中文名称与后端Excel导出完全一致

**验收标准：**
- 导出数据与后端100%一致
- 支持≥2种导出格式
- 导出成功率≥95%
- 文件编码格式正确
- 特殊字段（subsidy_info）导出准确率100%

**API集成：**
- POST /api/export/{task_id} - Excel格式导出
- POST /api/export/{task_id}/csv - CSV格式导出
- GET /api/export/{task_id}/download - 下载导出文件

### FR-007: 商品筛选功能
**优先级：** P2（中）  
**描述：** 可选的商品筛选功能，对应后端enableFilter参数

**详细需求：**
1. **筛选开关**
   - 简单的开关控制筛选功能
   - 对应后端CrawlRequest.enableFilter字段
   - 筛选状态持久化保存
   - 筛选规则配置界面

2. **筛选规则配置**
   - 基于后端product_filter配置的筛选规则界面
   - 支持品牌筛选、价格区间、销量阈值等
   - 筛选算法参数配置（match_threshold等）
   - 筛选效果预览和测试

**验收标准：**
- 筛选开关与后端参数100%对应
- 筛选规则配置完整性≥80%
- 筛选效果准确性≥90%

## 非功能需求

### NFR-001: 性能需求
1. **页面加载性能**
   - 首次加载时间≤3秒
   - 后续页面切换≤500ms
   - 代码分割和懒加载
   - 静态资源CDN优化

2. **运行时性能**
   - 页面操作响应时间≤100ms
   - WebSocket消息处理延迟≤500ms
   - 大数据量表格渲染≤2秒
   - 内存使用控制在合理范围

3. **网络性能**
   - API请求响应时间≤2秒
   - WebSocket连接延迟≤1秒
   - 数据传输压缩率≥30%
   - 离线缓存支持

**测试标准：**
- Lighthouse性能评分≥90
- 并发用户≥50无性能下降
- 内存泄漏检测通过
- 长时间运行稳定性测试通过

### NFR-002: 兼容性需求
1. **浏览器兼容性**
   - Chrome 80+（主要支持）
   - Firefox 75+（完整支持）
   - Safari 13+（基本支持）
   - Edge 80+（完整支持）

2. **设备兼容性**
   - 桌面端：1920x1080及以上分辨率最佳
   - 平板端：768px以上宽度适配
   - 移动端：375px以上基本功能支持

3. **系统兼容性**
   - Windows 10+
   - macOS 10.15+
   - Linux（Ubuntu 18.04+）

### NFR-003: 可用性需求
1. **界面设计**
   - 遵循Material Design或Ant Design规范
   - 支持明暗主题切换
   - 响应式设计适配多设备
   - 无障碍支持（WCAG 2.1 AA）

2. **用户体验**
   - 操作流程简化，关键功能≤3步完成
   - 错误提示友好且具有指导性
   - 加载状态和进度指示明确
   - 快捷键支持提升操作效率

3. **国际化支持**
   - 中文（简体）为主要语言
   - 英文界面支持（可选）
   - 日期时间本地化
   - 数字格式本地化

### NFR-004: 安全需求
1. **数据安全**
   - Cookie等敏感信息加密存储
   - 用户输入数据验证和清理
   - XSS攻击防护
   - CSRF攻击防护

2. **通信安全**
   - HTTPS强制使用（生产环境）
   - WebSocket安全连接（WSS）
   - API请求认证和授权
   - 敏感数据传输加密

3. **隐私保护**
   - 用户数据最小化收集
   - 数据使用透明化
   - 符合相关隐私法规要求

### NFR-005: 可维护性需求
1. **代码质量**
   - TypeScript严格模式
   - ESLint规则严格执行
   - 代码覆盖率≥80%
   - 代码重复率≤5%

2. **架构设计**
   - 组件化和模块化设计
   - 清晰的文件夹结构
   - 统一的编码规范
   - 完整的API文档

3. **测试覆盖**
   - 单元测试覆盖率≥80%
   - 集成测试覆盖核心流程
   - E2E测试覆盖关键用户场景
   - 性能测试和压力测试

### NFR-006: 可扩展性需求
1. **架构扩展性**
   - 插件化架构设计
   - 主题和样式可定制
   - 新功能模块易于集成
   - API版本兼容性支持

2. **功能扩展性**
   - 新的数据导出格式支持
   - 自定义筛选规则支持
   - 第三方服务集成能力
   - 多语言扩展支持

## 开发环境需求

### 开发工具链
- **Node.js**：18.0+ LTS版本
- **包管理器**：npm 8.0+ 或 yarn 1.22+
- **IDE**：VS Code 配合相关插件
- **浏览器调试**：Chrome DevTools
- **版本控制**：Git 2.30+

### 开发服务器配置
- **开发端口**：5173（Vite默认）
- **代理配置**：代理后端API请求到localhost:8000
- **热重载**：支持代码修改自动刷新
- **源码映射**：开发环境完整源码映射

### 构建配置
- **生产构建**：优化包体积和性能
- **环境变量**：支持多环境配置
- **静态资源**：图片、字体等资源优化
- **浏览器缓存**：合理的缓存策略

## 部署需求

### 构建产物
- **静态文件**：HTML、CSS、JS、图片等
- **资源优化**：压缩、合并、CDN准备
- **浏览器兼容**：Polyfill和降级方案
- **SEO优化**：基本的SEO meta信息

### 部署环境
- **Web服务器**：Nginx、Apache或静态文件服务
- **HTTPS支持**：SSL证书配置
- **Gzip压缩**：文本资源压缩
- **缓存策略**：合理的HTTP缓存头

### 监控和日志
- **错误监控**：JavaScript错误收集
- **性能监控**：用户体验指标收集
- **用户分析**：基本的用户行为分析（可选）
- **健康检查**：应用健康状态检查

## 项目约束

### 技术约束
1. **必须使用指定的技术栈**
2. **必须与现有后端API完全兼容**
3. **不得修改后端API接口定义**
4. **必须保持数据格式100%一致**

### 时间约束
1. **开发周期**：预计3-4周完成
2. **测试时间**：1周完整测试
3. **部署上线**：1-2天部署和验证

### 资源约束
1. **开发人员**：1-2名前端开发工程师
2. **测试资源**：与后端系统联调测试
3. **服务器资源**：使用现有服务器环境

## 验收标准

### 功能验收
1. **Cookie管理**：三步导入流程，状态检查准确
2. **关键词处理**：多格式支持，智能解析
3. **任务管理**：完整生命周期管理
4. **实时通信**：WebSocket稳定连接
5. **数据导出**：格式100%与后端一致
6. **数据预览**：实时更新，功能完整

### 性能验收
1. **加载性能**：首次加载≤3秒
2. **响应性能**：操作响应≤100ms
3. **网络性能**：API响应≤2秒
4. **稳定性**：24小时连续运行

### 兼容性验收
1. **浏览器**：主流浏览器完美支持
2. **设备**：桌面/平板/移动适配
3. **分辨率**：多分辨率响应式适配

### 用户体验验收
1. **操作流程**：关键功能≤3步完成
2. **错误处理**：友好错误提示
3. **视觉设计**：现代化美观界面
4. **交互体验**：流畅的操作反馈

## 风险评估

### 技术风险
1. **WebSocket连接稳定性**：网络环境复杂可能影响连接
2. **大数据量渲染性能**：数据量过大可能影响界面响应
3. **浏览器兼容性**：部分新特性在旧浏览器支持有限

### 项目风险
1. **需求变更**：后端API可能发生变化
2. **时间风险**：功能复杂度可能超出预期
3. **集成风险**：前后端集成可能存在兼容性问题

### 缓解措施
1. **技术选型谨慎**：选择成熟稳定的技术方案
2. **增量开发**：采用敏捷开发模式，及时反馈
3. **充分测试**：完整的测试覆盖，及早发现问题
4. **文档完善**：详细的技术文档和使用说明