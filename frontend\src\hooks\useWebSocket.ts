import { useEffect, useCallback, useRef, useState } from 'react';
import useWebSocketLib, { ReadyState } from 'react-use-websocket';
import { useAppStore } from '@/stores/appStore';
import type { CrawlStatus, WebSocketMessage } from '@/types';

interface WebSocketStats {
  messagesReceived: number;
  reconnectAttempts: number;
  averageLatency: number;
  lastHeartbeat: Date | null;
}

export const useWebSocket = (taskId?: string) => {
  const {
    setCrawlStatus,
    addProducts,
    setWsConnected,
    setError,
  } = useAppStore();

  const [stats, setStats] = useState<WebSocketStats>({
    messagesReceived: 0,
    reconnectAttempts: 0,
    averageLatency: 0,
    lastHeartbeat: null,
  });

  const heartbeatIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const latencyRef = useRef<number[]>([]);
  const lastPingTimeRef = useRef<number>(0);

  // 构建 WebSocket URL
  const getSocketUrl = useCallback(() => {
    if (!taskId) {
      return null; // 没有 taskId 时不建立连接
    }
    
    // 使用环境变量或默认值
    const wsBaseUrl = import.meta.env.VITE_WS_BASE_URL || 'ws://localhost:8001';
    const url = new URL(wsBaseUrl);
    const protocol = window.location.protocol === 'https:' ? 'wss:' : url.protocol;
    const host = window.location.hostname === 'localhost' ? url.hostname : window.location.hostname;
    const port = url.port || '8001';
    
    return `${protocol}//${host}:${port}/ws/crawl/${taskId}`;
  }, [taskId]);

  // WebSocket 配置
  const socketOptions = {
    reconnectAttempts: 10,
    reconnectInterval: 3000,
    shouldReconnect: () => true,
    retryOnError: true,
    heartbeat: {
      message: JSON.stringify({ type: 'ping', timestamp: Date.now() }),
      returnMessage: 'pong',
      timeout: 60000,
      interval: 30000,
    },
    onReconnectStop: (numAttempts: number) => {
      console.log('WebSocket重连停止，尝试次数:', numAttempts);
      setStats(prev => ({ ...prev, reconnectAttempts: numAttempts }));
    },
  };

  const socketUrl = getSocketUrl();
  const {
    sendMessage: sendRawMessage,
    lastMessage,
    readyState,
    getWebSocket,
  } = useWebSocketLib(socketUrl, {
    ...socketOptions,
    skip: !socketUrl, // 没有 URL 时跳过连接
  });

  // 处理WebSocket消息
  const handleMessage = useCallback((message: any) => {
    const { type, data: payload } = message;
    
    // 更新统计信息
    setStats(prev => ({
      ...prev,
      messagesReceived: prev.messagesReceived + 1,
    }));

    switch (type) {
      case 'connected':
        setWsConnected(true);
        setError(null);
        console.log('WebSocket连接成功');
        break;

      case 'status':
        setCrawlStatus(payload);
        break;
      
      case 'progress':
        setCrawlStatus((prev: CrawlStatus) => ({
          ...prev,
          progress: { ...prev.progress, ...payload }
        }));
        break;

      case 'keyword_started':
        setCrawlStatus((prev: CrawlStatus) => ({
          ...prev,
          currentKeyword: payload.keyword,
        }));
        break;

      case 'keyword_completed':
        setCrawlStatus((prev: CrawlStatus) => ({
          ...prev,
          currentKeyword: undefined,
        }));
        break;
      
      case 'data':
        if (Array.isArray(payload)) {
          addProducts(payload);
        } else if (payload.products && Array.isArray(payload.products)) {
          addProducts(payload.products);
        } else if (payload && Array.isArray(payload.data)) {
          addProducts(payload.data);
        }
        break;
      
      case 'error':
        setError(payload.message || '发生未知错误');
        break;
      
      case 'completed':
        setCrawlStatus((prev: CrawlStatus) => ({
          ...prev,
          isRunning: false
        }));
        setError(null);
        break;

      case 'pong':
        // 处理心跳响应
        const latency = Date.now() - lastPingTimeRef.current;
        latencyRef.current.push(latency);
        if (latencyRef.current.length > 10) {
          latencyRef.current.shift();
        }
        const avgLatency = latencyRef.current.reduce((a, b) => a + b, 0) / latencyRef.current.length;
        setStats(prev => ({
          ...prev,
          averageLatency: avgLatency,
          lastHeartbeat: new Date(),
        }));
        break;
      
      default:
        console.log('未处理的WebSocket消息类型:', type, payload);
    }
  }, [setCrawlStatus, addProducts, setError, setWsConnected]);

  // 处理接收到的消息
  useEffect(() => {
    if (lastMessage?.data) {
      try {
        const message: WebSocketMessage = JSON.parse(lastMessage.data);
        handleMessage(message);
      } catch (error) {
        console.error('解析WebSocket消息失败:', error);
      }
    }
  }, [lastMessage, handleMessage]);

  // 连接状态管理
  useEffect(() => {
    const isConnected = readyState === ReadyState.OPEN;
    setWsConnected(isConnected);
    
    if (!isConnected && readyState === ReadyState.CLOSED) {
      setError('WebSocket连接已断开');
    } else if (isConnected) {
      setError(null);
    }
  }, [readyState, setWsConnected, setError]);

  // 发送消息（带类型安全）
  const sendMessage = useCallback((message: any) => {
    if (readyState === ReadyState.OPEN) {
      sendRawMessage(JSON.stringify({
        ...message,
        timestamp: new Date().toISOString(),
      }));
    } else {
      console.warn('WebSocket未连接，无法发送消息');
    }
  }, [readyState, sendRawMessage]);

  // 心跳检测
  useEffect(() => {
    if (readyState === ReadyState.OPEN) {
      heartbeatIntervalRef.current = setInterval(() => {
        lastPingTimeRef.current = Date.now();
        sendMessage({ type: 'ping', timestamp: Date.now() });
      }, 30000);
    } else {
      if (heartbeatIntervalRef.current) {
        clearInterval(heartbeatIntervalRef.current);
      }
    }

    return () => {
      if (heartbeatIntervalRef.current) {
        clearInterval(heartbeatIntervalRef.current);
      }
    };
  }, [readyState, sendMessage]);

  // 重新连接
  const reconnect = useCallback(() => {
    const ws = getWebSocket();
    if (ws) {
      ws.close();
    }
    setStats(prev => ({ ...prev, reconnectAttempts: prev.reconnectAttempts + 1 }));
  }, [getWebSocket]);

  // 获取连接状态字符串
  const getConnectionState = useCallback(() => {
    switch (readyState) {
      case ReadyState.CONNECTING:
        return 'connecting';
      case ReadyState.OPEN:
        return 'connected';
      case ReadyState.CLOSING:
        return 'closing';
      case ReadyState.CLOSED:
        return 'disconnected';
      default:
        return 'unknown';
    }
  }, [readyState]);

  return {
    // 连接状态
    isConnected: readyState === ReadyState.OPEN,
    connectionState: getConnectionState(),
    readyState,
    
    // 方法
    sendMessage,
    reconnect,
    
    // 统计信息
    stats,
    
    // 工具方法
    getWebSocket,
  };
};