# 拼多多爬虫前端组件架构图

## 🏗️ 整体架构图

```mermaid
graph TB
    subgraph "App Layer"
        App[App.tsx - 根组件]
        Router[React Router - 路由管理]
        ErrorBoundary[ErrorBoundary - 错误边界]
        ThemeProvider[ThemeProvider - 主题提供者]
    end

    subgraph "Layout Layer"
        Layout[Layout - 主布局]
        Header[Header - 顶部导航]
        Sidebar[Sidebar - 侧边栏]
        Content[Content - 主内容区]
        Footer[Footer - 底部信息]
    end

    subgraph "Feature Components"
        CrawlControl[CrawlControl - 爬取控制]
        ProgressMonitor[ProgressMonitor - 进度监控]
        DataPreview[DataPreview - 数据预览]
        CookieManager[CookieManager - Cookie管理]
        ExportManager[ExportManager - 导出管理]
    end

    subgraph "Business Components"
        CrawlConfig[CrawlConfig - 爬取配置]
        KeywordInput[KeywordInput - 关键词输入]
        FilterConfig[FilterConfig - 筛选配置]
        ProgressBar[ProgressBar - 进度条]
        ProductTable[ProductTable - 商品表格]
        CookieImport[CookieImport - Cookie导入]
        ExportConfig[ExportConfig - 导出配置]
    end

    subgraph "Common Components"
        Loading[Loading - 加载组件]
        Modal[Modal - 模态框]
        Notification[Notification - 通知组件]
        Tooltip[Tooltip - 提示框]
        Button[Button - 按钮组件]
        Input[Input - 输入组件]
        Table[Table - 表格组件]
    end

    subgraph "State Management"
        CrawlerStore[CrawlerStore - 爬虫状态]
        CookieStore[CookieStore - Cookie状态]
        AppStore[AppStore - 应用状态]
        WSStore[WebSocketStore - WebSocket状态]
    end

    subgraph "Services Layer"
        APIClient[API Client - HTTP请求]
        WSManager[WebSocket Manager - 实时通信]
        CacheService[Cache Service - 缓存服务]
        StorageService[Storage Service - 存储服务]
    end

    subgraph "Utils Layer"
        Validation[Validation - 数据验证]
        Format[Format - 数据格式化]
        Constants[Constants - 常量定义]
        Helpers[Helpers - 工具函数]
    end

    App --> Router
    App --> ErrorBoundary
    App --> ThemeProvider
    Router --> Layout
    Layout --> Header
    Layout --> Sidebar
    Layout --> Content
    Layout --> Footer
    
    Content --> CrawlControl
    Content --> ProgressMonitor
    Content --> DataPreview
    Content --> CookieManager
    Content --> ExportManager
    
    CrawlControl --> CrawlConfig
    CrawlControl --> KeywordInput
    CrawlControl --> FilterConfig
    ProgressMonitor --> ProgressBar
    DataPreview --> ProductTable
    CookieManager --> CookieImport
    ExportManager --> ExportConfig
    
    CrawlConfig --> Loading
    CrawlConfig --> Modal
    KeywordInput --> Input
    ProgressBar --> Loading
    ProductTable --> Table
    CookieImport --> Button
    
    CrawlerStore <--> CrawlControl
    CrawlerStore <--> ProgressMonitor
    CrawlerStore <--> DataPreview
    CookieStore <--> CookieManager
    AppStore <--> Layout
    WSStore <--> ProgressMonitor
    
    CrawlerStore --> APIClient
    CookieStore --> APIClient
    WSStore --> WSManager
    
    APIClient --> CacheService
    WSManager --> StorageService
    
    CrawlConfig --> Validation
    ProductTable --> Format
    APIClient --> Constants
    WSManager --> Helpers
```

## 🎯 核心组件详细设计

### 1. CrawlControl（爬取控制组件）

```mermaid
graph LR
    subgraph "CrawlControl"
        CC[CrawlControl Container]
        
        subgraph "Configuration Panel"
            KI[KeywordInput]
            TC[TargetCountInput]
            SM[SortMethodSelect]
            FC[FilterConfig]
            AS[AdvancedSettings]
        end
        
        subgraph "Action Panel"
            AB[ActionButtons]
            StartBtn[开始按钮]
            PauseBtn[暂停按钮]
            StopBtn[停止按钮]
            ResetBtn[重置按钮]
        end
        
        subgraph "Status Panel"
            TaskStatus[任务状态]
            ConfigPreview[配置预览]
            ValidationMsg[验证消息]
        end
    end
    
    CC --> KI
    CC --> TC
    CC --> SM
    CC --> FC
    CC --> AS
    CC --> AB
    AB --> StartBtn
    AB --> PauseBtn
    AB --> StopBtn
    AB --> ResetBtn
    CC --> TaskStatus
    CC --> ConfigPreview
    CC --> ValidationMsg
```

**关键特性：**
- 关键词智能解析（多分隔符支持）
- 实时配置验证
- 任务状态同步
- 操作权限控制

**Props接口：**
```typescript
interface CrawlControlProps {
  onStart: (config: CrawlConfig) => Promise<void>;
  onPause: () => Promise<void>;
  onResume: () => Promise<void>;
  onStop: () => Promise<void>;
  isRunning: boolean;
  isPaused: boolean;
  currentTask: CrawlTask | null;
  disabled?: boolean;
}
```

### 2. ProgressMonitor（进度监控组件）

```mermaid
graph LR
    subgraph "ProgressMonitor"
        PM[ProgressMonitor Container]
        
        subgraph "Overall Progress"
            OPB[总体进度条]
            OPS[总体进度统计]
            ETA[预计完成时间]
        end
        
        subgraph "Keyword Progress"
            KPT[关键词标签页]
            KPB[关键词进度条]
            KPS[关键词统计]
            KDS[数据预览]
        end
        
        subgraph "Connection Status"
            WSS[WebSocket状态]
            CSI[连接状态指示器]
            RSB[重连按钮]
        end
        
        subgraph "Real-time Stats"
            RTS[实时统计]
            Speed[爬取速度]
            Success[成功率]
            Errors[错误计数]
        end
    end
    
    PM --> OPB
    PM --> OPS
    PM --> ETA
    PM --> KPT
    PM --> KPB
    PM --> KPS
    PM --> KDS
    PM --> WSS
    PM --> CSI
    PM --> RSB
    PM --> RTS
    PM --> Speed
    PM --> Success
    PM --> Errors
```

**关键特性：**
- 实时进度更新
- 关键词级别进度跟踪
- WebSocket连接监控
- 性能指标显示

**Props接口：**
```typescript
interface ProgressMonitorProps {
  taskId: string | null;
  progress: ProgressInfo;
  keywordProgress: Record<string, KeywordProgress>;
  connectionStatus: ConnectionStatus;
  onReconnect: () => void;
}
```

### 3. DataPreview（数据预览组件）

```mermaid
graph LR
    subgraph "DataPreview"
        DP[DataPreview Container]
        
        subgraph "Data Controls"
            DC[数据控制面板]
            RefreshBtn[刷新按钮]
            FilterBtn[筛选按钮]
            ExportBtn[导出按钮]
            ViewModeBtn[视图模式]
        end
        
        subgraph "Data Display"
            PT[ProductTable]
            PG[ProductGrid]
            PS[ProductStats]
        end
        
        subgraph "Table Features"
            TH[表格头部]
            TB[表格主体]
            TP[表格分页]
            TS[表格搜索]
            TC[表格配置]
        end
        
        subgraph "Statistics Panel"
            SC[统计卡片]
            PC[价格分布图]
            BC[品牌分布图]
            KC[关键词分布]
        end
    end
    
    DP --> DC
    DC --> RefreshBtn
    DC --> FilterBtn
    DC --> ExportBtn
    DC --> ViewModeBtn
    
    DP --> PT
    DP --> PG
    DP --> PS
    
    PT --> TH
    PT --> TB
    PT --> TP
    PT --> TS
    PT --> TC
    
    PS --> SC
    PS --> PC
    PS --> BC
    PS --> KC
```

**关键特性：**
- 多视图模式（表格/卡片/统计）
- 实时数据更新
- 高级筛选和搜索
- 数据可视化

**Props接口：**
```typescript
interface DataPreviewProps {
  taskId: string | null;
  products: Product[];
  loading: boolean;
  onRefresh: () => void;
  onExport: (format: ExportFormat) => void;
  viewMode: 'table' | 'grid' | 'stats';
  onViewModeChange: (mode: ViewMode) => void;
}
```

### 4. CookieManager（Cookie管理组件）

```mermaid
graph LR
    subgraph "CookieManager"
        CM[CookieManager Container]
        
        subgraph "Import Panel"
            CI[CookieImport]
            FileUpload[文件上传]
            TextInput[文本输入]
            FormatDetect[格式检测]
            ImportBtn[导入按钮]
        end
        
        subgraph "Status Panel"
            CS[CookieStatus]
            ValidIcon[有效性图标]
            ExpireTime[过期时间]
            HealthCheck[健康检查]
        end
        
        subgraph "Management Panel"
            CL[CookieList]
            EditBtn[编辑按钮]
            DeleteBtn[删除按钮]
            ExportBtn[导出按钮]
            ClearBtn[清空按钮]
        end
        
        subgraph "Validation Panel"
            VR[验证结果]
            VMsg[验证消息]
            VDetails[验证详情]
        end
    end
    
    CM --> CI
    CI --> FileUpload
    CI --> TextInput
    CI --> FormatDetect
    CI --> ImportBtn
    
    CM --> CS
    CS --> ValidIcon
    CS --> ExpireTime
    CS --> HealthCheck
    
    CM --> CL
    CL --> EditBtn
    CL --> DeleteBtn
    CL --> ExportBtn
    CL --> ClearBtn
    
    CM --> VR
    VR --> VMsg
    VR --> VDetails
```

**关键特性：**
- 多格式Cookie导入
- 实时有效性验证
- 可视化管理界面
- 智能格式检测

**Props接口：**
```typescript
interface CookieManagerProps {
  cookieStatus: CookieStatus;
  loading: boolean;
  onImport: (data: CookieImportData) => Promise<void>;
  onValidate: () => Promise<void>;
  onClear: () => Promise<void>;
  onExport: () => Promise<CookieExportData>;
}
```

## 🔄 数据流架构

```mermaid
sequenceDiagram
    participant U as User
    participant CC as CrawlControl
    participant CS as CrawlerStore
    participant API as APIClient
    participant WS as WebSocketManager
    participant PM as ProgressMonitor
    participant DP as DataPreview

    U->>CC: 配置爬取参数
    CC->>CS: 更新配置状态
    CC->>API: 启动爬取任务
    API-->>CC: 返回任务ID
    CC->>CS: 设置任务状态
    CS->>WS: 建立WebSocket连接
    
    loop 实时数据流
        WS->>CS: 接收进度更新
        CS->>PM: 更新进度显示
        WS->>CS: 接收商品数据
        CS->>DP: 更新数据预览
    end
    
    WS->>CS: 任务完成通知
    CS->>CC: 更新任务状态
    CS->>PM: 显示完成状态
    CS->>DP: 最终数据展示
```

## 🎨 组件设计模式

### 1. Container/Presenter 模式

```typescript
// Container组件 - 处理逻辑和状态
const CrawlControlContainer: React.FC = () => {
  const crawlerStore = useCrawlerStore();
  const { startCrawl, pauseCrawl, stopCrawl } = crawlerStore;
  
  return (
    <CrawlControlPresenter
      isRunning={crawlerStore.isRunning}
      isPaused={crawlerStore.isPaused}
      onStart={startCrawl}
      onPause={pauseCrawl}
      onStop={stopCrawl}
    />
  );
};

// Presenter组件 - 纯UI展示
const CrawlControlPresenter: React.FC<CrawlControlPresenterProps> = ({
  isRunning,
  isPaused,
  onStart,
  onPause,
  onStop,
}) => {
  return (
    <div className="crawl-control">
      {/* UI组件 */}
    </div>
  );
};
```

### 2. Hook模式

```typescript
// 自定义Hook封装逻辑
export function useCrawlControl() {
  const [config, setConfig] = useState<CrawlConfig>(defaultConfig);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  
  const validateConfig = useCallback((config: CrawlConfig) => {
    const errors = validateCrawlConfig(config);
    setValidationErrors(errors);
    return errors.length === 0;
  }, []);
  
  const startCrawl = useCallback(async (config: CrawlConfig) => {
    if (!validateConfig(config)) return;
    // 启动爬取逻辑
  }, [validateConfig]);
  
  return {
    config,
    setConfig,
    validationErrors,
    startCrawl,
    validateConfig,
  };
}
```

### 3. 复合组件模式

```typescript
// 复合组件设计
const DataPreview = {
  Root: DataPreviewRoot,
  Header: DataPreviewHeader,
  Filters: DataPreviewFilters,
  Table: DataPreviewTable,
  Pagination: DataPreviewPagination,
  Stats: DataPreviewStats,
};

// 使用方式
<DataPreview.Root>
  <DataPreview.Header>
    <DataPreview.Filters />
  </DataPreview.Header>
  <DataPreview.Table />
  <DataPreview.Pagination />
  <DataPreview.Stats />
</DataPreview.Root>
```

## 🔌 组件通信机制

### 1. Props传递（父子组件）
```typescript
// 父组件向子组件传递数据和回调
<CrawlControl
  config={config}
  isRunning={isRunning}
  onStart={handleStart}
  onConfigChange={handleConfigChange}
/>
```

### 2. Context传递（跨层级组件）
```typescript
// 主题Context
const ThemeContext = createContext<ThemeContextValue>(defaultValue);

// WebSocket Context
const WebSocketContext = createContext<WebSocketContextValue>(defaultValue);
```

### 3. 状态管理（全局状态）
```typescript
// Zustand状态管理
const useCrawlerStore = create<CrawlerState>((set, get) => ({
  // 状态和方法定义
}));
```

### 4. 事件系统（松耦合通信）
```typescript
// 自定义事件系统
class EventEmitter {
  private listeners = new Map<string, Set<Function>>();
  
  on(event: string, listener: Function) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set());
    }
    this.listeners.get(event)!.add(listener);
  }
  
  emit(event: string, ...args: any[]) {
    this.listeners.get(event)?.forEach(listener => listener(...args));
  }
}
```

## 🧪 组件测试策略

### 1. 单元测试
```typescript
describe('CrawlControl', () => {
  test('should start crawl with valid config', async () => {
    const mockStart = jest.fn();
    render(<CrawlControl onStart={mockStart} />);
    
    // 填写配置
    fireEvent.change(screen.getByLabelText('关键词'), {
      target: { value: '手机' }
    });
    
    // 点击开始
    fireEvent.click(screen.getByText('开始爬取'));
    
    expect(mockStart).toHaveBeenCalledWith({
      keywords: ['手机'],
      targetCount: 60,
      // ...其他配置
    });
  });
});
```

### 2. 集成测试
```typescript
describe('Crawler Workflow', () => {
  test('should complete full crawl workflow', async () => {
    render(<App />);
    
    // 导入Cookie
    await importCookies();
    
    // 配置爬取
    await configureCrawl();
    
    // 开始爬取
    await startCrawl();
    
    // 等待完成
    await waitForCompletion();
    
    // 验证结果
    expect(screen.getByText('爬取完成')).toBeInTheDocument();
  });
});
```

### 3. E2E测试
```typescript
// Cypress E2E测试
describe('Crawler E2E', () => {
  it('should complete full user journey', () => {
    cy.visit('/');
    cy.importCookies();
    cy.configureCrawl(['手机'], 10);
    cy.startCrawl();
    cy.waitForCompletion();
    cy.exportData('excel');
    cy.verifyDownload();
  });
});
```

## 📱 响应式设计策略

### 1. 断点系统
```css
/* 断点定义 */
@media (max-width: 768px) {
  /* 移动端样式 */
}

@media (min-width: 769px) and (max-width: 1024px) {
  /* 平板端样式 */
}

@media (min-width: 1025px) {
  /* 桌面端样式 */
}
```

### 2. 组件适配策略
```typescript
// 响应式Hook
export function useResponsive() {
  const [screenSize, setScreenSize] = useState<ScreenSize>('desktop');
  
  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      if (width <= 768) setScreenSize('mobile');
      else if (width <= 1024) setScreenSize('tablet');
      else setScreenSize('desktop');
    };
    
    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);
  
  return screenSize;
}

// 响应式组件
const ResponsiveDataPreview: React.FC = () => {
  const screenSize = useResponsive();
  
  if (screenSize === 'mobile') {
    return <MobileDataPreview />;
  } else if (screenSize === 'tablet') {
    return <TabletDataPreview />;
  } else {
    return <DesktopDataPreview />;
  }
};
```

## 🎯 性能优化策略

### 1. 组件懒加载
```typescript
// 路由级别懒加载
const CrawlControl = lazy(() => import('./components/CrawlControl'));
const DataPreview = lazy(() => import('./components/DataPreview'));

// 组件级别懒加载
const ExportManager = lazy(() => import('./components/ExportManager'));
```

### 2. 虚拟滚动
```typescript
// 大列表虚拟滚动
import { FixedSizeList as List } from 'react-window';

const VirtualizedProductList: React.FC<{ products: Product[] }> = ({ products }) => {
  const Row = ({ index, style }: any) => (
    <div style={style}>
      <ProductCard product={products[index]} />
    </div>
  );

  return (
    <List
      height={600}
      itemCount={products.length}
      itemSize={120}
    >
      {Row}
    </List>
  );
};
```

### 3. 记忆化优化
```typescript
// React.memo优化
const ProductCard = React.memo<ProductCardProps>(({ product }) => {
  return <div>{/* 产品卡片内容 */}</div>;
});

// useMemo优化计算
const expensiveValue = useMemo(() => {
  return computeExpensiveValue(data);
}, [data]);

// useCallback优化函数
const handleClick = useCallback((id: string) => {
  // 处理点击
}, []);
```

这个组件架构图详细展示了整个前端系统的组件结构、数据流、设计模式和优化策略，为开发团队提供了清晰的实现指导。