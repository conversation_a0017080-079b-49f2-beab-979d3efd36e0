"""
API响应监听器 - 完全替代请求拦截的高性能方案

特点：
1. 被动监听，不干预请求流程
2. 完善的生命周期管理
3. 智能并发控制
4. 熔断保护机制
5. 详细的监控指标
"""
import asyncio
import json
import re
import time
from asyncio import Queue, Semaphore
from typing import Dict, List, Optional, Callable, Set, Any
from playwright.async_api import Page, Response
from loguru import logger

try:
    import orjson
    HAS_ORJSON = True
except ImportError:
    HAS_ORJSON = False

from src.utils.helpers import load_config


class APIResponseMonitor:
    """
    API响应监听器 - 完全替代请求拦截的高性能方案
    
    特点：
    1. 被动监听，不干预请求流程
    2. 完善的生命周期管理
    3. 智能并发控制
    4. 熔断保护机制
    5. 详细的监控指标
    """
    
    # 商户类型映射（基于真实数据分析和验证）
    MERCHANT_TYPE_MAPPING = {
        0: "官方旗舰店",      # 基于真实数据：海尔官方旗舰店等
        1: "企业店铺",        # 基于真实数据：精选好物大家电等
        2: "品牌授权店",      # 品牌授权经营
        3: "品牌旗舰店",      # 品牌自营旗舰店
        4: "品牌直营店",      # 品牌直接经营
        5: "专营店",          # 基于真实数据：众沁电器专营店等
        6: "普通店铺",        # 基于真实数据：乐享优品家电购等
        7: "工厂店",          # 工厂直销店
        10: "官方旗舰店",     # 官方认证旗舰店
        11: "品牌旗舰店"      # 品牌认证旗舰店
    }
    
    def __init__(self, config_path: str = "config/settings.yaml"):
        self.config = load_config(config_path)
        self.target_config = self.config.get("target", {})
        
        # 基础配置
        self.base_url = self.target_config.get("base_url", "https://mobile.yangkeduo.com")
        self.monitored_data: List[Dict[str, Any]] = []  # 更新术语：监听到的数据
        self.current_keyword = ""
        self.data_callback: Optional[Callable] = None
        
        # 监听器管理
        self._response_handlers: Dict[Page, Callable] = {}
        self._active_pages: Set[Page] = set()
        
        # API模式匹配（使用正则表达式）
        self.api_patterns = [
            # 拼多多真实商品数据API（最优先）
            r"/proxy/api/search\?pdduid=",  # 主要的商品搜索API
            r"/proxy/api/search(?:\?|$)(?!_hotquery)",  # 排除search_hotquery
            # 其他可能的API路径
            r"/api/search(?:\?|$)(?!_hotquery)", 
            r"/search/goods(?:\?|$)",
            r"/goods/list(?:\?|$)",
            r"/api/v[0-9]+/search",  # 版本化API
            r"/mobile/search",
            # 通用模式（更严格的匹配，避免误匹配）
            r"/api/.*(?:goods|products)(?:\?|$)",
            r"/api/.*(?:list|items)(?:\?|$)"
        ]
        self._pattern_regex = re.compile("|".join(self.api_patterns))
        
        # 并发控制
        response_config = self.target_config.get("response_monitoring", {})
        max_concurrent = response_config.get("max_concurrent", 20)
        queue_size = response_config.get("queue_size", 100)
        
        self._processing_semaphore = Semaphore(max_concurrent)
        self._response_queue = Queue(maxsize=queue_size)
        self._queue_processor_task = None
        self._processing_tasks: Set[asyncio.Task] = set()
        self._processing_paused = False
        
        # 监控指标
        self.start_time = time.time()
        self.metrics = {
            "total_responses": 0,
            "processed_responses": 0,
            "successful_extractions": 0,
            "queued_responses": 0,
            "dropped_responses": 0,
            "processing_errors": 0,
            "parse_errors": 0,
            "avg_processing_time": 0,
            "total_processing_time": 0,
            "api_hit_rate": 0,
            "data_quality_score": 0
        }
        
        # 熔断器
        self.error_counter = 0
        self.error_threshold = response_config.get("error_threshold", 10)
        self.circuit_breaker_open = False
        self.last_error_time = None
        self.reset_timeout = response_config.get("reset_timeout", 60)
        
        # 响应缓存（避免重复处理）
        self._processed_urls: Set[str] = set()
        self._cache_size_limit = 1000
        
        # 关键词上下文管理
        self.keyword_context = {
            'current': None,
            'previous': None,
            'switch_time': None
        }
        
        # 数据提取路径配置（复用原有逻辑）
        self.data_paths = [
            ['items'],  # 拼多多搜索API直接返回items
            ['goods', 'list'],
            ['result', 'goods_list'],
            ['data', 'goods_list'],
            ['goods_list'],
            ['list'],
            ['data', 'list'],
            ['result', 'list'],
            ['data', 'items'],
            ['result', 'items'],
            ['data', 'data', 'list'],
            ['result', 'data', 'list'],
            ['data', 'result', 'list'],
            ['result', 'result', 'list'],
            ['store', 'data', 'list'],
            ['flip', 'goods', 'list']
        ]
        
        # Icon ID映射字典
        self.icon_id_mapping = {
            0: "秒杀",  # Flash Sale
            10: "旗舰店",  # Flagship Store
            20001: "百亿补贴",  # Billion Subsidy
            20013: "夏清仓",  # Summer Clearance
            10014: "品牌黑标",  # Brand Black Label/Brand Certification
        }

        # 🎯 扩充后的统一品牌映射表 (英文→中文) - 2025年最新版本
        self.brand_mapping = {
            # === 大家电品牌（海尔集团） ===
            "haier": "海尔", "海尔": "海尔", "HAIER": "海尔", "Haier": "海尔",
            "casarte": "卡萨帝", "卡萨帝": "卡萨帝", "CASARTE": "卡萨帝", "Casarte": "卡萨帝",
            "leader": "统帅", "统帅": "统帅", "LEADER": "统帅", "Leader": "统帅",
            "aqua": "AQUA", "AQUA": "AQUA", "Aqua": "AQUA",

            # === 大家电品牌（美的集团） ===
            "midea": "美的", "美的": "美的", "MIDEA": "美的", "Midea": "美的",
            "littleswan": "小天鹅", "小天鹅": "小天鹅", "LITTLESWAN": "小天鹅", "LittleSwan": "小天鹅",
            "colmo": "COLMO", "COLMO": "COLMO", "Colmo": "COLMO",
            "华凌": "华凌", "hualing": "华凌", "HUALING": "华凌", "Hualing": "华凌",
            "布谷": "布谷", "bugu": "布谷", "BUGU": "布谷", "Bugu": "布谷",
            "toshiba": "东芝", "东芝": "东芝", "TOSHIBA": "东芝", "Toshiba": "东芝",

            # === 大家电品牌（格力集团） ===
            "gree": "格力", "格力": "格力", "GREE": "格力", "Gree": "格力",
            "tosot": "大松", "大松": "大松", "TOSOT": "大松", "Tosot": "大松",
            "晶弘": "晶弘", "kinghome": "晶弘", "KINGHOME": "晶弘", "Kinghome": "晶弘",

            # === 大家电品牌（其他中国品牌） ===
            "tcl": "TCL", "TCL": "TCL", "Tcl": "TCL",
            "雷鸟": "雷鸟", "thunderbird": "雷鸟", "THUNDERBIRD": "雷鸟", "Thunderbird": "雷鸟",
            "hisense": "海信", "海信": "海信", "HISENSE": "海信", "Hisense": "海信",
            "kelon": "科龙", "科龙": "科龙", "KELON": "科龙", "Kelon": "科龙",
            "ronshen": "容声", "容声": "容声", "RONSHEN": "容声", "Ronshen": "容声",
            "vidaa": "VIDAA", "VIDAA": "VIDAA", "Vidaa": "VIDAA",
            "changhong": "长虹", "长虹": "长虹", "CHANGHONG": "长虹", "Changhong": "长虹",
            "chiq": "长虹", "CHiQ": "长虹", "CHIQ": "长虹", "ChiQ": "长虹",
            "konka": "康佳", "康佳": "康佳", "KONKA": "康佳", "Konka": "康佳",
            "skyworth": "创维", "创维": "创维", "SKYWORTH": "创维", "Skyworth": "创维",
            "coocaa": "酷开", "酷开": "酷开", "COOCAA": "酷开", "Coocaa": "酷开",
            "aux": "奥克斯", "奥克斯": "奥克斯", "AUX": "奥克斯", "Aux": "奥克斯",
            "chigo": "志高", "志高": "志高", "CHIGO": "志高", "Chigo": "志高",
            "扬子": "扬子", "yangzi": "扬子", "YANGZI": "扬子", "Yangzi": "扬子",
            "扬子佳": "扬子", "yangzijia": "扬子", "YANGZIJIA": "扬子",
            "新飞": "新飞", "xinfei": "新飞", "XINFEI": "新飞", "Xinfei": "新飞",

            # === 国际大家电品牌 ===
            "siemens": "西门子", "西门子": "西门子", "SIEMENS": "西门子", "Siemens": "西门子",
            "bosch": "博世", "博世": "博世", "BOSCH": "博世", "Bosch": "博世",
            "panasonic": "松下", "松下": "松下", "PANASONIC": "松下", "Panasonic": "松下",
            "samsung": "三星", "三星": "三星", "SAMSUNG": "三星", "Samsung": "三星",
            "lg": "LG", "LG": "LG", "Lg": "LG", "lG": "LG",
            "whirlpool": "惠而浦", "惠而浦": "惠而浦", "WHIRLPOOL": "惠而浦", "Whirlpool": "惠而浦",
            "electrolux": "伊莱克斯", "伊莱克斯": "伊莱克斯", "ELECTROLUX": "伊莱克斯", "Electrolux": "伊莱克斯",
            "liebherr": "利勃海尔", "利勃海尔": "利勃海尔", "LIEBHERR": "利勃海尔", "Liebherr": "利勃海尔",

            # === 小家电品牌（中国） ===
            "joyoung": "九阳", "九阳": "九阳", "JOYOUNG": "九阳", "Joyoung": "九阳",
            "supor": "苏泊尔", "苏泊尔": "苏泊尔", "SUPOR": "苏泊尔", "Supor": "苏泊尔",
            "bear": "小熊", "小熊": "小熊", "BEAR": "小熊", "Bear": "小熊",
            "小熊电器": "小熊", "bearelectric": "小熊", "BEARELECTRIC": "小熊",
            "galanz": "格兰仕", "格兰仕": "格兰仕", "GALANZ": "格兰仕", "Galanz": "格兰仕",
            "royalstar": "荣事达", "荣事达": "荣事达", "ROYALSTAR": "荣事达", "Royalstar": "荣事达",
            "liven": "利仁", "利仁": "利仁", "LIVEN": "利仁", "Liven": "利仁",
            "deerma": "德尔玛", "德尔玛": "德尔玛", "DEERMA": "德尔玛", "Deerma": "德尔玛",
            "buydeem": "北鼎", "北鼎": "北鼎", "BUYDEEM": "北鼎", "Buydeem": "北鼎",

            # === 小米生态链 ===
            "xiaomi": "小米", "小米": "小米", "XIAOMI": "小米", "Xiaomi": "小米",
            "mi": "小米", "MI": "小米", "Mi": "小米", "mI": "小米",
            "米家": "小米", "mijia": "小米", "MIJIA": "小米", "Mijia": "小米",
            "素士": "素士", "soocas": "素士", "SOOCAS": "素士", "Soocas": "素士",
            "云米": "云米", "viomi": "云米", "VIOMI": "云米", "Viomi": "云米",
            "追觅": "追觅", "dreame": "追觅", "DREAME": "追觅", "Dreame": "追觅",

            # === 国际小家电品牌 ===
            "morphyrichards": "摩飞", "摩飞": "摩飞", "MORPHYRICHARDS": "摩飞", "MorphyRichards": "摩飞",
            "morphy": "摩飞", "MORPHY": "摩飞", "Morphy": "摩飞",
            "philips": "飞利浦", "飞利浦": "飞利浦", "PHILIPS": "飞利浦", "Philips": "飞利浦",
            "dyson": "戴森", "戴森": "戴森", "DYSON": "戴森", "Dyson": "戴森",
            "braun": "博朗", "博朗": "博朗", "BRAUN": "博朗", "Braun": "博朗",
            "tefal": "特福", "特福": "特福", "TEFAL": "特福", "Tefal": "特福",
            "delonghi": "德龙", "德龙": "德龙", "DELONGHI": "德龙", "DeLonghi": "德龙",
            "zojirushi": "象印", "象印": "象印", "ZOJIRUSHI": "象印", "Zojirushi": "象印",
            "tiger": "虎牌", "虎牌": "虎牌", "TIGER": "虎牌", "Tiger": "虎牌",

            # === 数码电器品牌（中国手机） ===
            "huawei": "华为", "华为": "华为", "HUAWEI": "华为", "Huawei": "华为",
            "honor": "荣耀", "荣耀": "荣耀", "HONOR": "荣耀", "Honor": "荣耀",
            "oppo": "OPPO", "OPPO": "OPPO", "Oppo": "OPPO", "opPO": "OPPO",
            "vivo": "vivo", "vivo": "vivo", "VIVO": "vivo", "Vivo": "vivo",
            "oneplus": "一加", "一加": "一加", "ONEPLUS": "一加", "OnePlus": "一加",
            "realme": "真我", "真我": "真我", "REALME": "真我", "Realme": "真我",
            "meizu": "魅族", "魅族": "魅族", "MEIZU": "魅族", "Meizu": "魅族",
            "zte": "中兴", "中兴": "中兴", "ZTE": "中兴", "Zte": "中兴",
            "nubia": "努比亚", "努比亚": "努比亚", "NUBIA": "努比亚", "Nubia": "努比亚",

            # === 国际数码品牌 ===
            "apple": "苹果", "苹果": "苹果", "APPLE": "苹果", "Apple": "苹果",
            "iphone": "苹果", "iPhone": "苹果", "IPHONE": "苹果", "IPhone": "苹果",
            "lenovo": "联想", "联想": "联想", "LENOVO": "联想", "Lenovo": "联想",
            "thinkpad": "联想", "ThinkPad": "联想", "THINKPAD": "联想", "Thinkpad": "联想",
            "hasee": "神舟", "神舟": "神舟", "HASEE": "神舟", "Hasee": "神舟",
            "tongfang": "同方", "同方": "同方", "TONGFANG": "同方", "Tongfang": "同方",
            "dell": "戴尔", "戴尔": "戴尔", "DELL": "戴尔", "Dell": "戴尔",
            "hp": "惠普", "惠普": "惠普", "HP": "惠普", "Hp": "惠普",
            "asus": "华硕", "华硕": "华硕", "ASUS": "华硕", "Asus": "华硕",
            "acer": "宏碁", "宏碁": "宏碁", "ACER": "宏碁", "Acer": "宏碁",
            "msi": "微星", "微星": "微星", "MSI": "微星", "Msi": "微星",

            # === 骑行服装品牌（中国） ===
            "santic": "森地客", "森地客": "森地客", "SANTIC": "森地客", "Santic": "森地客",
            "spakct": "思帕客", "思帕客": "思帕客", "SPAKCT": "思帕客", "Spakct": "思帕客",
            "mysenlan": "迈森兰", "迈森兰": "迈森兰", "MYSENLAN": "迈森兰", "Mysenlan": "迈森兰",
            "msl": "迈森兰", "MSL": "迈森兰", "Msl": "迈森兰",
            "grc": "GRC", "GRC": "GRC", "Grc": "GRC", "gRC": "GRC",
            "jakroo": "捷酷", "捷酷": "捷酷", "JAKROO": "捷酷", "Jakroo": "捷酷",
            "ccn": "CCN", "CCN": "CCN", "Ccn": "CCN", "cCN": "CCN",
            "sobike": "速盟", "速盟": "速盟", "SOBIKE": "速盟", "Sobike": "速盟",
            "lightning": "闪电", "闪电": "闪电", "LIGHTNING": "闪电", "Lightning": "闪电",
            "qiji": "骑记", "骑记": "骑记", "QIJI": "骑记", "Qiji": "骑记",
            "rockbros": "洛克兄弟", "洛克兄弟": "洛克兄弟", "ROCKBROS": "洛克兄弟", "RockBros": "洛克兄弟",
            "forever": "永久", "永久": "永久", "FOREVER": "永久", "Forever": "永久",
            "phoenix": "凤凰", "凤凰": "凤凰", "PHOENIX": "凤凰", "Phoenix": "凤凰",
            "pigeon": "飞鸽", "飞鸽": "飞鸽", "PIGEON": "飞鸽", "Pigeon": "飞鸽",

            # === 国际骑行品牌 ===
            "decathlon": "迪卡侬", "迪卡侬": "迪卡侬", "DECATHLON": "迪卡侬", "Decathlon": "迪卡侬",
            "btwin": "迪卡侬", "BTWIN": "迪卡侬", "Btwin": "迪卡侬",
            "rapha": "Rapha", "Rapha": "Rapha", "RAPHA": "Rapha", "rAPHA": "Rapha",
            "castelli": "Castelli", "Castelli": "Castelli", "CASTELLI": "Castelli", "cASTELLI": "Castelli",
            "assos": "Assos", "Assos": "Assos", "ASSOS": "Assos", "aSsos": "Assos",
            "pearlizumi": "Pearl Izumi", "Pearl Izumi": "Pearl Izumi", "PEARLIZUMI": "Pearl Izumi",
            "pearl": "Pearl Izumi", "PEARL": "Pearl Izumi", "Pearl": "Pearl Izumi",
            "craft": "Craft", "Craft": "Craft", "CRAFT": "Craft", "cRAFT": "Craft",
            "endura": "Endura", "Endura": "Endura", "ENDURA": "Endura", "eNDURA": "Endura",
            "gore": "Gore", "Gore": "Gore", "GORE": "Gore", "gORE": "Gore",
            "goretex": "Gore", "GORETEX": "Gore", "GoreTex": "Gore",
            "specialized": "闪电", "闪电": "闪电", "SPECIALIZED": "闪电", "Specialized": "闪电",
            "trek": "Trek", "Trek": "Trek", "TREK": "Trek", "tREK": "Trek",
            "giant": "捷安特", "捷安特": "捷安特", "GIANT": "捷安特", "Giant": "捷安特",
            "merida": "美利达", "美利达": "美利达", "MERIDA": "美利达", "Merida": "美利达",

            # === 小众品牌和新兴品牌 ===
            "sevenstars": "七星", "seven stars": "七星", "七星": "七星",
            "SEVENSTARS": "七星", "Sevenstars": "七星", "SevenStars": "七星",
            "twinwash": "町渥", "町渥": "町渥", "TWINWASH": "町渥", "Twinwash": "町渥",
            "candara": "Candara", "Candara": "Candara", "CANDARA": "Candara", "cANDARA": "Candara",
            "lingmu": "菱木", "菱木": "菱木", "LINGMU": "菱木", "Lingmu": "菱木",
            "meiling": "美凌", "美凌": "美凌", "MEILING": "美凌", "MeiLing": "美凌",
            "haer": "海尔", "HAER": "海尔", "Haer": "海尔", "hAER": "海尔",
            "tronssra": "TRONSSRA", "TRONSSRA": "TRONSSRA", "Tronssra": "TRONSSRA", "tRONSSRA": "TRONSSRA",
            "hairi": "海日", "海日": "海日", "HAIRI": "海日", "Hairi": "海日",
        }

        # 🎯 核心2：编译正则表达式 (轻量化正则增强)
        self._compiled_patterns = [
            # 模式1: 中英文混合 "Brand/品牌" 或 "品牌/Brand"
            (re.compile(r'^([A-Za-z]+)/([\u4e00-\u9fa5]+)'), 'en_cn_mixed'),
            (re.compile(r'^([\u4e00-\u9fa5]+)/([A-Za-z]+)'), 'cn_en_mixed'),

            # 模式2: 英文品牌 + 斜杠分隔
            (re.compile(r'^([A-Z][a-z]+)/'), 'en_brand_slash'),

            # 模式3: 纯英文大写品牌
            (re.compile(r'^([A-Z]{2,8})\s'), 'en_brand_upper'),

            # 模式4: 中文品牌开头 (2-4个字符)
            (re.compile(r'^([\u4e00-\u9fa5]{2,4})(?=冰箱|洗衣机|空调|电视|微波炉)'), 'cn_brand_product'),

            # 模式5: 德国/进口品牌
            (re.compile(r'德国([A-Za-z]+)'), 'german_brand'),
            (re.compile(r'进口([A-Za-z]+)'), 'import_brand'),

            # 模式6: 营销标签后的品牌
            (re.compile(r'【[^】]*】([A-Za-z\u4e00-\u9fa5]{2,8})'), 'after_tag_brand'),

            # 模式7: 品牌 + 明确产品词
            (re.compile(r'^([A-Za-z\u4e00-\u9fa5]{2,8})(?=冰箱|洗衣机|空调|电视|微波炉)'), 'brand_product'),

            # 模式8: 限时特价等前缀后的品牌
            (re.compile(r'限时特价[！!]*([A-Za-z\u4e00-\u9fa5]{2,8})'), 'after_promo_brand'),

            # 模式9: 括号中的品牌
            (re.compile(r'[（(]([A-Za-z\u4e00-\u9fa5]{2,8})[）)]'), 'bracket_brand'),
        ]

        # 🎯 多关键词状态管理增强 - 支持关键词级别的数据隔离
        self.keyword_context = {
            'current': None,                    # 当前处理的关键词
            'previous': None,                   # 上一个关键词
            'data_mapping': {},                 # keyword -> data_list 数据归属映射
            'collection_stats': {},             # keyword -> stats 收集统计
            'processing_status': {},            # keyword -> status 处理状态
            'sequence_index': 0                 # 关键词处理序号
        }
        
        # 添加缺失的属性
        self._collected_goods_ids = set()  # 用于去重的商品ID集合

        logger.info("API响应监听器初始化完成")

    def detect_subsidy_status(self, item_data: Dict) -> bool:
        """
        🎯 增强的百亿补贴检测逻辑 - 支持多重检测机制
        
        Args:
            item_data: 商品数据字典
            
        Returns:
            bool: 是否为百亿补贴商品
        """
        try:
            # 方法1：iconIds检测（主要方法）- 🔧 修复：同时检查两种命名格式
            icon_ids = item_data.get('iconIds', item_data.get('icon_ids', []))
            
            # 列表格式检测
            if isinstance(icon_ids, list) and 20001 in icon_ids:
                return True
            
            # 字符串格式检测
            if isinstance(icon_ids, str) and '20001' in icon_ids:
                return True
            
            # 其他格式检测
            if icon_ids and str(icon_ids).find('20001') != -1:
                return True
            
            # 方法2：icon_list检测（备用方法）
            icon_list = item_data.get('icon_list', [])
            if isinstance(icon_list, list):
                for icon in icon_list:
                    if isinstance(icon, dict):
                        # 检查icon ID
                        if icon.get('id') == 20001:
                            return True
                        # 检查icon URL特征
                        icon_url = icon.get('url', '')
                        if icon_url and ('social/pincard/1/share.png' in icon_url or
                                       'pincard/1/share.png' in icon_url):
                            return True
            
            # 方法3：文本检测（最后备用）
            goods_name = item_data.get('goods_name', '')
            if isinstance(goods_name, str):
                subsidy_indicators = ['百亿补贴', 'billion', 'subsidy']
                item_text = goods_name.lower()
                if any(indicator in item_text for indicator in subsidy_indicators):
                    return True
            
            # 方法4：标签检测
            tag_list = item_data.get('tag_list', [])
            if isinstance(tag_list, list):
                for tag in tag_list:
                    if isinstance(tag, str) and ('百亿补贴' in tag or '拼多多百亿补贴' in tag):
                        return True
            
            return False
        
        except Exception as e:
            logger.debug(f"百亿补贴检测异常: {e}")
            return False

    async def switch_keyword_context(self, keyword: str) -> None:
        """
        关键词上下文切换 - 支持多关键词数据隔离

        Args:
            keyword: 新的关键词
        """
        try:
            old_keyword = self.keyword_context['current']

            # 更新关键词上下文
            self.keyword_context['previous'] = old_keyword
            self.keyword_context['current'] = keyword
            self.keyword_context['sequence_index'] += 1

            # 初始化新关键词的数据结构
            if keyword not in self.keyword_context['data_mapping']:
                self.keyword_context['data_mapping'][keyword] = []
                self.keyword_context['collection_stats'][keyword] = {
                    'total_collected': 0,
                    'start_time': time.time(),
                    'last_update': time.time()
                }
                self.keyword_context['processing_status'][keyword] = 'started'

            # 更新当前关键词（保持向后兼容）
            self.current_keyword = keyword

            logger.info(f"🔄 关键词上下文切换: {old_keyword} → {keyword} (序号: {self.keyword_context['sequence_index']})")

        except Exception as e:
            logger.error(f"关键词上下文切换失败: {e}")

    def get_keyword_data(self, keyword: str) -> List[Dict]:
        """
        获取特定关键词的数据

        Args:
            keyword: 关键词

        Returns:
            该关键词对应的数据列表
        """
        return self.keyword_context['data_mapping'].get(keyword, [])

    def clear_keyword_data(self, keyword: str) -> None:
        """
        清理特定关键词的数据

        Args:
            keyword: 要清理的关键词
        """
        try:
            if keyword in self.keyword_context['data_mapping']:
                cleared_count = len(self.keyword_context['data_mapping'][keyword])
                self.keyword_context['data_mapping'][keyword].clear()
                self.keyword_context['collection_stats'][keyword]['total_collected'] = 0
                logger.debug(f"🧹 已清理关键词 '{keyword}' 的 {cleared_count} 条数据")
        except Exception as e:
            logger.error(f"清理关键词数据失败: {e}")

    def get_keyword_stats(self, keyword: str = None) -> Dict:
        """
        获取关键词统计信息

        Args:
            keyword: 指定关键词，如果为None则返回当前关键词统计

        Returns:
            统计信息字典
        """
        target_keyword = keyword or self.keyword_context['current']
        if target_keyword and target_keyword in self.keyword_context['collection_stats']:
            stats = self.keyword_context['collection_stats'][target_keyword].copy()
            stats['keyword'] = target_keyword
            stats['data_count'] = len(self.keyword_context['data_mapping'].get(target_keyword, []))
            return stats
        return {}

    async def setup_monitoring(self, page: Page, keyword: str = "") -> None:
        """设置响应监听（带生命周期管理）- 增强多关键词支持"""
        # 🎯 使用新的关键词上下文管理
        old_keyword = self.keyword_context.get('current') or self.current_keyword

        # 切换关键词上下文
        if keyword and keyword != old_keyword:
            await self.switch_keyword_context(keyword)

        # 如果页面已经有监听器，且只是关键词变化，不要清理监听器
        if page in self._active_pages and old_keyword and keyword != old_keyword:
            logger.info(f"✅ 切换关键词: {old_keyword} → {keyword}，保持监听器活跃")
            return
        
        # 清理已存在的监听器（仅在必要时）
        if page not in self._active_pages:
            await self.cleanup_monitoring(page)
        
            # 创建页面专属的处理器
            handler = lambda response: asyncio.create_task(self._handle_response(response))
            self._response_handlers[page] = handler
            self._active_pages.add(page)
            
            # 设置响应监听器
            page.on("response", handler)
            
            # 监听页面关闭事件，自动清理
            page.on("close", lambda: asyncio.create_task(self.cleanup_monitoring(page)))
        
        # 启动处理器（如果未启动）
        if not self._queue_processor_task:
            await self.start_processing()
        
        logger.info(f"已设置API响应监听，关键词: {keyword}")
    
    async def cleanup_monitoring(self, page: Page) -> None:
        """清理响应监听器（增强版）"""
        try:
            # 清理页面专属的处理器
            if page in self._response_handlers:
                handler = self._response_handlers.pop(page)
                try:
                    page.remove_listener("response", handler)
                    logger.debug(f"✅ 已移除页面响应监听器")
                except Exception as e:
                    logger.debug(f"⚠️ 移除监听器时出错: {e}")

            # 从活跃页面集合中移除
            self._active_pages.discard(page)

            # 清理页面相关的待处理响应
            initial_queue_size = self._response_queue.qsize()
            temp_queue = asyncio.Queue()

            # 重新构建队列，排除当前页面的响应
            while not self._response_queue.empty():
                try:
                    response_data = self._response_queue.get_nowait()
                    # 检查响应是否来自当前页面（通过URL或其他标识）
                    if not self._is_response_from_page(response_data, page):
                        await temp_queue.put(response_data)
                except asyncio.QueueEmpty:
                    break

            # 替换队列
            self._response_queue = temp_queue

            cleaned_count = initial_queue_size - self._response_queue.qsize()
            if cleaned_count > 0:
                logger.debug(f"🧹 清理了 {cleaned_count} 个待处理响应")

            logger.debug(f"✅ 页面监听器清理完成")

        except Exception as e:
            logger.warning(f"⚠️ 清理监听器时出现异常: {e}")

    def _is_response_from_page(self, response_data: Dict, page: Page) -> bool:
        """判断响应是否来自指定页面"""
        try:
            # 这里可以通过URL、时间戳或其他标识来判断
            # 简单实现：如果页面已关闭，认为所有旧响应都应该清理
            return False  # 保守策略：清理所有待处理响应
        except Exception:
            return False

    def get_monitoring_stats(self) -> Dict[str, Any]:
        """获取监听器状态统计"""
        processor_running = False
        if hasattr(self, '_queue_processor_task') and self._queue_processor_task is not None:
            processor_running = not self._queue_processor_task.done()

        return {
            "active_pages": len(self._active_pages),
            "response_handlers": len(self._response_handlers),
            "queue_size": self._response_queue.qsize(),
            "current_keyword": self.current_keyword,
            "processor_running": processor_running,
            "total_responses_processed": getattr(self, '_total_responses_processed', 0),
            "total_goods_extracted": getattr(self, '_total_goods_extracted', 0)
        }

    def clear_cached_data(self) -> None:
        """清理缓存的数据（用于关键词切换）"""
        try:
            # 清空待处理队列
            while not self._response_queue.empty():
                try:
                    self._response_queue.get_nowait()
                except asyncio.QueueEmpty:
                    break
            
            # 重置统计信息
            self.metrics["cached_responses"] = 0
            logger.debug("✅ API监听器缓存数据已清理")
        except Exception as e:
            logger.warning(f"清理缓存数据时出错: {e}")
    
    def clear_response_cache(self) -> None:
        """清理响应缓存（内存优化）"""
        try:
            # 清理响应处理器
            self._response_handlers.clear()
            
            # 清理缓存队列
            self.clear_cached_data()
            
            # 清理监控数据中的冗余字段
            for item in self.monitored_data:
                # 只保留必要字段，删除大字段
                if 'raw_response' in item:
                    del item['raw_response']
                if 'debug_info' in item:
                    del item['debug_info']
            
            # 强制垃圾回收
            import gc
            collected = gc.collect()
            
            logger.debug(f"✅ API响应缓存已清理，释放 {collected} 个对象")
        except Exception as e:
            logger.warning(f"清理响应缓存时出错: {e}")
    
    def log_monitoring_stats(self, stage: str = "") -> None:
        """记录监听器状态统计"""
        stats = self.get_monitoring_stats()
        processor_status = "运行中" if stats.get('processor_running', False) else "已停止"
        logger.info(f"📊 [{stage}] 监听器状态 - 活跃页面: {stats['active_pages']}, "
                   f"队列大小: {stats['queue_size']}, 处理器: {processor_status}")

    async def start_processing(self) -> None:
        """启动响应处理器"""
        if not self._queue_processor_task:
            self._queue_processor_task = asyncio.create_task(self._process_response_queue())
            logger.info("响应处理器已启动")
    
    async def stop_processing(self) -> None:
        """停止响应处理器"""
        if self._queue_processor_task:
            self._queue_processor_task.cancel()
            await asyncio.gather(*self._processing_tasks, return_exceptions=True)
            logger.info("响应处理器已停止")
    
    async def _handle_response(self, response: Response) -> None:
        """处理API响应（改进版）"""
        self.metrics["total_responses"] += 1
        
        # 添加调试日志
        if "goods" in response.url or "search" in response.url or "api" in response.url:
            logger.debug(f"收到响应: {response.url[:100]}... 状态: {response.status}")
        
        try:
            # 快速预检查
            if not self._should_process_response(response):
                return
            
            # 尝试加入队列
            try:
                self._response_queue.put_nowait(response)
                self.metrics["queued_responses"] += 1
            except asyncio.QueueFull:
                self.metrics["dropped_responses"] += 1
                logger.warning("响应队列已满，丢弃响应")
                
        except Exception as e:
            logger.error(f"响应处理异常: {e}")
    
    def _should_process_response(self, response: Response) -> bool:
        """快速判断是否需要处理响应"""
        # 排除热搜API
        if "search_hotquery" in response.url or "hotquery" in response.url:
            logger.debug(f"忽略热搜API: {response.url}")
            return False
        
        # URL匹配
        if not self._pattern_regex.search(response.url):
            return False
        
        # Content-Type检查
        content_type = response.headers.get("content-type", "")
        if "application/json" not in content_type:
            return False
        
        # 状态码检查
        if response.status not in (200, 304):
            return False
            
        return True
    
    async def _process_response_queue(self) -> None:
        """响应队列处理器"""
        while True:
            try:
                response = await self._response_queue.get()
                
                # 如果暂停处理，等待恢复
                while self._processing_paused:
                    await asyncio.sleep(0.1)
                
                # 创建处理任务
                task = asyncio.create_task(self._process_single_response(response))
                self._processing_tasks.add(task)
                task.add_done_callback(self._processing_tasks.discard)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"队列处理异常: {e}")
    
    async def _process_single_response(self, response: Response) -> None:
        """处理单个响应"""
        async with self._processing_semaphore:
            start_time = time.time()
            try:
                await self._process_response(response)
                self.metrics["processed_responses"] += 1
            except Exception as e:
                self.metrics["processing_errors"] += 1
                logger.error(f"处理响应失败: {e}")
            finally:
                # 更新平均处理时间
                processing_time = time.time() - start_time
                self._update_avg_processing_time(processing_time)
    
    async def _process_response(self, response: Response) -> None:
        """带熔断机制的响应处理"""
        # 检查熔断器
        if self.circuit_breaker_open:
            if time.time() - self.last_error_time > self.reset_timeout:
                self.circuit_breaker_open = False
                self.error_counter = 0
                logger.info("熔断器已重置")
            else:
                logger.warning("熔断器开启，跳过处理")
                return
        
        try:
            # 检查响应大小
            content_length = response.headers.get("content-length")
            if content_length and int(content_length) > 10 * 1024 * 1024:  # 10MB限制
                logger.warning(f"响应过大，跳过处理: {content_length} bytes")
                return
            
            # 设置超时
            try:
                response_body = await asyncio.wait_for(
                    response.body(), 
                    timeout=5.0  # 5秒超时
                )
            except asyncio.TimeoutError:
                logger.error(f"响应读取超时: {response.url}")
                return
            
            # 验证响应
            if not response_body:
                return
                
            # 解析数据
            await self._parse_response_data(
                response.url, 
                response_body, 
                response.status
            )
            
            # 成功处理，重置错误计数
            if self.error_counter > 0:
                self.error_counter = max(0, self.error_counter - 1)
                
        except Exception as e:
            self._handle_processing_error(e)
    
    def _handle_processing_error(self, error: Exception) -> None:
        """错误处理和熔断逻辑"""
        self.error_counter += 1
        self.last_error_time = time.time()
        
        # 错误分类
        if isinstance(error, json.JSONDecodeError):
            logger.error(f"JSON解析错误: {error}")
        elif isinstance(error, asyncio.TimeoutError):
            logger.error(f"处理超时: {error}")
        else:
            logger.error(f"未知错误: {error}")
        
        # 检查是否需要开启熔断器
        if self.error_counter >= self.error_threshold:
            self.circuit_breaker_open = True
            logger.error(f"错误次数过多({self.error_counter})，开启熔断器")
    
    async def _parse_response_data(self, url: str, body: bytes, status_code: int) -> None:
        """解析响应数据 - 复用现有逻辑"""
        try:
            # 解析JSON
            if HAS_ORJSON:
                data = orjson.loads(body)
            else:
                data = json.loads(body.decode('utf-8'))
            
            # 调试：显示响应数据的结构
            logger.debug(f"API响应URL: {url}")
            logger.debug(f"响应数据键: {list(data.keys()) if isinstance(data, dict) else type(data)}")
            
            # 特别处理拼多多搜索API
            if "/proxy/api/search" in url and "items" in data:
                items = data.get("items", [])
                if items and len(items) > 0:
                    logger.info(f"🎯 检测到商品搜索API，包含 {len(items)} 个商品")
                    # 检查第一个商品的结构
                    first_item = items[0] if isinstance(items, list) else None
                    if first_item and isinstance(first_item, dict):
                        logger.debug(f"商品数据结构示例: {list(first_item.keys())[:10]}")
                        # 如果是拼多多的特殊结构，处理item_data
                        if "item_type" in first_item and "item_data" in first_item:
                            logger.debug(f"item_type值: {first_item.get('item_type')} (类型: {type(first_item.get('item_type'))})")
                            item_data = first_item.get("item_data", {})
                            if isinstance(item_data, dict):
                                logger.debug(f"item_data内部结构: {list(item_data.keys())[:10]}")
                                # 如果有goods_model，打印其内容
                                if "goods_model" in item_data:
                                    goods_model = item_data.get("goods_model", {})
                                    if isinstance(goods_model, dict):
                                        logger.debug(f"goods_model内部结构: {list(goods_model.keys())[:10]}")
                                        logger.debug(f"goods_model示例 - goods_id: {goods_model.get('goods_id', 'N/A')}")
                                        logger.debug(f"goods_model示例 - goods_name: {goods_model.get('goods_name', goods_model.get('short_name', 'N/A'))}")
                                        logger.debug(f"goods_model示例 - price: {goods_model.get('price', goods_model.get('group_price', 'N/A'))}")
                                else:
                                    # 打印一些示例字段值
                                    logger.debug(f"示例数据 - goods_id: {item_data.get('goods_id', 'N/A')}")
                                    logger.debug(f"示例数据 - goods_name: {item_data.get('goods_name', item_data.get('short_name', 'N/A'))}")
                                    logger.debug(f"示例数据 - price: {item_data.get('price', item_data.get('group_price', 'N/A'))}")
            
            # 如果是加密API，尝试查找可能的数据字段
            if "/xg/pfb/" in url:
                # 拼多多加密API可能使用不同的数据结构
                logger.debug(f"检测到加密API，尝试解析...")
                # 遍历所有字段查找可能的商品列表
                for key, value in data.items() if isinstance(data, dict) else []:
                    if isinstance(value, list) and len(value) > 0:
                        logger.debug(f"发现列表字段 '{key}'，长度: {len(value)}")
                        # 检查是否是商品数据
                        if len(value) > 0 and isinstance(value[0], dict):
                            sample_keys = list(value[0].keys())[:5]
                            logger.debug(f"样本键: {sample_keys}")
            
            # 提取商品数据
            goods_list = await self._extract_goods_data(data, {})
            
            if goods_list:
                logger.info(f"监听到 {len(goods_list)} 条商品数据")
                self.monitored_data.extend(goods_list)
                self.metrics["successful_extractions"] += 1
                
                # 触发回调
                if self.data_callback:
                    await self.data_callback(goods_list)
            else:
                logger.debug(f"未能从响应中提取商品数据")
                    
        except json.JSONDecodeError as e:
            self.metrics["parse_errors"] += 1
            logger.error(f"JSON解析失败: {e}")
        except Exception as e:
            logger.error(f"数据解析异常: {e}")
    
    async def _extract_goods_data(self, data: Dict, query_params: Dict) -> List[Dict]:
        """提取商品数据 - 复用原有逻辑"""
        goods_list = []
        
        # 使用配置的数据路径
        for path in self.data_paths:
            try:
                current = data
                for key in path:
                    if isinstance(current, dict) and key in current:
                        current = current[key]
                    else:
                        current = None
                        break
                
                if current and isinstance(current, list):
                    logger.debug(f"通过路径 {' -> '.join(path)} 找到 {len(current)} 条数据")
                    # 添加调试：查看第一条数据的结构
                    if current:
                        first_item = current[0] if len(current) > 0 else None
                        if first_item:
                            logger.debug(f"第一条数据结构: {list(first_item.keys()) if isinstance(first_item, dict) else type(first_item)}")
                    for item in current:
                        # 只处理字典类型的商品数据
                        if isinstance(item, dict):
                            # 处理拼多多特殊的item结构
                            if "item_type" in item and "item_data" in item:
                                item_type = item.get("item_type")
                                logger.debug(f"处理item - type: {item_type}, data keys: {list(item.get('item_data', {}).keys())[:5] if isinstance(item.get('item_data'), dict) else 'Not a dict'}")
                                
                                # 扩展商品类型检查，包括更多可能的值
                                # 0: 普通商品, 1: 推广商品, 2: 商品也可能是2
                                if item_type in [0, 1, 2, 3, "0", "1", "2", "3", "goods", "product", "item"]:
                                    item_data = item.get("item_data", {})
                                    if isinstance(item_data, dict):
                                        # 拼多多特殊处理：商品数据在 goods_model 字段中
                                        if "goods_model" in item_data:
                                            goods_model = item_data.get("goods_model", {})
                                            if isinstance(goods_model, dict):
                                                goods_data = await self._parse_single_goods(goods_model)
                                                if goods_data:
                                                    # 🎯 增强关键词归属管理
                                                    current_keyword = self.keyword_context.get('current') or self.current_keyword
                                                    goods_data['keyword'] = current_keyword  # 前端需要keyword字段
                                                    goods_data['search_keyword'] = current_keyword  # 保持兼容性
                                                    goods_data['keyword_sequence'] = self.keyword_context.get('sequence_index', 0)

                                                    # 添加到关键词数据映射
                                                    if current_keyword:
                                                        # 确保 keyword_context 有正确的结构
                                                        if 'data_mapping' not in self.keyword_context:
                                                            self.keyword_context['data_mapping'] = {}
                                                        if current_keyword not in self.keyword_context['data_mapping']:
                                                            self.keyword_context['data_mapping'][current_keyword] = []
                                                        self.keyword_context['data_mapping'][current_keyword].append(goods_data)

                                                        # 更新统计信息
                                                        if current_keyword in self.keyword_context['collection_stats']:
                                                            self.keyword_context['collection_stats'][current_keyword]['total_collected'] += 1
                                                            self.keyword_context['collection_stats'][current_keyword]['last_update'] = time.time()

                                                    goods_list.append(goods_data)
                                                else:
                                                    logger.debug(f"解析失败 - goods_model: {list(goods_model.keys())[:10]}")
                                        else:
                                            # 标准处理
                                            goods_data = await self._parse_single_goods(item_data)
                                            if goods_data:
                                                # 🎯 增强关键词归属管理
                                                current_keyword = self.keyword_context.get('current') or self.current_keyword
                                                goods_data['search_keyword'] = current_keyword
                                                goods_data['keyword_sequence'] = self.keyword_context.get('sequence_index', 0)

                                                # 添加到关键词数据映射
                                                if current_keyword:
                                                    # 确保 keyword_context 有正确的结构
                                                    if 'data_mapping' not in self.keyword_context:
                                                        self.keyword_context['data_mapping'] = {}
                                                    if current_keyword not in self.keyword_context['data_mapping']:
                                                        self.keyword_context['data_mapping'][current_keyword] = []
                                                    self.keyword_context['data_mapping'][current_keyword].append(goods_data)

                                                    # 更新统计信息
                                                    if current_keyword in self.keyword_context['collection_stats']:
                                                        self.keyword_context['collection_stats'][current_keyword]['total_collected'] += 1
                                                        self.keyword_context['collection_stats'][current_keyword]['last_update'] = time.time()

                                                goods_list.append(goods_data)
                                            else:
                                                logger.debug(f"解析失败 - item_data: {list(item_data.keys())[:10]}")
                                else:
                                    logger.debug(f"跳过非商品类型: {item_type}")
                            else:
                                # 标准商品数据结构
                                goods_data = await self._parse_single_goods(item)
                                if goods_data:
                                    # 🎯 增强关键词归属管理
                                    current_keyword = self.keyword_context.get('current') or self.current_keyword
                                    goods_data['search_keyword'] = current_keyword
                                    goods_data['keyword_sequence'] = self.keyword_context.get('sequence_index', 0)

                                    # 添加到关键词数据映射
                                    if current_keyword:
                                        # 确保 keyword_context 有正确的结构
                                        if 'data_mapping' not in self.keyword_context:
                                            self.keyword_context['data_mapping'] = {}
                                        if current_keyword not in self.keyword_context['data_mapping']:
                                            self.keyword_context['data_mapping'][current_keyword] = []
                                        self.keyword_context['data_mapping'][current_keyword].append(goods_data)

                                        # 更新统计信息
                                        if current_keyword in self.keyword_context['collection_stats']:
                                            self.keyword_context['collection_stats'][current_keyword]['total_collected'] += 1
                                            self.keyword_context['collection_stats'][current_keyword]['last_update'] = time.time()

                                    goods_list.append(goods_data)
                        else:
                            logger.debug(f"跳过非字典类型的数据: {type(item)}")
                    if goods_list:
                        break
            except Exception as e:
                logger.debug(f"尝试路径 {path} 时出错: {e}")
                continue
        
        return goods_list
    
    # 基于真实API响应数据分析结果（2025-08-01）：
    # ✅ 商品名称关键词检测：100%准确（17/17个国补商品都在名称中包含国补关键词）
    # ❌ 营销标签精确匹配：0%准确（tag_list和prop_tag_list中完全没有国补关键词）
    # ❌ activity_type=34检测：35.3%准确（只有6/17个国补商品activity_type=34）
    # 
    # 修复措施：
    # 1. 移除不准确的营销标签匹配逻辑
    # 2. 移除activity_type=34的国补专用标识
    # 3. 优化商品名称关键词检测，添加"国补20%"、"国补15%"等精确匹配

    async def _parse_single_goods(self, item: Dict) -> Optional[Dict]:
        """解析单个商品数据 - 支持全部29个字段"""
        try:
            # 必需字段
            goods_id = str(item.get('goods_id', item.get('goodsId', item.get('id', ''))))
            goods_name = item.get('goods_name', item.get('goodsName', item.get('short_name', item.get('name', ''))))
            
            # 调试信息
            if not goods_id:
                logger.debug(f"缺少goods_id - 可用字段: {list(item.keys())[:10]}")
            if not goods_name:
                logger.debug(f"缺少goods_name - 可用字段: {list(item.keys())[:10]}")
            
            if not goods_id or not goods_name:
                return None
            
            # 价格处理 - 分转元（修复：所有价格都需要转换）
            price = self._safe_get(item, 'price', 0)
            if price == 0:
                price = self._safe_get(item, 'group_price', 0)
            if isinstance(price, (int, float)) and price > 0:
                price = self._process_price_from_cents(price)

            # 其他价格字段（修复：所有价格都需要转换）
            coupon_price = self._safe_get(item, 'couponPromoPrice', 0)
            if isinstance(coupon_price, (int, float)) and coupon_price > 0:
                coupon_price = self._process_price_from_cents(coupon_price)

            normal_price = self._safe_get(item, 'normal_price', 0)
            if isinstance(normal_price, (int, float)) and normal_price > 0:
                normal_price = self._process_price_from_cents(normal_price)

            market_price = self._safe_get(item, 'market_price', 0)
            if isinstance(market_price, (int, float)) and market_price > 0:
                market_price = self._process_price_from_cents(market_price)
            
            # 销量处理
            sales = self._safe_get(item, 'sales', 0)
            if sales == 0:
                sales = self._safe_get(item, 'cnt', 0)
            
            # 提取标签列表
            tag_list = []
            if 'tag_list' in item and isinstance(item['tag_list'], list):
                for tag in item['tag_list']:
                    if isinstance(tag, dict) and 'text' in tag:
                        tag_list.append(tag['text'])
            
            # 提取属性标签
            prop_tags = []
            if 'prop_tag_list' in item and isinstance(item['prop_tag_list'], list):
                for tag in item['prop_tag_list']:
                    if isinstance(tag, dict) and 'text' in tag:
                        prop_tags.append(tag['text'])
            
            # 商家类型处理
            merchant_type = self._safe_get(item, 'merchant_type', 0)
            merchant_type_name = self.MERCHANT_TYPE_MAPPING.get(merchant_type, f"未知类型({merchant_type})")

            # 调试日志：记录商家类型信息
            if self.config.get('product_filter', {}).get('debug', {}).get('enabled', False):
                logger.debug(f"商家类型调试: {goods_name[:30]}... merchant_type={merchant_type} -> {merchant_type_name}")
            
            # 品牌名称提取和映射
            brand_id = self._safe_get(item, 'brandId', self._safe_get(item, 'brand_id', 0))
            brand_name = self._extract_brand_name(goods_name, brand_id)

            # 活动类型和事件类型转换
            activity_type = self._safe_get(item, 'activity_type', 0)
            activity_type_name = self._get_activity_type_name(activity_type)

            event_type = self._safe_get(item, 'event_type', 0)
            event_type_name = self._get_event_type_name(event_type)

            price_type = self._safe_get(item, 'price_type', 0)
            price_type_name = self._get_price_type_name(price_type)

            # 构建完整商品链接
            goods_url = self._build_complete_goods_url(goods_id, item)

            # 提取更多图片信息
            hd_url = self._safe_get(item, 'hd_url', '')

            # 提取图标信息
            icon_ids = self._extract_icon_ids(item)  # 字符串格式，用于存储
            icon_ids_list = self._get_icon_ids_list(item)  # 列表格式，用于逻辑判断
            icon_list = self._extract_icon_list(item)

            # 🎯 基于真实API响应数据的补贴信息提取逻辑
            is_subsidy = False
            is_government_subsidy = False
            subsidy_info = ""
            
            # 🎯 使用增强的百亿补贴检测方法
            is_subsidy = self.detect_subsidy_status(item)
            if is_subsidy:
                subsidy_info = "百亿补贴"
                logger.info(f"✅ 检测到百亿补贴商品: {goods_name[:30]}...")
            
            # 方法2：通过商品名称检测政府补贴
            # 注意：activity_type=34 仅有7%相关性，不可靠，改用名称匹配
            if not is_government_subsidy and goods_name:
                goods_name_str = str(goods_name) if goods_name else ""

                # 政府补贴检测关键词（基于真实API数据优化）
                gov_subsidy_keywords = ['国补', '政府补贴', '政府消费补贴', '国补20%', '国补15%']

                # 检查商品名称中是否包含政府补贴关键词
                for keyword in gov_subsidy_keywords:
                    # 确保keyword是字符串类型
                    keyword_str = str(keyword) if keyword else ""
                    if keyword_str and keyword_str in goods_name_str:
                        is_government_subsidy = True
                        if is_subsidy:
                            subsidy_info = "百亿补贴 + 国补"
                        else:
                            subsidy_info = f"国补({keyword_str})"
                        logger.info(f"✅ 通过商品名称检测到政府补贴 (关键词: {keyword_str}): {goods_name[:30]}...")
                        break
            
            # 方法3：通过icon_list检测补贴信息（备用方法）
            if not is_subsidy and icon_list:
                # 确保icon_list是字符串类型
                icon_list_str = str(icon_list) if icon_list else ""
                # 现在icon_list包含了解析后的中文图标名称
                if "百亿补贴" in icon_list_str:
                    is_subsidy = True
                    # 如果已经有国补信息，则合并
                    if is_government_subsidy:
                        subsidy_info = "百亿补贴 + 国补"
                    else:
                        subsidy_info = "百亿补贴"
                    logger.info(f"✅ 通过icon_list检测到百亿补贴: {goods_name[:30]}...")

                # 检测品牌黑标
                if "品牌黑标" in icon_list_str:
                    logger.debug(f"🏷️ 检测到品牌黑标商品: {goods_name[:30]}...")

                # 检测秒杀
                if "秒杀" in icon_list_str:
                    logger.debug(f"⚡ 检测到秒杀商品: {goods_name[:30]}...")

            # 方法3.5：通过图标URL检测百亿补贴（新增方法）
            if not is_subsidy:
                icon_list_raw = self._safe_get(item, 'icon_list', [])
                if isinstance(icon_list_raw, list):
                    for icon in icon_list_raw:
                        if isinstance(icon, dict):
                            icon_url = icon.get('url', '')
                            # 检查百亿补贴的特征URL
                            if icon_url and ('social/pincard/1/share.png' in icon_url or
                                           'pincard/1/share.png' in icon_url or
                                           icon_url.endswith('social/pincard/1/share.png')):
                                is_subsidy = True
                                # 如果已经有国补信息，则合并
                                if is_government_subsidy:
                                    subsidy_info = "百亿补贴 + 国补"
                                else:
                                    subsidy_info = "百亿补贴"
                                logger.info(f"✅ 通过图标URL检测到百亿补贴: {goods_name[:30]}... (URL: {icon_url})")
                                break

            # 方法4：通过标签检测补贴信息（备用方法）
            if not is_subsidy and not is_government_subsidy:
                all_tags = tag_list + prop_tags
                for tag in all_tags:
                    if not tag:
                        continue
                    tag_str = str(tag).strip()
                    
                    # 百亿补贴检测（精确匹配）
                    if tag_str == "百亿补贴" or tag_str == "拼多多百亿补贴":
                        is_subsidy = True
                        # 如果已经有国补信息，则合并
                        if is_government_subsidy:
                            subsidy_info = "百亿补贴 + 国补"
                        else:
                            subsidy_info = "百亿补贴"
                        logger.info(f"✅ 通过标签检测到百亿补贴: '{tag_str}'")
                        break
                    
                    # 注意：基于真实API数据分析，营销标签中不包含国补关键词
                    # 已移除不准确的国补标签检测逻辑
            
            # 记录iconIds信息（用于调试和分析）
            if icon_ids_list:
                icon_info = []
                if 20001 in icon_ids_list:
                    icon_info.append("百亿补贴(20001)")
                if 10014 in icon_ids_list:
                    icon_info.append("品牌标识(10014)")
                if 0 in icon_ids_list:
                    icon_info.append("秒杀(0)")
                if 10 in icon_ids_list:
                    icon_info.append("旗舰店(10)")
                if 20013 in icon_ids_list:
                    icon_info.append("夏清仓(20013)")
                if icon_info:
                    logger.debug(f"商品图标: {', '.join(icon_info)}")
            
            # 最终统计
            if is_subsidy or is_government_subsidy:
                logger.info(f"📊 补贴商品检测结果 - {goods_name[:30]}... : {subsidy_info}")
            else:
                logger.info(f"📦 普通商品 - {goods_name[:30]}... (价格: {price}元)")
            
            # 构建完整的商品数据
            goods_data = {
                # 基础信息
                'goods_id': goods_id,
                'goods_name': goods_name,

                # 价格信息
                'price': price,  # 拼团价
                'coupon_price': coupon_price,  # 券后价
                'normal_price': normal_price,  # 正常价
                'market_price': market_price,  # 市场价
                'original_price': self._safe_get(item, 'original_price', market_price),  # 原价
                'price_prefix': self._safe_get(item, 'price_prefix_info', ''),  # 价格前缀说明
                'price_type': price_type,  # 价格类型
                'price_type_name': price_type_name,  # 价格类型名称
                'price_info': self._safe_get(item, 'price_info', ''),  # 价格信息文本

                # 销量信息
                'sales': sales,  # 销量
                'sales_tip': self._safe_get(item, 'sales_tip', ''),  # 销量描述
                'downgrade_sale_tips': self._safe_get(item, 'downgrade_sale_tips', ''),  # 详细销量描述

                # 店铺信息
                'brand_id': brand_id,
                'brand_name': brand_name,  # 品牌名称
                'mall_id': self._safe_get(item, 'mall_id', 0),
                'shop_id': self._safe_get(item, 'shop_id', self._safe_get(item, 'mall_id', 0)),
                'shop_name': self._safe_get(item, 'mall_name', self._safe_get(item, 'shop_name', '')),
                'merchant_type': merchant_type,
                'merchant_type_name': merchant_type_name,

                # 图片信息
                'image_url': item.get('hd_thumb_url', item.get('thumb_url', '')),
                'thumb_url': item.get('thumb_url', ''),
                'hd_thumb_url': item.get('hd_thumb_url', ''),
                'hd_url': hd_url,  # 高清图片URL
                'long_thumb_url': item.get('long_thumb_url', ''),

                # 活动和事件信息
                'activity_type': activity_type,
                'activity_type_name': activity_type_name,  # 活动类型名称
                'event_type': event_type,
                'event_type_name': event_type_name,  # 事件类型名称

                # 补贴信息 🎯 关键修复：添加补贴字段
                'is_subsidy': is_subsidy,  # 百亿补贴
                'is_government_subsidy': is_government_subsidy,  # 国补商品
                'subsidy_info': subsidy_info,  # 补贴详情

                # 标签信息
                'tags': ', '.join(tag_list),  # 商品标签
                'tag_list': ', '.join(tag_list),  # 标签列表
                'prop_tag_list': ', '.join(prop_tags),  # 属性标签
                'icon_ids': icon_ids,  # 图标ID列表
                'icon_list': icon_list,  # 图标列表
                'special_text': self._safe_get(item, 'special_text', ''),  # 特殊信息

                # 链接信息
                'goods_url': goods_url,  # 完整商品链接
                'link_url': self._safe_get(item, 'link_url', ''),  # 原始链接

                # 其他信息
                'ad_id': self._safe_get(item, 'adid', self._safe_get(item, 'ad_id', '')),  # 广告ID
                'category': self._safe_get(item, 'cat_name', self._safe_get(item, 'category_name', self._safe_get(item, 'category', ''))),  # 商品分类
                'rating': self._safe_get(item, 'item_score', self._safe_get(item, 'goods_score', self._safe_get(item, 'rating', 0))),  # 评分
                'comment_count': self._safe_get(item, 'comment_cnt', self._safe_get(item, 'review_cnt', self._safe_get(item, 'comment_count', 0))),  # 评论数
                'keyword': self.current_keyword,  # 搜索关键词
                'search_keyword': self.current_keyword,  # 搜索关键词（兼容）
                'created_time': time.strftime('%Y-%m-%d %H:%M:%S')  # 创建时间
            }
            
            return goods_data
            
        except Exception as e:
            logger.debug(f"解析商品数据失败: {e}")
            return None
    
    def _safe_get(self, data: Dict, key: str, default: Any = None) -> Any:
        """安全获取字典值"""
        try:
            return data.get(key, default)
        except:
            return default
    
    def _process_price_from_cents(self, price_in_cents: int) -> float:
        """处理分为单位的价格"""
        try:
            return round(price_in_cents / 100, 2)
        except:
            return 0.0

    def _extract_brand_name(self, goods_name: str, brand_id: int) -> str:
        """
        提取品牌名称 - 完整优化版本

        流程：
        1. 现有品牌库匹配 → 统一化处理
        2. 轻量化正则增强匹配 → 统一化处理
        3. 返回标准中文品牌名称
        """
        if not goods_name:
            return ""

        # 🎯 步骤1：现有品牌库匹配 (保持原有逻辑 + 统一化)
        brand_result = self._match_existing_brands(goods_name)
        if brand_result:
            return self._normalize_brand_name(brand_result)

        # 🎯 步骤2：轻量化正则增强匹配
        smart_result = self._extract_with_smart_patterns(goods_name)
        if smart_result:
            return self._normalize_brand_name(smart_result)

        return ""

    def _match_existing_brands(self, goods_name: str) -> str:
        """
        现有品牌库匹配逻辑 (保持原有逻辑)
        """
        goods_name_clean = goods_name.strip()
        goods_name_upper = goods_name_clean.upper()

        # 按品牌名长度排序，优先匹配长品牌名
        sorted_brands = sorted(self.brand_mapping.items(), key=lambda x: len(x[0]), reverse=True)

        for brand_key, brand_name in sorted_brands:
            brand_key_upper = brand_key.upper()

            # 精确匹配
            if (goods_name_clean.startswith(brand_key) or
                goods_name_clean.startswith(brand_key_upper) or
                f" {brand_key} " in goods_name_clean or
                f"/{brand_key}/" in goods_name_clean or
                f"【{brand_key}】" in goods_name_clean or
                f"（{brand_key}）" in goods_name_clean or
                f"[{brand_key}]" in goods_name_clean):
                return brand_name

            # 大小写不敏感匹配
            if (brand_key_upper in goods_name_upper and len(brand_key) >= 2):
                if brand_key.lower() in ["美的", "海尔", "格力", "小米", "tcl"]:
                    # 对于常见品牌，进行更严格的匹配
                    if (goods_name_clean.find(brand_key) == 0 or
                        f" {brand_key}" in goods_name_clean or
                        f"{brand_key} " in goods_name_clean):
                        return brand_name
                elif brand_key.lower() in ["七星", "sevenstars", "twinwash", "candara", "菱木", "美凌", "haer", "tronssra", "hairi"]:
                    # 对于小众品牌，使用更宽松的匹配
                    return brand_name
                else:
                    return brand_name

        return ""

    def _extract_with_smart_patterns(self, goods_name: str) -> str:
        """
        🎯 轻量化正则增强核心功能
        """
        for compiled_pattern, pattern_type in self._compiled_patterns:
            match = compiled_pattern.search(goods_name)
            if match:
                candidate = self._extract_candidate_from_match(match, pattern_type)
                if candidate and self._is_valid_brand_candidate(candidate):
                    return candidate

        return ""

    def _extract_candidate_from_match(self, match, pattern_type: str) -> str:
        """
        从正则匹配结果中提取品牌候选词
        """
        if pattern_type == 'en_cn_mixed':
            # "Brand/品牌" → 优先返回中文
            en_part = match.group(1).strip()
            cn_part = match.group(2).strip()
            return cn_part if cn_part else en_part

        elif pattern_type == 'cn_en_mixed':
            # "品牌/Brand" → 优先返回中文
            cn_part = match.group(1).strip()
            en_part = match.group(2).strip()
            return cn_part if cn_part else en_part

        else:
            # 其他模式返回第一个匹配组
            return match.group(1).strip()

    def _normalize_brand_name(self, brand_name: str) -> str:
        """
        🎯 品牌名称统一化处理 (中文优先)
        """
        if not brand_name:
            return ""

        # 1. 直接映射查找
        normalized = self.brand_mapping.get(brand_name)
        if normalized:
            return normalized

        # 2. 小写映射查找
        normalized = self.brand_mapping.get(brand_name.lower())
        if normalized:
            return normalized

        # 3. 大写映射查找
        normalized = self.brand_mapping.get(brand_name.upper())
        if normalized:
            return normalized

        # 4. 首字母大写映射查找
        normalized = self.brand_mapping.get(brand_name.capitalize())
        if normalized:
            return normalized

        # 5. 如果没有找到映射，返回原始名称
        return brand_name

    def _is_valid_brand_candidate(self, candidate: str) -> bool:
        """
        验证品牌候选词的有效性
        """
        if not candidate or len(candidate) < 2:
            return False

        # 排除明显的非品牌词
        invalid_words = {
            '冰箱', '洗衣机', '空调', '电视', '微波炉', '家用', '商用', '节能',
            '静音', '大容量', '小型', '迷你', '双门', '三门', '对开门', '十字门',
            '全自动', '半自动', '变频', '定频', '风冷', '直冷', '无霜', '有霜',
            '新款', '热销', '特价', '包邮', '正品', '品牌', '厂家', '直销',
            '一级', '二级', '三级', '能效', '省电', '智能', '遥控', '触控',
            '不锈钢', '塑料', '金属', '玻璃', '镜面', '拉丝', '烤漆',
            '限时', '特价', '促销', '活动', '优惠', '折扣'
        }

        if candidate in invalid_words:
            return False

        # 长度验证
        if len(candidate) > 12:  # 品牌名通常不会太长
            return False

        # 字符验证
        if candidate.isdigit():  # 纯数字不是品牌
            return False

        # 特殊字符验证
        if any(char in candidate for char in ['升', 'L', 'kg', '公斤', '寸', '英寸', '年', '月', '日']):
            return False

        return True

    def _get_activity_type_name(self, activity_type: int) -> str:
        """获取活动类型名称（基于真实数据）"""
        activity_types = {
            0: "无活动",          # ✅ 基于真实数据确认
            1: "拼团活动",        # ✅ 基于真实数据确认
            34: "活动类型34",     # ❌ 经真实数据验证，非国补专用标识
            101: "新人专享",      # ✅ 基于真实数据确认
        }
        return activity_types.get(activity_type, f"活动类型{activity_type}")

    def _get_event_type_name(self, event_type: int) -> str:
        """获取事件类型名称"""
        event_types = {
            0: "无事件",
            1: "促销活动",
            2: "新品上市",
            3: "清仓处理",
            4: "节日特惠",
            5: "品牌日活动"
        }
        return event_types.get(event_type, f"事件类型{event_type}")

    def _get_price_type_name(self, price_type: int) -> str:
        """获取价格类型名称（基于真实API数据）"""
        price_types = {
            0: "普通价格",        # ✅ 基于真实数据确认
            2: "券后价格",        # ✅ 基于真实数据确认（price_prefix_info: "券后"）
        }
        return price_types.get(price_type, f"价格类型{price_type}")

    def _build_complete_goods_url(self, goods_id: str, item: Dict) -> str:
        """构建完整的商品链接"""
        # 获取原始链接
        link_url = self._safe_get(item, 'link_url', '')

        if link_url:
            # 如果是相对路径，补充完整域名
            if link_url.startswith('goods.html'):
                return f"https://mobile.yangkeduo.com/{link_url}"
            elif link_url.startswith('/'):
                return f"https://mobile.yangkeduo.com{link_url}"
            elif link_url.startswith('http'):
                return link_url

        # 如果没有链接，根据goods_id构建标准链接
        if goods_id:
            return f"https://mobile.yangkeduo.com/goods.html?goods_id={goods_id}"

        return ""

    def _extract_icon_ids(self, item: Dict) -> str:
        """提取图标ID列表（字符串格式，用于存储）"""
        icon_ids = self._safe_get(item, 'iconIds', [])
        if isinstance(icon_ids, list):
            return ','.join(str(id) for id in icon_ids)
        return str(icon_ids) if icon_ids else ""

    def _get_icon_ids_list(self, item: Dict) -> List[int]:
        """获取图标ID列表（列表格式，用于逻辑判断）"""
        icon_ids = self._safe_get(item, 'iconIds', [])
        if isinstance(icon_ids, list):
            # 确保所有ID都是整数
            result = []
            for id_val in icon_ids:
                try:
                    if isinstance(id_val, int):
                        result.append(id_val)
                    elif isinstance(id_val, str) and id_val.isdigit():
                        result.append(int(id_val))
                    elif isinstance(id_val, (float,)) and id_val.is_integer():
                        result.append(int(id_val))
                except (ValueError, AttributeError):
                    continue
            return result
        return []

    def _extract_icon_list(self, item: Dict) -> str:
        """提取图标列表 - 使用icon ID映射"""
        try:
            icon_list = self._safe_get(item, 'icon_list', [])
            if isinstance(icon_list, list):
                icons = []
                for icon in icon_list:
                    if isinstance(icon, dict):
                        # 优先使用icon ID映射
                        icon_id = icon.get('id')
                        if icon_id is not None and icon_id in self.icon_id_mapping:
                            icon_text = self.icon_id_mapping[icon_id]
                            icons.append(str(icon_text))  # 确保是字符串
                            # 如果是百亿补贴，记录日志
                            if icon_id == 20001:
                                logger.debug(f"🎯 通过icon ID {icon_id} 识别到百亿补贴")
                        else:
                            # 如果没有映射，尝试从text或name字段获取
                            icon_text = icon.get('text', icon.get('name', ''))
                            if icon_text:
                                icons.append(str(icon_text))  # 确保是字符串
                            # 记录未知的icon ID
                            if icon_id is not None and icon_id not in self.icon_id_mapping:
                                logger.debug(f"发现未知icon ID: {icon_id}, URL: {icon.get('url', 'N/A')}")
                    elif isinstance(icon, (str, int, float)):
                        icons.append(str(icon))  # 转换为字符串
                return ','.join(icons)
            return str(icon_list) if icon_list else ""
        except Exception as e:
            logger.debug(f"提取icon_list时出错: {e}")
            return ""
    
    def _update_avg_processing_time(self, processing_time: float) -> None:
        """更新平均处理时间"""
        total = self.metrics["total_processing_time"] + processing_time
        count = self.metrics["processed_responses"]
        self.metrics["total_processing_time"] = total
        self.metrics["avg_processing_time"] = total / max(count, 1)
    
    def _pause_processing(self) -> None:
        """暂停响应处理"""
        self._processing_paused = True
        logger.debug("响应处理已暂停")
            
    def _resume_processing(self) -> None:
        """恢复响应处理"""
        self._processing_paused = False
        logger.debug("响应处理已恢复")
    
    # 兼容性方法（保持与原有接口一致）
    def set_data_callback(self, callback: Callable) -> None:
        """设置数据回调函数"""
        self.data_callback = callback
    
    def get_data_count(self) -> int:
        """获取已监听的数据数量"""
        return len(self.monitored_data)
    
    def clear_data(self) -> None:
        """清空已监听的数据"""
        self.monitored_data.clear()
    
    def get_monitoring_stats(self) -> Dict[str, Any]:
        """获取详细监控统计"""
        runtime = time.time() - self.start_time if hasattr(self, 'start_time') else 0
        
        return {
            "runtime": runtime,
            "metrics": self.metrics,
            "circuit_breaker": {
                "open": self.circuit_breaker_open,
                "error_count": self.error_counter,
                "last_error_time": self.last_error_time
            },
            "active_pages": len(self._active_pages),
            "queue_size": self._response_queue.qsize() if hasattr(self, '_response_queue') else 0,
            "processing_tasks": len(self._processing_tasks) if hasattr(self, '_processing_tasks') else 0
        }
    
    async def setup_monitoring_with_integration(
        self, 
        page: Page, 
        keyword: str,
        anti_detection_manager=None,
        scroll_manager=None
    ) -> None:
        """与其他系统组件集成的监听设置"""
        self.current_keyword = keyword
        
        # 设置基础监听
        await self.setup_monitoring(page, keyword)
        
        # 与反检测系统协调
        if anti_detection_manager:
            # 在高频响应时增加延迟
            self._anti_detection = anti_detection_manager
            
        # 与滚动管理器协调
        if scroll_manager:
            # 滚动时暂停处理，避免过载
            scroll_manager.on_scroll_start = lambda: self._pause_processing()
            scroll_manager.on_scroll_end = lambda: self._resume_processing()
    
    def reset_keyword_context(self, new_keyword: str):
        """重置关键词上下文"""
        # 保存原有的数据结构，确保所有必要的字段都存在
        self.keyword_context = {
            'current': new_keyword,
            'previous': self.current_keyword if hasattr(self, 'current_keyword') else None,
            'switch_time': time.time(),
            'data_mapping': {},                 # 必须包含此字段
            'collection_stats': {},             # 必须包含此字段
            'processing_status': {},            # 必须包含此字段
            'sequence_index': self.keyword_context.get('sequence_index', 0) + 1 if hasattr(self, 'keyword_context') else 1
        }
        self.current_keyword = new_keyword
        logger.info(f"关键词上下文已重置: {self.keyword_context}")
    
    def clear_keyword_data(self, keyword: str = None):
        """清理指定关键词的数据"""
        if keyword:
            # 清理特定关键词的数据
            self.monitored_data = [d for d in self.monitored_data if d.get('keyword') != keyword]
        else:
            # 清理所有数据
            self.monitored_data.clear()
        self._collected_goods_ids.clear()
        logger.info(f"已清理关键词数据: {keyword or '所有'}")