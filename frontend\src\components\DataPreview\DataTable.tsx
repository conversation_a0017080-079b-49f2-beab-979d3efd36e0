/**
 * 数据表格组件 - 支持虚拟滚动、列配置、排序筛选
 */

import React, { useState, useMemo, useCallback } from 'react';
import { Table, Tag, Image, Button, Space, Tooltip, Switch, Popover, Input, Slider, Select } from 'antd';
import { LinkOutlined, SettingOutlined, SearchOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import type { Product, ColumnConfig, DataFilter } from '../../types';
import type { DataTableProps } from './types';
import { formatPrice, getDefaultColumns } from './utils';

const { Option } = Select;

const DataTable: React.FC<DataTableProps> = ({
  data = [],
  loading = false,
  pagination,
  onRowClick,
  filters,
  onFiltersChange,
  columns: customColumns,
  onColumnsChange
}) => {
  const [columnConfigs, setColumnConfigs] = useState<ColumnConfig[]>(() => 
    customColumns || getDefaultColumns()
  );

  // 过滤后的数据
  const filteredData = useMemo(() => {
    if (!filters) return data;

    return data.filter(product => {
      // 关键词过滤
      if (filters.keyword && !product.goods_name?.toLowerCase().includes(filters.keyword.toLowerCase())) {
        return false;
      }

      // 价格范围过滤
      if (filters.priceRange) {
        const price = Number(product.price) || 0;
        if (price < filters.priceRange[0] || price > filters.priceRange[1]) {
          return false;
        }
      }

      // 品牌过滤
      if (filters.brands && filters.brands.length > 0) {
        if (!product.brand_name || !filters.brands.includes(product.brand_name)) {
          return false;
        }
      }

      // 补贴过滤
      if (filters.hasSubsidy !== undefined) {
        const hasSubsidy = !!(product.subsidy_info?.trim());
        if (hasSubsidy !== filters.hasSubsidy) {
          return false;
        }
      }

      // 最小销量过滤
      if (filters.minSales !== undefined) {
        let sales = 0;
        if (typeof product.sales === 'number') {
          sales = product.sales;
        } else if (typeof product.sales === 'string') {
          const salesStr = product.sales.toString();
          if (salesStr.includes('万')) {
            const match = salesStr.match(/(\d+(?:\.\d+)?)\s*万/);
            if (match) {
              sales = parseFloat(match[1]) * 10000;
            }
          } else {
            sales = parseInt(salesStr.replace(/[^\d]/g, '')) || 0;
          }
        }
        if (sales < filters.minSales) {
          return false;
        }
      }

      // 最小评分过滤
      if (filters.minRating !== undefined) {
        const rating = Number(product.rating) || 0;
        if (rating < filters.minRating) {
          return false;
        }
      }

      return true;
    });
  }, [data, filters]);

  // 渲染单元格内容
  const renderCell = useCallback((value: any, _record: Product, renderType: string) => {
    switch (renderType) {
      case 'price':
        return formatPrice(value);
      
      case 'image':
        return value ? (
          <Image
            src={value}
            alt="商品图片"
            width={60}
            height={60}
            style={{ objectFit: 'cover' }}
            fallback="data:image/png;base64,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"
          />
        ) : (
          <div style={{ width: 60, height: 60, backgroundColor: '#f0f0f0', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            无图
          </div>
        );
      
      case 'link':
        return value ? (
          <Tooltip title="查看商品">
            <Button
              type="link"
              icon={<LinkOutlined />}
              onClick={(e) => {
                e.stopPropagation();
                window.open(value, '_blank');
              }}
            >
              查看
            </Button>
          </Tooltip>
        ) : null;
      
      case 'tag':
        if (!value) return null;
        const tags = value.split(',').filter((tag: string) => tag.trim());
        return (
          <Space wrap>
            {tags.slice(0, 3).map((tag: string, index: number) => (
              <Tag key={index} color={tag.includes('百亿补贴') ? 'red' : 'blue'}>
                {tag.trim()}
              </Tag>
            ))}
            {tags.length > 3 && (
              <Tooltip title={tags.slice(3).join(', ')}>
                <Tag>+{tags.length - 3}</Tag>
              </Tooltip>
            )}
          </Space>
        );
      
      case 'subsidy':
        if (!value?.trim()) return <Tag color="default">无补贴</Tag>;
        
        if (value.includes('百亿补贴')) {
          return <Tag color="red">百亿补贴</Tag>;
        } else if (value.includes('国补') || value.includes('政府补贴')) {
          return <Tag color="blue">政府补贴</Tag>;
        } else {
          return <Tag color="green">{value}</Tag>;
        }
      
      default:
        if (value === null || value === undefined) return '-';
        return String(value);
    }
  }, []);

  // 生成表格列配置
  const tableColumns: ColumnsType<Product> = useMemo(() => {
    return columnConfigs
      .filter(col => col.visible)
      .map((col) => ({
        key: col.key,
        title: col.title,
        dataIndex: col.dataIndex,
        width: col.width,
        fixed: col.fixed,
        sorter: col.sorter ? (a, b) => {
          const aVal = a[col.dataIndex as keyof Product];
          const bVal = b[col.dataIndex as keyof Product];
          
          // 数字排序
          if (col.render === 'price' || col.key === 'sales' || col.key === 'rating') {
            const aNum = Number(aVal) || 0;
            const bNum = Number(bVal) || 0;
            return aNum - bNum;
          }
          
          // 字符串排序
          return String(aVal || '').localeCompare(String(bVal || ''));
        } : false,
        render: (value, record) => renderCell(value, record, col.render || 'text')
      }));
  }, [columnConfigs, renderCell]);

  // 列配置面板
  const ColumnSettings = () => (
    <div style={{ maxHeight: 400, overflowY: 'auto', padding: 16 }}>
      <div style={{ marginBottom: 16 }}>
        <strong>列显示设置</strong>
      </div>
      {columnConfigs.map(col => (
        <div key={col.key} style={{ marginBottom: 8, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <span>{col.title}</span>
          <Switch
            size="small"
            checked={col.visible}
            onChange={(checked) => {
              const newConfigs = columnConfigs.map(c => 
                c.key === col.key ? { ...c, visible: checked } : c
              );
              setColumnConfigs(newConfigs);
              onColumnsChange?.(newConfigs);
            }}
          />
        </div>
      ))}
    </div>
  );

  // 过滤器面板
  const FilterPanel = () => {
    const brands = useMemo(() => {
      const brandSet = new Set<string>();
      data.forEach(product => {
        if (product.brand_name?.trim()) {
          brandSet.add(product.brand_name);
        }
      });
      return Array.from(brandSet).sort();
    }, [data]);

    const priceRange = useMemo(() => {
      const prices = data.map(p => Number(p.price) || 0).filter(p => p > 0);
      if (prices.length === 0) return [0, 1000];
      return [Math.min(...prices), Math.max(...prices)];
    }, [data]);

    return (
      <div style={{ padding: 16, minWidth: 300 }}>
        <div style={{ marginBottom: 16 }}>
          <strong>数据过滤</strong>
        </div>
        
        {/* 商品名称搜索 */}
        <div style={{ marginBottom: 16 }}>
          <label>商品名称:</label>
          <Input
            placeholder="搜索商品名称"
            value={filters?.keyword || ''}
            onChange={(e) => onFiltersChange?.({ ...filters, keyword: e.target.value })}
            allowClear
          />
        </div>

        {/* 价格范围 */}
        <div style={{ marginBottom: 16 }}>
          <label>价格范围: ¥{filters?.priceRange?.[0] || priceRange[0]} - ¥{filters?.priceRange?.[1] || priceRange[1]}</label>
          <Slider
            range
            min={priceRange[0]}
            max={priceRange[1]}
            value={filters?.priceRange || priceRange}
            onChange={(value) => onFiltersChange?.({ ...filters, priceRange: value as [number, number] })}
            step={10}
          />
        </div>

        {/* 品牌选择 */}
        <div style={{ marginBottom: 16 }}>
          <label>品牌:</label>
          <Select
            mode="multiple"
            placeholder="选择品牌"
            value={filters?.brands || []}
            onChange={(value) => onFiltersChange?.({ ...filters, brands: value })}
            style={{ width: '100%' }}
            maxTagCount={3}
          >
            {brands.map(brand => (
              <Option key={brand} value={brand}>{brand}</Option>
            ))}
          </Select>
        </div>

        {/* 补贴筛选 */}
        <div style={{ marginBottom: 16 }}>
          <label>补贴商品:</label>
          <Select
            placeholder="选择补贴类型"
            value={filters?.hasSubsidy}
            onChange={(value) => onFiltersChange?.({ ...filters, hasSubsidy: value })}
            style={{ width: '100%' }}
            allowClear
          >
            <Option value={true}>有补贴</Option>
            <Option value={false}>无补贴</Option>
          </Select>
        </div>

        {/* 最小销量 */}
        <div style={{ marginBottom: 16 }}>
          <label>最小销量:</label>
          <Input
            type="number"
            placeholder="输入最小销量"
            value={filters?.minSales || ''}
            onChange={(e) => onFiltersChange?.({ ...filters, minSales: Number(e.target.value) || undefined })}
          />
        </div>

        {/* 最小评分 */}
        <div style={{ marginBottom: 16 }}>
          <label>最小评分:</label>
          <Input
            type="number"
            min={0}
            max={5}
            step={0.1}
            placeholder="输入最小评分"
            value={filters?.minRating || ''}
            onChange={(e) => onFiltersChange?.({ ...filters, minRating: Number(e.target.value) || undefined })}
          />
        </div>
      </div>
    );
  };

  return (
    <div>
      {/* 工具栏 */}
      <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Space>
          <span>共 {filteredData.length} 条数据</span>
          {filteredData.length !== data.length && (
            <span style={{ color: '#666' }}>（已过滤 {data.length - filteredData.length} 条）</span>
          )}
        </Space>
        
        <Space>
          {/* 过滤器 */}
          <Popover
            content={<FilterPanel />}
            title="数据过滤"
            trigger="click"
            placement="bottomRight"
          >
            <Button icon={<SearchOutlined />}>
              过滤 {filters && Object.keys(filters).some(key => filters[key as keyof DataFilter] !== undefined) && '●'}
            </Button>
          </Popover>

          {/* 列设置 */}
          <Popover
            content={<ColumnSettings />}
            title="列设置"
            trigger="click"
            placement="bottomRight"
          >
            <Button icon={<SettingOutlined />}>列设置</Button>
          </Popover>
        </Space>
      </div>

      {/* 表格 */}
      <Table
        columns={tableColumns}
        dataSource={filteredData}
        rowKey="goods_id"
        loading={loading}
        pagination={pagination ? {
          ...pagination,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          pageSizeOptions: ['10', '20', '50', '100']
        } : false}
        scroll={{ x: 1500, y: 600 }}
        size="small"
        onRow={(record) => ({
          onClick: () => onRowClick?.(record),
          style: { cursor: onRowClick ? 'pointer' : 'default' }
        })}
        rowClassName={(_record, index) => index % 2 === 0 ? 'table-row-light' : 'table-row-dark'}
      />

      <style>{`
        .table-row-light {
          background-color: #fafafa;
        }
        .table-row-dark {
          background-color: #ffffff;
        }
        .table-row-light:hover,
        .table-row-dark:hover {
          background-color: #e6f7ff !important;
        }
      `}</style>
    </div>
  );
};

export default DataTable;