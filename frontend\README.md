# 拼多多爬虫前端系统 v2.2.0

基于 React + TypeScript + Vite + Ant Design 构建的现代化爬虫管理界面，提供完整的数据采集、监控和管理功能。

## 🚀 核心功能

### 搜索配置管理
- **智能关键词输入**: 支持多关键词批量设置，自动去重和验证
- **高级筛选器**: 价格区间、品牌筛选、销量范围等多维度筛选
- **参数设置**: 排序方式、页数限制、采集间隔等详细配置
- **配置模板**: 保存和复用常用搜索配置

### Cookie管理系统
- **多格式导入**: 支持JSON格式和字符串格式Cookie导入
- **实时验证**: 自动验证Cookie有效性，显示状态指示器
- **安全存储**: 本地加密存储，支持云端同步
- **批量操作**: 批量导入、导出、清理过期Cookie

### 爬虫任务控制
- **一键启停**: 智能启动和优雅停止机制
- **任务队列**: 支持多任务并发，任务优先级管理
- **错误处理**: 自动重试机制，详细错误日志
- **任务历史**: 完整的任务执行记录和统计

### 实时进度监控
- **WebSocket实时通信**: 毫秒级状态更新，无延迟监控
- **可视化进度**: 进度条、统计图表、关键指标展示
- **性能监控**: CPU、内存使用率，网络状态监控
- **报警机制**: 异常情况自动报警和通知

### 数据预览与管理
- **实时数据展示**: 动态数据表格，支持搜索、排序、筛选
- **产品详情**: 完整产品信息展示，包括价格、补贴、评价等
- **数据统计**: 多维度数据分析，品牌分布、价格区间等
- **数据导出**: Excel、CSV格式导出，自定义字段选择

## 🛠️ 技术栈

- **前端框架**: React 19.1.0 + TypeScript 5.8.3
- **构建工具**: Vite 7.0.4 (快速构建，HMR支持)
- **UI组件库**: Ant Design 5.26.7 (企业级UI设计语言)
- **路由管理**: React Router 6.30.1
- **状态管理**: Zustand 4.5.7 (轻量级状态管理)
- **HTTP客户端**: Axios 1.11.0 (请求拦截、响应处理)
- **WebSocket**: react-use-websocket 4.13.0 (实时通信)
- **时间处理**: Day.js 1.11.13 (轻量级时间库)

## 📦 快速开始

### 环境要求

- **Node.js**: >= 16.0.0 (推荐 18.x 或更高版本)
- **npm**: >= 8.0.0
- **现代浏览器**: Chrome 90+, Firefox 88+, Safari 14+

### 一键启动

**Windows用户**:
```bash
# 双击运行启动脚本
start-frontend.bat
```

**Linux/macOS用户**:
```bash
# 执行启动脚本
./start-frontend.sh
```

启动脚本会自动：
- ✅ 检查Node.js和npm环境
- ✅ 检查端口占用情况
- ✅ 安装项目依赖（如果需要）
- ✅ 启动开发服务器

### 手动安装

```bash
# 1. 安装依赖
npm install

# 2. 启动开发服务器
npm run dev

# 3. 打开浏览器访问
# http://localhost:5173
```

### 生产环境部署

```bash
# 1. 构建生产版本
npm run build

# 2. 预览生产构建
npm run preview

# 3. 部署dist目录到服务器
```

## 🏗️ 项目结构

```
frontend/
├── public/                 # 静态资源
├── src/
│   ├── components/         # 可复用组件
│   ├── pages/             # 页面组件
│   │   ├── Dashboard.tsx  # 数据概览页
│   │   └── Crawler.tsx    # 爬虫控制页
│   ├── services/          # API 和 WebSocket 服务
│   │   ├── api.ts         # REST API 服务
│   │   └── websocket.ts   # WebSocket 服务
│   ├── stores/            # Zustand 状态管理
│   │   └── appStore.ts    # 全局应用状态
│   ├── hooks/             # 自定义 React Hooks
│   │   ├── useApi.ts      # API 调用 Hook
│   │   └── useWebSocket.ts # WebSocket 连接 Hook
│   ├── types/             # TypeScript 类型定义
│   │   └── index.ts       # 公共类型定义
│   ├── utils/             # 工具函数
│   │   └── index.ts       # 通用工具函数
│   ├── App.tsx            # 主应用组件
│   └── main.tsx           # 应用入口
├── package.json
├── tsconfig.json          # TypeScript 配置
├── vite.config.ts         # Vite 配置
└── README.md
```

## 🔧 配置说明

### API 代理配置

在 `vite.config.ts` 中配置了代理，将前端请求转发到后端：

```typescript
server: {
  port: 3000,
  proxy: {
    '/api': {
      target: 'http://localhost:8000',
      changeOrigin: true,
      rewrite: (path) => path.replace(/^\/api/, '')
    },
    '/ws': {
      target: 'ws://localhost:8000',
      ws: true,
      changeOrigin: true
    }
  }
}
```

### 路径别名配置

配置了便捷的路径别名：

- `@/` → `src/`
- `@/components` → `src/components`
- `@/pages` → `src/pages`
- `@/services` → `src/services`
- `@/hooks` → `src/hooks`
- `@/stores` → `src/stores`
- `@/types` → `src/types`
- `@/utils` → `src/utils`
- `@/assets` → `src/assets`

## 🌐 API 接口

前端通过以下接口与后端通信：

### REST API

- `POST /api/crawl/start` - 启动爬虫
- `POST /api/crawl/stop` - 停止爬虫
- `GET /api/crawl/status` - 获取爬虫状态
- `GET /api/products` - 获取产品数据
- `GET /api/statistics` - 获取统计数据
- `GET /api/export/{format}` - 导出数据
- `DELETE /api/data/clear` - 清除数据
- `GET /api/health` - 健康检查

### WebSocket

- 连接地址: `ws://localhost:8000/ws`
- 消息类型:
  - `status` - 爬虫状态更新
  - `progress` - 进度更新
  - `data` - 新产品数据
  - `error` - 错误信息
  - `complete` - 爬取完成

## 📱 页面功能

### 数据概览 (Dashboard)

- 实时统计数据展示
- 热门品牌和分类排行
- 价格分布图表
- 关键指标监控

### 爬虫控制 (Crawler)

- WebSocket 连接状态显示
- 搜索参数配置
- 实时进度监控
- 爬虫启停控制
- 错误信息展示

## 🎨 开发规范

### 代码格式化

```bash
# 格式化代码
npm run format

# 检查格式
npm run format:check
```

### 代码检查

```bash
# 运行 ESLint
npm run lint

# 自动修复
npm run lint:fix

# 类型检查
npm run type-check
```

## 🔍 故障排除

### 常见问题

1. **WebSocket 连接失败**
   - 确保后端服务器正在运行
   - 检查端口是否正确 (默认 8000)
   - 查看浏览器控制台错误信息

2. **API 请求失败**
   - 确认后端 API 服务正常
   - 检查网络连接
   - 查看 Network 面板中的请求详情

3. **页面白屏**
   - 检查浏览器控制台错误
   - 确认所有依赖已正确安装
   - 尝试清除浏览器缓存

### 调试技巧

- 使用 React Developer Tools 调试组件状态
- 使用 Redux DevTools 查看 Zustand 状态变化
- 在浏览器 Application 面板查看 WebSocket 连接状态
- 使用 Network 面板监控 API 请求

## 📝 开发计划

- [ ] 商品数据页面 (表格展示、筛选、排序)
- [ ] 系统设置页面 (配置管理)
- [ ] 数据可视化图表
- [ ] 导出功能优化
- [ ] 移动端适配优化
- [ ] 国际化支持

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

MIT License
