import React, { useEffect } from 'react';
import { Card, Row, Col, Statistic, Space } from 'antd';
import { 
  ShoppingOutlined, 
  DatabaseOutlined, 
  TrophyOutlined, 
  DollarOutlined 
} from '@ant-design/icons';
import { useAppStore } from '@/stores/appStore';
import { useApi } from '@/hooks/useApi';
import { formatPrice, formatCount } from '@/utils';

const Dashboard: React.FC = () => {
  const { statistics, loading } = useAppStore();
  const { fetchStatistics } = useApi();

  useEffect(() => {
    fetchStatistics();
  }, [fetchStatistics]);

  return (
    <div style={{ padding: '24px' }}>
      <h1>数据概览</h1>
      
      {/* 基础统计 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="总商品数"
              value={statistics?.totalProducts || 0}
              prefix={<ShoppingOutlined />}
              formatter={(value) => formatCount(Number(value))}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="总关键词数"
              value={statistics?.totalKeywords || 0}
              prefix={<DatabaseOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="平均价格"
              value={statistics?.avgPrice || 0}
              prefix={<DollarOutlined />}
              formatter={(value) => formatPrice(Number(value))}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="热门品牌数"
              value={statistics?.topBrands?.length || 0}
              prefix={<TrophyOutlined />}
            />
          </Card>
        </Col>
      </Row>

      {/* 详细统计 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} md={12}>
          <Card title="热门品牌" loading={loading.fetching}>
            <Space direction="vertical" style={{ width: '100%' }}>
              {statistics?.topBrands?.slice(0, 10).map((brand, index) => (
                <div key={brand.name} style={{ 
                  display: 'flex', 
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  padding: '8px 0',
                  borderBottom: index < 9 ? '1px solid #f0f0f0' : 'none'
                }}>
                  <span>{brand.name}</span>
                  <span style={{ color: '#1890ff', fontWeight: 'bold' }}>
                    {formatCount(brand.count)}
                  </span>
                </div>
              ))}
            </Space>
          </Card>
        </Col>
        
        <Col xs={24} md={12}>
          <Card title="热门分类" loading={loading.fetching}>
            <Space direction="vertical" style={{ width: '100%' }}>
              {statistics?.topCategories?.slice(0, 10).map((category, index) => (
                <div key={category.name} style={{ 
                  display: 'flex', 
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  padding: '8px 0',
                  borderBottom: index < 9 ? '1px solid #f0f0f0' : 'none'
                }}>
                  <span>{category.name}</span>
                  <span style={{ color: '#52c41a', fontWeight: 'bold' }}>
                    {formatCount(category.count)}
                  </span>
                </div>
              ))}
            </Space>
          </Card>
        </Col>
      </Row>

      {/* 价格分布 */}
      {statistics?.priceDistribution && (
        <Row style={{ marginTop: '24px' }}>
          <Col span={24}>
            <Card title="价格分布">
              <Space direction="vertical" style={{ width: '100%' }}>
                {statistics.priceDistribution.map((item, index) => (
                  <div key={item.range} style={{ 
                    display: 'flex', 
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    padding: '8px 0',
                    borderBottom: index < statistics.priceDistribution.length - 1 ? '1px solid #f0f0f0' : 'none'
                  }}>
                    <span>{item.range}</span>
                    <span style={{ color: '#fa8c16', fontWeight: 'bold' }}>
                      {formatCount(item.count)}
                    </span>
                  </div>
                ))}
              </Space>
            </Card>
          </Col>
        </Row>
      )}
    </div>
  );
};

export default Dashboard;